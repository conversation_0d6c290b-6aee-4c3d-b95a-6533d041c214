#!/bin/bash

# 数据库初始化脚本
# 确保容器启动时正确初始化数据库和执行迁移

set -e

echo "开始数据库初始化..."

# 创建必要的目录
mkdir -p /app/db /app/logs

# 创建日志文件
touch /app/logs/django.log

# 检查数据库文件是否存在
if [ ! -f "/app/db/sqlite.db" ]; then
    echo "数据库文件不存在，将创建新的数据库..."
else
    echo "发现现有数据库文件，将执行迁移..."
fi

# 执行数据库迁移
echo "执行数据库迁移..."
python manage.py migrate

# 检查并创建超级用户
echo "检查超级用户..."
python manage.py shell -c "
from django.contrib.auth import get_user_model
import os

User = get_user_model()
if not User.objects.filter(is_superuser=True).exists():
    print('没有超级用户，正在创建默认admin用户...')

    # 从环境变量获取admin用户信息，如果没有则使用默认值
    admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
    admin_email = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
    admin_password = os.environ.get('ADMIN_PASSWORD', 'admin123')

    # 创建超级用户
    admin_user = User.objects.create_superuser(
        username=admin_username,
        email=admin_email,
        password=admin_password
    )

    print(f'✅ 超级用户创建成功:')
    print(f'   用户名: {admin_username}')
    print(f'   邮箱: {admin_email}')
    print(f'   密码: {admin_password}')
    print('⚠️  请在生产环境中修改默认密码！')
else:
    print('✅ 超级用户已存在')
"

# 收集静态文件（如果需要）
if [ "$COLLECT_STATIC" = "true" ]; then
    echo "收集静态文件..."
    python manage.py collectstatic --noinput
fi

echo "数据库初始化完成！"
