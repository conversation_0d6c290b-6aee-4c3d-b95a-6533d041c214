# Agent Portal Docker 部署指南

本目录包含了Agent Portal项目的Docker化部署配置文件。

## 文件说明

- `docker-compose.yml` - 生产环境Docker Compose配置（支持SQLite数据持久化）
- `docker-compose.dev.yml` - 开发环境Docker Compose配置
- `Dockerfile` - 生产环境Docker镜像构建文件
- `Dockerfile.dev` - 开发环境Docker镜像构建文件
- `build-image.sh` - 镜像构建脚本
- `init-db.sh` - 数据库初始化脚本
- `.env.example` - 环境变量配置示例

## 快速开始

### 1. 开发环境

```bash
# 进入docker目录
cd docker

# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

开发环境特点：
- 使用SQLite数据库
- 启用DEBUG模式
- 代码热重载
- 端口：8000

### 2. 生产环境

```bash
# 复制环境变量配置
cp .env.example .env
# 编辑.env文件，修改相应的配置

# 构建镜像
./build-image.sh --prod

# 启动生产环境
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f web

# 停止服务
docker-compose down
```

生产环境特点：
- 使用SQLite数据库（支持数据持久化）
- 使用Gunicorn WSGI服务器
- 数据库和日志文件持久化存储
- 端口：8000

**注意**: 当前配置使用SQLite数据库以简化部署。如需PostgreSQL，请参考`.env.example`中的配置。

## 构建脚本使用

`build-image.sh` 脚本提供了灵活的镜像构建选项：

```bash
# 构建生产环境镜像
./build-image.sh

# 构建开发环境镜像
./build-image.sh --dev

# 构建并推送到镜像仓库
./build-image.sh --prod --push

# 清理旧镜像后构建
./build-image.sh --clean

# 指定镜像标签
./build-image.sh --tag v1.0.0

# 查看帮助
./build-image.sh --help
```

## 服务说明

### Web服务
- **容器名**: agent-portal-web
- **端口**: 8000
- **功能**: Django Web应用主服务

### PostgreSQL数据库
- **容器名**: agent-portal-postgres
- **端口**: 5432
- **数据库**: agent_portal
- **用户**: agent_portal

### Redis缓存
- **容器名**: agent-portal-redis
- **端口**: 6379
- **功能**: 缓存和Celery消息队列

### Celery Worker
- **容器名**: agent-portal-celery
- **功能**: 异步任务处理

### Nginx反向代理
- **容器名**: agent-portal-nginx
- **端口**: 80, 443
- **功能**: 反向代理、静态文件服务、SSL终止

## 环境变量配置

主要环境变量说明：

```bash
# Django基础配置
DEBUG=False
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=localhost,your-domain.com

# 数据库配置
DATABASE_URL=postgresql://user:pass@host:port/db
POSTGRES_DB=agent_portal
POSTGRES_USER=agent_portal
POSTGRES_PASSWORD=your-password

# Redis配置
REDIS_URL=redis://redis:6379/0

# AI平台配置
RAGFLOW_BASE_URL=http://*********:7080
RAGFLOW_API_KEY=your-ragflow-api-key
DIFY_BASE_URL=https://api.dify.ai/v1
DIFY_API_KEY=your-dify-api-key
```

## 数据持久化

当前配置使用独立的SQLite数据库，确保即使Docker容器更新，数据也能保持：

### SQLite数据持久化
- **数据库文件**: 存储在项目根目录的 `../db/` 目录中
- **日志文件**: 存储在项目根目录的 `../logs/` 目录中
- **数据卷挂载**: 通过Docker Compose自动挂载这些目录

### 目录结构
```
agent-portal/
├── db/                 # 数据库文件目录（持久化）
│   ├── sqlite.db      # 主数据库文件
│   ├── README.md      # 数据库目录说明
│   └── .gitignore     # 忽略数据库文件
├── logs/              # 日志文件目录（持久化）
│   └── django.log     # Django应用日志
└── docker/            # Docker配置目录
```

### 数据库管理
```bash
# 备份数据库
cp ../db/sqlite.db ../db/sqlite.db.backup.$(date +%Y%m%d_%H%M%S)

# 查看数据库文件
ls -la ../db/

# 进入容器操作数据库
docker-compose exec web python manage.py dbshell

# 创建超级用户
docker-compose exec web python manage.py createsuperuser
```

## SSL证书配置

当前项目直接使用Django gunicorn服务器，不使用nginx代理。如需启用HTTPS，建议：

1. 使用反向代理服务器（如nginx、Apache或云服务商的负载均衡器）
2. 在反向代理层配置SSL证书
3. 更新环境变量中的安全配置

## 监控和日志

```bash
# 查看所有服务状态
docker-compose ps

# 查看特定服务日志
docker-compose logs -f web
docker-compose logs -f postgres
docker-compose logs -f redis

# 进入容器调试
docker-compose exec web bash
docker-compose exec postgres psql -U agent_portal -d agent_portal

# 查看资源使用情况
docker stats
```

## 备份和恢复

### 数据库备份
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U agent_portal agent_portal > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U agent_portal agent_portal < backup.sql
```

### 完整备份
```bash
# 备份所有数据卷
docker run --rm -v agent-portal_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .
docker run --rm -v agent-portal_redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis_backup.tar.gz -C /data .
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8000
   # 修改docker-compose.yml中的端口映射
   ```

2. **权限问题**
   ```bash
   # 确保构建脚本可执行
   chmod +x build-image.sh
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库服务状态
   docker-compose logs postgres
   # 检查环境变量配置
   ```

4. **静态文件404**
   ```bash
   # 重新收集静态文件
   docker-compose exec web python manage.py collectstatic --noinput
   ```

## 性能优化

1. **调整Worker数量**
   - 修改Gunicorn workers数量
   - 调整Celery worker并发数

2. **数据库优化**
   - 配置PostgreSQL参数
   - 启用连接池

3. **缓存优化**
   - 配置Redis内存限制
   - 启用Redis持久化

## 安全建议

1. 修改默认密码和密钥
2. 限制数据库访问权限
3. 配置防火墙规则
4. 定期更新镜像
5. 启用SSL/TLS
6. 配置安全头

更多详细信息请参考项目主README文件。
