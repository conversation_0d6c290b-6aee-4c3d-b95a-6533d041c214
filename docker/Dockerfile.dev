# 开发环境Dockerfile
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
    git \
    vim \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY app/requirements.txt /app/

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 安装开发工具
RUN pip install --no-cache-dir \
    ipython \
    django-extensions \
    watchdog

# 复制应用代码
COPY app/ /app/

# 暴露端口
EXPOSE 8000

# 默认命令（开发模式）
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
