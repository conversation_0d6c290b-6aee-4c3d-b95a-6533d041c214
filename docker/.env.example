# Agent Portal Docker环境变量配置
# 复制此文件为.env并修改相应的值

# Django配置
DEBUG=False
SECRET_KEY=your-very-secret-key-here-change-in-production-min-50-chars
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# 数据库配置
DATABASE_URL=*************************************************************/agent_portal
POSTGRES_DB=agent_portal
POSTGRES_USER=agent_portal
POSTGRES_PASSWORD=agent_portal_password

# Redis配置
REDIS_URL=redis://redis:6379/0

# 邮件配置（可选）
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# 静态文件配置
STATIC_URL=/static/
MEDIA_URL=/media/

# 时区配置
TIME_ZONE=Asia/Shanghai

# 日志级别
LOG_LEVEL=INFO

# 安全配置
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY

# Celery配置
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# AI平台配置（根据需要配置）
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# RagFlow配置
RAGFLOW_BASE_URL=http://*********:7080
RAGFLOW_API_KEY=ragflow-NkYzE0OTk4MzU3NTExZjA5ZmMwMDI0Mm

# Dify配置
DIFY_BASE_URL=https://api.dify.ai/v1
DIFY_API_KEY=your-dify-api-key

# 监控配置（可选）
SENTRY_DSN=your-sentry-dsn-here

# 备份配置
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点备份
