version: '3.8'

services:
  # Django Web应用
  web:
    image: agent-portal:latest
    container_name: agent-portal-web
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      # 挂载数据库目录以确保数据持久化
      - ../db:/app/db
      # 挂载日志目录
      - ../logs:/app/logs
    environment:
      - DEBUG=False
      - SECRET_KEY=your-secret-key-here-change-in-production
      - ALLOWED_HOSTS=*
      # Admin用户配置（首次启动时创建）
      - ADMIN_USERNAME=admin
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=admin123
    command: >
      sh -c "/app/init-db.sh &&
             python /app/manage.py runserver 0.0.0.0:8000"

#             gunicorn agent_portal.wsgi:application --bind 0.0.0.0:8000 --workers 3"
