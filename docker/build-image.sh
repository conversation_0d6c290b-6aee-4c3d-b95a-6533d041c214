#!/bin/bash

# Agent Portal Docker构建脚本
# 用法: ./build-image.sh [选项]
# 选项:
#   --dev     构建开发环境镜像
#   --prod    构建生产环境镜像（默认）
#   --push    构建后推送到镜像仓库
#   --clean   构建前清理旧镜像

set -e

# 默认配置
IMAGE_NAME="agent-portal"
IMAGE_TAG="latest"
DOCKERFILE="docker/Dockerfile"
BUILD_CONTEXT="."
PUSH_IMAGE=false
CLEAN_IMAGES=false
BUILD_ENV="prod"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Agent Portal Docker构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --dev         构建开发环境镜像"
    echo "  --prod        构建生产环境镜像（默认）"
    echo "  --push        构建后推送到镜像仓库"
    echo "  --clean       构建前清理旧镜像"
    echo "  --tag TAG     指定镜像标签（默认: latest）"
    echo "  --name NAME   指定镜像名称（默认: agent-portal）"
    echo "  -h, --help    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 构建生产环境镜像"
    echo "  $0 --dev             # 构建开发环境镜像"
    echo "  $0 --prod --push     # 构建生产环境镜像并推送"
    echo "  $0 --clean --tag v1.0 # 清理后构建标签为v1.0的镜像"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --dev)
            BUILD_ENV="dev"
            IMAGE_TAG="dev"
            shift
            ;;
        --prod)
            BUILD_ENV="prod"
            shift
            ;;
        --push)
            PUSH_IMAGE=true
            shift
            ;;
        --clean)
            CLEAN_IMAGES=true
            shift
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    log_error "Docker未运行，请启动Docker服务"
    exit 1
fi

# 切换到项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

log_info "项目根目录: $PROJECT_ROOT"
log_info "构建环境: $BUILD_ENV"
log_info "镜像名称: $IMAGE_NAME:$IMAGE_TAG"

# 清理旧镜像
if [ "$CLEAN_IMAGES" = true ]; then
    log_info "清理旧镜像..."
    docker rmi "$IMAGE_NAME:$IMAGE_TAG" 2>/dev/null || true
    docker system prune -f
    log_success "镜像清理完成"
fi

# 构建镜像
log_info "开始构建Docker镜像..."

BUILD_ARGS=""
if [ "$BUILD_ENV" = "dev" ]; then
    BUILD_ARGS="--build-arg DEBUG=True"
fi

docker build \
    $BUILD_ARGS \
    -t "$IMAGE_NAME:$IMAGE_TAG" \
    -f "$DOCKERFILE" \
    "$BUILD_CONTEXT"

if [ $? -eq 0 ]; then
    log_success "镜像构建成功: $IMAGE_NAME:$IMAGE_TAG"
else
    log_error "镜像构建失败"
    exit 1
fi

# 显示镜像信息
log_info "镜像信息:"
docker images "$IMAGE_NAME:$IMAGE_TAG"

# 推送镜像
if [ "$PUSH_IMAGE" = true ]; then
    log_info "推送镜像到仓库..."
    docker push "$IMAGE_NAME:$IMAGE_TAG"
    if [ $? -eq 0 ]; then
        log_success "镜像推送成功"
    else
        log_error "镜像推送失败"
        exit 1
    fi
fi

log_success "构建完成！"
log_info "运行容器: docker run -p 8000:8000 $IMAGE_NAME:$IMAGE_TAG"
log_info "使用Docker Compose: cd docker && docker-compose up -d"
