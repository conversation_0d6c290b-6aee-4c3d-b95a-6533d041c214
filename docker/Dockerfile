# 使用Python 3.11官方镜像
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 更换为阿里云debian源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY app/requirements.txt /app/

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制应用代码（包括所有文件和目录）
COPY app/ /app/

# 复制初始化脚本
COPY docker/init-db.sh /app/init-db.sh
RUN chmod +x /app/init-db.sh

# 创建必要的目录
RUN mkdir -p /app/logs /app/db

# 暴露端口
EXPOSE 8000

# 默认命令
CMD ["sh", "-c", "touch /app/logs/django.log && gunicorn agent_portal.wsgi:application --bind 0.0.0.0:8000 --workers 4"]
