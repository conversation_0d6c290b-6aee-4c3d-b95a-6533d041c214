#!/usr/bin/env python3
"""
RagFlow Authorization Token Manager

This script automatically obtains and refreshes RagFlow Authorization tokens
by logging in with account credentials. The KB API requires Authorization tokens
rather than API keys for access.

Usage:
    python ragflow_auth.py --email <EMAIL> --password mypassword --url http://ragflow-server:7080

The script can be run periodically (e.g., every 12 hours) to refresh tokens.
"""

import argparse
import base64
import json
import logging
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Tuple

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5


class RagFlowAuthManager:
    """Manages RagFlow authentication and token generation."""
    
    def __init__(self, base_url: str, email: str, password: str, timeout: int = 30):
        """
        Initialize the auth manager.
        
        Args:
            base_url: RagFlow server base URL (e.g., http://localhost:7080)
            email: User email for login
            password: User password (will be encrypted by <PERSON>g<PERSON><PERSON>)
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.email = email
        self.password = password
        self.timeout = timeout
        
        # Setup session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def encrypt_password(self, password: str) -> str:
        """
        Encrypt password using RagFlow's RSA encryption method.
        This matches the frontend encryption in web/src/utils/index.ts
        """
        try:
            # RagFlow's public key (from frontend)
            public_key_pem = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArq9XTUSeYr2+N1h3Afl/z8Dse/2yD0ZGrKwx+EEEcdsBLca9Ynmx3nIB5obmLlSfmskLpBo0UACBmB5rEjBp2Q2f3AG3Hjd4B+gNCG6BDaawuDlgANIhGnaTLrIqWrrcm4EMzJOnAOI1fgzJRsOOUEfaS318Eq9OVO3apEyCCt0lOQK6PuksduOjVxtltDav+guVAA068NrPYmRNabVKRNLJpL8w4D44sfth5RvZ3q9t+6RTArpEtc5sh5ChzvqPOzKGMXW83C95TxmXqpbK6olN4RevSfVjEAgCydH6HN6OhtOQEcnrU97r9H0iZOWwbw3pVrZiUkuRD1R56Wzs2wIDAQAB
-----END PUBLIC KEY-----"""

            # Import the public key
            rsa_key = RSA.importKey(public_key_pem)
            cipher = PKCS1_v1_5.new(rsa_key)

            # Base64 encode the password first (like frontend does)
            password_b64 = base64.b64encode(password.encode('utf-8')).decode('utf-8')

            # Encrypt the base64 encoded password
            encrypted = cipher.encrypt(password_b64.encode('utf-8'))

            # Base64 encode the encrypted result
            return base64.b64encode(encrypted).decode('utf-8')

        except Exception as e:
            self.logger.error(f"Password encryption failed: {str(e)}")
            raise
    
    def login(self) -> Tuple[bool, Optional[str]]:
        """
        Login to RagFlow and get Authorization token.
        
        Returns:
            Tuple of (success, authorization_token)
        """
        try:
            login_url = f"{self.base_url}/v1/user/login"
            
            # Encrypt password (placeholder implementation)
            encrypted_password = self.encrypt_password(self.password)
            
            login_data = {
                "email": self.email,
                "password": encrypted_password
            }
            
            self.logger.info(f"Attempting login for {self.email}")
            
            response = self.session.post(
                login_url,
                json=login_data,
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                self.logger.error(f"Login failed with status {response.status_code}")
                return False, None
            
            result = response.json()
            
            if result.get("code") != 0:
                self.logger.error(f"Login failed: {result.get('message')}")
                return False, None
            
            # Get Authorization token from response headers
            auth_token = response.headers.get("Authorization")
            if not auth_token:
                self.logger.error("No Authorization header in login response")
                return False, None
            
            self.logger.info("Login successful")
            return True, auth_token
            
        except Exception as e:
            self.logger.error(f"Login error: {str(e)}")
            return False, None
    

    
    def get_authorization_token(self) -> Optional[str]:
        """
        Get Authorization token for KB API access.

        Returns:
            Authorization token or None if failed
        """
        # Login to get Authorization token
        success, auth_token = self.login()
        if not success:
            return None

        return auth_token
    
    def save_token_to_file(self, auth_token: str, file_path: str):
        """Save Authorization token to a JSON file with timestamp."""
        token_data = {
            "timestamp": datetime.now().isoformat(),
            "expires_at": (datetime.now() + timedelta(hours=12)).isoformat(),
            "authorization": auth_token
        }

        try:
            with open(file_path, 'w') as f:
                json.dump(token_data, f, indent=2)
            self.logger.info(f"Authorization token saved to {file_path}")
        except Exception as e:
            self.logger.error(f"Failed to save token: {str(e)}")

    def load_token_from_file(self, file_path: str) -> Optional[str]:
        """Load Authorization token from JSON file if it's still valid."""
        try:
            if not os.path.exists(file_path):
                return None

            with open(file_path, 'r') as f:
                token_data = json.load(f)

            expires_at = datetime.fromisoformat(token_data.get("expires_at", ""))
            if datetime.now() >= expires_at:
                self.logger.info("Stored token has expired")
                return None

            self.logger.info("Loaded valid token from file")
            return token_data.get("authorization")

        except Exception as e:
            self.logger.error(f"Failed to load token: {str(e)}")
            return None


def main():
    """Main function to handle command line arguments and run the auth manager."""
    parser = argparse.ArgumentParser(description="RagFlow Authorization Token Manager")
    parser.add_argument("--email", required=True, help="RagFlow user email")
    parser.add_argument("--password", required=True, help="RagFlow user password")
    parser.add_argument("--url", required=True, help="RagFlow server URL")
    parser.add_argument("--output", default="ragflow_auth_token.json", help="Output file for token")
    parser.add_argument("--force-refresh", action="store_true", help="Force token refresh")

    args = parser.parse_args()

    # Initialize auth manager
    auth_manager = RagFlowAuthManager(args.url, args.email, args.password)

    # Check if we have valid cached token
    if not args.force_refresh:
        cached_token = auth_manager.load_token_from_file(args.output)
        if cached_token:
            result = {"authorization": cached_token}
            print(json.dumps(result, indent=2))
            return

    # Get fresh Authorization token
    auth_token = auth_manager.get_authorization_token()

    if auth_token is None:
        print("Failed to obtain Authorization token", file=sys.stderr)
        sys.exit(1)

    # Save token to file
    auth_manager.save_token_to_file(auth_token, args.output)

    # Output token
    result = {"authorization": auth_token}
    print(json.dumps(result, indent=2))


if __name__ == "__main__":
    main()
