#!/usr/bin/env python3
"""
Dify Knowledge Base Document Uploader

This script automatically uploads markdown files from a specified directory
to a Dify knowledge base, with duplicate detection to avoid re-uploading
existing files.

Usage:
    python dify_uploader.py --dataset-id <DATASET_ID> --input-dir <INPUT_DIRECTORY> --api-key <API_KEY>
    python dify_uploader.py --dataset-id 5beea32b-3d3b-4c99-840a-956fe2430941 --input-dir output --api-key your_api_key

Features:
- Scans directory for markdown files (*.md)
- Checks for existing documents to avoid duplicates
- Supports incremental uploads
- Detailed logging and progress reporting
"""

import argparse
import json
import logging
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class DifyDocumentUploader:
    """Manages document uploads to Dify knowledge base."""

    def __init__(self, base_url: str, dataset_id: str, api_key: str, timeout: int = 30):
        """
        Initialize the Dify document uploader.

        Args:
            base_url: Dify server base URL
            dataset_id: Dataset ID to upload to
            api_key: Dify API key
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.dataset_id = dataset_id
        self.api_key = api_key
        self.timeout = timeout
        
        # Setup session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authorization headers for API requests."""
        return {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def get_existing_documents(self) -> Set[str]:
        """
        Get list of existing document names in the dataset.

        Returns:
            Set of existing document names
        """
        headers = self.get_auth_headers()

        try:
            # Use Dify's Get document list API
            url = f"{self.base_url}/api/v1/datasets/{self.dataset_id}/documents"
            params = {
                'page': 1,
                'limit': 100  # Get documents in batches
            }

            self.logger.info(f"Fetching existing documents from dataset {self.dataset_id}")
            self.logger.info(f"Request URL: {url}")

            existing_names = set()
            page = 1
            
            while True:
                params['page'] = page
                response = self.session.get(url, headers=headers, params=params, timeout=self.timeout)

                self.logger.info(f"Response status: {response.status_code}")

                if response.status_code != 200:
                    self.logger.error(f"Failed to fetch documents: HTTP {response.status_code}")
                    self.logger.error(f"Response: {response.text}")
                    break

                result = response.json()
                self.logger.info(f"API response for page {page}: {result}")

                # Extract document names from the response
                # Dify API returns: {"data": [...], "has_more": bool, "limit": N, "total": N, "page": N}
                documents = result.get("data", [])
                
                self.logger.info(f"Found {len(documents)} documents on page {page}")

                for doc in documents:
                    if isinstance(doc, dict):
                        # Dify API returns document name in 'name' field
                        name = doc.get("name", "")
                        if name:
                            existing_names.add(name)
                            self.logger.info(f"Found existing document: {name}")
                        else:
                            self.logger.warning(f"Document missing name field: {doc}")
                    else:
                        self.logger.warning(f"Document is not dict: {type(doc)} - {doc}")

                # Check if there are more pages
                if not result.get("has_more", False):
                    break
                    
                page += 1

            self.logger.info(f"Extracted {len(existing_names)} existing document names")
            if existing_names:
                self.logger.info(f"Existing document names: {sorted(list(existing_names))}")

            return existing_names

        except Exception as e:
            self.logger.error(f"Error fetching existing documents: {str(e)}")
            return set()
    
    def upload_document(self, file_path: Path) -> bool:
        """
        Upload a single document to the dataset.
        
        Args:
            file_path: Path to the markdown file
            
        Returns:
            True if upload successful, False otherwise
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}'
            # Don't set Content-Type for multipart upload
        }
        
        try:
            # Prepare multipart form data for Dify API
            url = f"{self.base_url}/api/v1/datasets/{self.dataset_id}/document/create-by-file"
            
            # Dify API requires specific data format
            data_payload = {
                "indexing_technique": "high_quality",
                "process_rule": {
                    "mode": "automatic"
                }
            }
            
            with open(file_path, 'rb') as f:
                files = {
                    'file': (file_path.name, f, 'text/markdown')
                }
                data = {
                    'data': (None, json.dumps(data_payload), 'application/json')
                }
                
                self.logger.info(f"Uploading {file_path.name} to Dify dataset")
                response = self.session.post(
                    url, 
                    headers=headers, 
                    files=files,
                    data=data,
                    timeout=self.timeout
                )
            
            self.logger.info(f"Upload response status: {response.status_code}")
            self.logger.info(f"Upload response: {response.text}")
            
            if response.status_code != 200:
                self.logger.error(f"Upload failed for {file_path.name}: HTTP {response.status_code}")
                return False
            
            result = response.json()
            
            # Check if upload was successful
            if "document" in result:
                self.logger.info(f"Successfully uploaded {file_path.name}")
                return True
            else:
                self.logger.error(f"Upload failed for {file_path.name}: {result}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error uploading {file_path.name}: {str(e)}")
            return False
    
    def scan_directory(self, input_dir: Path) -> List[Path]:
        """
        Scan directory for markdown files.
        
        Args:
            input_dir: Directory to scan
            
        Returns:
            List of markdown file paths
        """
        if not input_dir.exists():
            self.logger.error(f"Input directory does not exist: {input_dir}")
            return []
        
        if not input_dir.is_dir():
            self.logger.error(f"Input path is not a directory: {input_dir}")
            return []
        
        # Find all .md files
        md_files = list(input_dir.glob("*.md"))
        
        # Sort by filename for consistent processing order
        md_files.sort()
        
        self.logger.info(f"Found {len(md_files)} markdown files in {input_dir}")
        return md_files
    
    def process_directory(self, input_dir: Path) -> Tuple[int, int]:
        """
        Process all markdown files in the directory.
        
        Args:
            input_dir: Directory containing markdown files
            
        Returns:
            Tuple of (successful_uploads, total_files)
        """
        # Scan for markdown files
        md_files = self.scan_directory(input_dir)
        if not md_files:
            return 0, 0
        
        # Get existing documents
        existing_docs = self.get_existing_documents()
        
        # Filter out files that already exist
        files_to_upload = []
        for file_path in md_files:
            if file_path.name in existing_docs:
                self.logger.info(f"Skipping {file_path.name} (already exists)")
            else:
                files_to_upload.append(file_path)
        
        if not files_to_upload:
            self.logger.info("No new files to upload")
            return 0, len(md_files)
        
        self.logger.info(f"Uploading {len(files_to_upload)} new files")
        
        # Upload files
        successful_uploads = 0
        for file_path in files_to_upload:
            if self.upload_document(file_path):
                successful_uploads += 1
            
            # Small delay between uploads to avoid overwhelming the server
            time.sleep(0.5)
        
        return successful_uploads, len(md_files)


def main():
    """Main function to handle command line arguments and run the uploader."""
    parser = argparse.ArgumentParser(description="Dify Knowledge Base Document Uploader")
    parser.add_argument("--dataset-id", required=True, help="Dify dataset ID")
    parser.add_argument("--input-dir", required=True, help="Input directory containing markdown files")
    parser.add_argument("--api-url", default="http://************", help="Dify server URL")
    parser.add_argument("--api-key", required=True, help="Dify API key")

    args = parser.parse_args()

    # Initialize uploader
    uploader = DifyDocumentUploader(args.api_url, args.dataset_id, args.api_key)
    
    # Process directory
    input_dir = Path(args.input_dir)
    successful, total = uploader.process_directory(input_dir)
    
    print(f"\nUpload Summary:")
    print(f"Total files found: {total}")
    print(f"Successfully uploaded: {successful}")
    print(f"Skipped (already exist): {total - successful}")
    
    if successful > 0:
        print(f"\n✅ Successfully uploaded {successful} new documents to Dify dataset {args.dataset_id}")
    else:
        print(f"\n📝 No new documents to upload")


if __name__ == "__main__":
    main()
