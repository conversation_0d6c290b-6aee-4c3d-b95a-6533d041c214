#!/usr/bin/env python3
"""
RagFlow Knowledge Base Document Uploader

This script automatically uploads markdown files from a specified directory
to a RagFlow knowledge base, with duplicate detection to avoid re-uploading
existing files, and optional deletion of all existing documents.

Usage:
    # Incremental upload (skip existing files)
    python update_kb.py --kb-id <KNOWLEDGE_BASE_ID> --input-dir <INPUT_DIRECTORY>
    python update_kb.py --kb-id 340f56c64da611f084300242ac130006 --input-dir output

    # Delete all existing documents and re-upload everything
    python update_kb.py --kb-id <KNOWLEDGE_BASE_ID> --input-dir <INPUT_DIRECTORY> --delete
    python update_kb.py --kb-id 340f56c64da611f084300242ac130006 --input-dir output --delete

Features:
- Scans directory for markdown files (*.md)
- Checks for existing documents to avoid duplicates
- Supports incremental uploads (default mode)
- Supports full replacement mode with --delete flag
- Detailed logging and progress reporting
- Automatic document parsing after upload
- Interactive confirmation for delete operations
"""

import argparse
import json
import logging
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Import the existing auth manager (only needed for username/password auth)
try:
    from ragflow_auth import RagFlowAuthManager
except ImportError:
    RagFlowAuthManager = None


class RagFlowDocumentUploader:
    """Manages document uploads to RagFlow knowledge base."""

    def __init__(self, base_url: str, kb_id: str, auth_manager: RagFlowAuthManager = None, api_key: str = None, timeout: int = 30):
        """
        Initialize the document uploader.

        Args:
            base_url: RagFlow server base URL
            kb_id: Knowledge base ID to upload to
            auth_manager: Initialized RagFlowAuthManager instance (optional if api_key provided)
            api_key: RagFlow API key (alternative to auth_manager)
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.kb_id = kb_id
        self.auth_manager = auth_manager
        self.api_key = api_key
        self.timeout = timeout
        
        # Setup session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def get_auth_headers(self) -> Optional[Dict[str, str]]:
        """Get authorization headers for API requests."""
        if self.api_key:
            # Use API key authentication
            return {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
        elif self.auth_manager:
            # Use username/password authentication
            auth_token = self.auth_manager.get_authorization_token()
            if not auth_token:
                self.logger.error("Failed to get authorization token")
                return None

            return {
                'Authorization': auth_token,
                'Content-Type': 'application/json'
            }
        else:
            self.logger.error("No authentication method provided (api_key or auth_manager)")
            return None
    
    def get_existing_documents(self) -> Tuple[Set[str], List[Dict]]:
        """
        Get list of existing document names and full document info in the knowledge base.

        Returns:
            Tuple of (Set of existing document names, List of full document objects)
        """
        headers = self.get_auth_headers()
        if not headers:
            return set(), []

        try:
            # Use RagFlow's List documents API
            url = f"{self.base_url}/api/v1/datasets/{self.kb_id}/documents"
            params = {
                'page': 1,
                'page_size': 1000,  # Get all documents at once
                'orderby': 'create_time',
                'desc': True
            }

            self.logger.info(f"Fetching existing documents from KB {self.kb_id}")
            self.logger.info(f"Request URL: {url}")
            self.logger.info(f"Request params: {params}")

            response = self.session.get(url, headers=headers, params=params, timeout=self.timeout)

            self.logger.info(f"Response status: {response.status_code}")

            if response.status_code != 200:
                self.logger.error(f"Failed to fetch documents: HTTP {response.status_code}")
                self.logger.error(f"Response: {response.text}")
                return set(), []

            result = response.json()
            self.logger.info(f"API response: {result}")

            if result.get("code") != 0:
                self.logger.error(f"API error: {result.get('message')}")
                return set(), []

            # Extract document names from the response
            # RagFlow API returns: {"code": 0, "data": {"docs": [...], "total": N}}
            data = result.get("data", {})
            documents = data.get("docs", []) if isinstance(data, dict) else []
            existing_names = set()

            self.logger.info(f"Found {len(documents)} documents in knowledge base")

            for doc in documents:
                if isinstance(doc, dict):
                    # RagFlow API returns document name in 'name' field
                    name = doc.get("name", "")
                    if name:
                        existing_names.add(name)
                        self.logger.info(f"Found existing document: {name}")
                    else:
                        self.logger.warning(f"Document missing name field: {doc}")
                else:
                    self.logger.warning(f"Document is not dict: {type(doc)} - {doc}")

            self.logger.info(f"Extracted {len(existing_names)} existing document names")
            if existing_names:
                self.logger.info(f"Existing document names: {sorted(list(existing_names))}")

            return existing_names, documents

        except Exception as e:
            self.logger.error(f"Error fetching existing documents: {str(e)}")
            return set(), []

    def delete_all_documents(self) -> bool:
        """
        Delete all documents in the knowledge base.

        Returns:
            True if deletion successful, False otherwise
        """
        headers = self.get_auth_headers()
        if not headers:
            return False

        try:
            # Get all existing documents first
            existing_names, documents = self.get_existing_documents()

            if not documents:
                self.logger.info("No documents to delete")
                return True

            # Extract document IDs
            doc_ids = []
            for doc in documents:
                if isinstance(doc, dict) and "id" in doc:
                    doc_ids.append(doc["id"])

            if not doc_ids:
                self.logger.warning("No document IDs found to delete")
                return True

            self.logger.info(f"Deleting {len(doc_ids)} documents from knowledge base")

            # Use RagFlow's Delete documents API
            url = f"{self.base_url}/api/v1/datasets/{self.kb_id}/documents"
            data = {
                "ids": doc_ids
            }

            self.logger.info(f"Delete URL: {url}")
            self.logger.info(f"Delete data: {data}")

            response = self.session.delete(url, headers=headers, json=data, timeout=self.timeout)

            self.logger.info(f"Delete response status: {response.status_code}")

            if response.status_code != 200:
                self.logger.error(f"Failed to delete documents: HTTP {response.status_code}")
                self.logger.error(f"Response: {response.text}")
                return False

            result = response.json()
            self.logger.info(f"Delete response: {result}")

            if result.get("code") != 0:
                self.logger.error(f"Delete API error: {result.get('message')}")
                return False

            self.logger.info(f"✅ Successfully deleted {len(doc_ids)} documents from knowledge base")
            return True

        except Exception as e:
            self.logger.error(f"Error deleting documents: {str(e)}")
            return False

    def upload_document(self, file_path: Path) -> bool:
        """
        Upload a single document to the knowledge base.
        
        Args:
            file_path: Path to the markdown file
            
        Returns:
            True if upload successful, False otherwise
        """
        headers = self.get_auth_headers()
        if not headers:
            return False
        
        try:
            # Prepare multipart form data
            url = f"{self.base_url}/api/v1/datasets/{self.kb_id}/documents"
            
            with open(file_path, 'rb') as f:
                files = {
                    'file': (file_path.name, f, 'text/markdown')
                }
                
                # Remove Content-Type from headers for multipart upload
                upload_headers = {k: v for k, v in headers.items() if k != 'Content-Type'}
                
                self.logger.info(f"Uploading {file_path.name}")
                response = self.session.post(
                    url, 
                    headers=upload_headers, 
                    files=files, 
                    timeout=self.timeout
                )
            
            if response.status_code != 200:
                self.logger.error(f"Upload failed for {file_path.name}: HTTP {response.status_code}")
                return False
            
            result = response.json()
            if result.get("code") != 0:
                self.logger.error(f"Upload failed for {file_path.name}: {result.get('message')}")
                return False
            
            self.logger.info(f"Successfully uploaded {file_path.name}")
            
            # Trigger document parsing
            documents = result.get("data", [])
            if documents:
                doc_id = documents[0].get("id")
                if doc_id:
                    self.trigger_parsing(doc_id)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error uploading {file_path.name}: {str(e)}")
            return False
    
    def trigger_parsing(self, doc_id: str) -> bool:
        """
        Trigger parsing for an uploaded document using RagFlow's Parse documents API.

        Args:
            doc_id: Document ID to parse

        Returns:
            True if parsing triggered successfully
        """
        headers = self.get_auth_headers()
        if not headers:
            return False

        try:
            # Use RagFlow's Parse documents API: POST /api/v1/datasets/{dataset_id}/chunks
            url = f"{self.base_url}/api/v1/datasets/{self.kb_id}/chunks"

            # Request body with document IDs to parse
            data = {
                "document_ids": [doc_id]
            }

            self.logger.info(f"Triggering parsing for document {doc_id}")
            self.logger.info(f"Parse URL: {url}")
            self.logger.info(f"Parse data: {data}")

            response = self.session.post(url, headers=headers, json=data, timeout=self.timeout)

            self.logger.info(f"Parse response status: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"Parse response: {result}")

                if result.get("code") == 0:
                    self.logger.info(f"✅ Parsing triggered successfully for document {doc_id}")
                    return True
                else:
                    self.logger.warning(f"Parse API error: {result.get('message')}")
                    return False
            else:
                self.logger.warning(f"Parse request failed: HTTP {response.status_code}")
                self.logger.warning(f"Response: {response.text}")
                return False

        except Exception as e:
            self.logger.error(f"Error triggering parsing for {doc_id}: {str(e)}")
            return False
    
    def scan_directory(self, input_dir: Path) -> List[Path]:
        """
        Scan directory for markdown files.
        
        Args:
            input_dir: Directory to scan
            
        Returns:
            List of markdown file paths
        """
        if not input_dir.exists():
            self.logger.error(f"Input directory does not exist: {input_dir}")
            return []
        
        if not input_dir.is_dir():
            self.logger.error(f"Input path is not a directory: {input_dir}")
            return []
        
        # Find all .md files
        md_files = list(input_dir.glob("*.md"))
        
        # Sort by filename for consistent processing order
        md_files.sort()
        
        self.logger.info(f"Found {len(md_files)} markdown files in {input_dir}")
        return md_files
    
    def process_directory(self, input_dir: Path, delete_first: bool = False) -> Tuple[int, int]:
        """
        Process all markdown files in the directory.

        Args:
            input_dir: Directory containing markdown files
            delete_first: Whether to delete all existing documents first

        Returns:
            Tuple of (successful_uploads, total_files)
        """
        # Delete all existing documents if requested
        if delete_first:
            self.logger.info("🗑️  Deleting all existing documents first...")
            if not self.delete_all_documents():
                self.logger.error("Failed to delete existing documents")
                return 0, 0
            self.logger.info("✅ All existing documents deleted successfully")

        # Scan for markdown files
        md_files = self.scan_directory(input_dir)
        if not md_files:
            return 0, 0

        if delete_first:
            # If we deleted everything, upload all files
            files_to_upload = md_files
            self.logger.info(f"Uploading all {len(files_to_upload)} files (after deletion)")
        else:
            # Get existing documents and filter
            existing_docs, _ = self.get_existing_documents()

            # Filter out files that already exist
            files_to_upload = []
            for file_path in md_files:
                if file_path.name in existing_docs:
                    self.logger.info(f"Skipping {file_path.name} (already exists)")
                else:
                    files_to_upload.append(file_path)

            if not files_to_upload:
                self.logger.info("No new files to upload")
                return 0, len(md_files)

            self.logger.info(f"Uploading {len(files_to_upload)} new files")

        # Upload files
        successful_uploads = 0
        for file_path in files_to_upload:
            if self.upload_document(file_path):
                successful_uploads += 1

            # Small delay between uploads to avoid overwhelming the server
            time.sleep(0.5)

        return successful_uploads, len(md_files)


def main():
    """Main function to handle command line arguments and run the uploader."""
    parser = argparse.ArgumentParser(description="RagFlow Knowledge Base Document Uploader")
    parser.add_argument("--kb-id", required=True, help="RagFlow knowledge base ID")
    parser.add_argument("--input-dir", required=True, help="Input directory containing markdown files")
    parser.add_argument("--url", default="http://*********:7080", help="RagFlow server URL")
    parser.add_argument("--api-key", help="RagFlow API key (alternative to email/password)")
    parser.add_argument("--email", help="RagFlow user email (if not provided, will try to read from environment)")
    parser.add_argument("--password", help="RagFlow user password (if not provided, will try to read from environment)")
    parser.add_argument("--auth-file", default="ragflow_tokens.json", help="Auth token cache file")
    parser.add_argument("--delete", action="store_true", help="Delete all existing documents before uploading new ones")

    args = parser.parse_args()

    # Initialize uploader based on authentication method
    if args.api_key:
        # Use API key authentication
        uploader = RagFlowDocumentUploader(args.url, args.kb_id, api_key=args.api_key)
    else:
        # Use email/password authentication
        if RagFlowAuthManager is None:
            print("Error: ragflow_auth.py not found. For username/password authentication, please ensure ragflow_auth.py is in the same directory.")
            print("Alternatively, use --api-key for API key authentication.")
            sys.exit(1)

        email = args.email or os.getenv("RAGFLOW_EMAIL")
        password = args.password or os.getenv("RAGFLOW_PASSWORD")

        if not email or not password:
            print("Error: Either --api-key or email/password are required.")
            print("Provide email/password via --email/--password or RAGFLOW_EMAIL/RAGFLOW_PASSWORD environment variables.")
            sys.exit(1)

        # Initialize auth manager
        auth_manager = RagFlowAuthManager(args.url, email, password)
        uploader = RagFlowDocumentUploader(args.url, args.kb_id, auth_manager=auth_manager)
    
    # Process directory
    input_dir = Path(args.input_dir)

    if args.delete:
        print(f"\n⚠️  WARNING: --delete flag is set. This will DELETE ALL existing documents in knowledge base {args.kb_id}")
        print("Are you sure you want to continue? (y/N): ", end="")
        confirmation = input().strip().lower()
        if confirmation not in ['y', 'yes']:
            print("Operation cancelled.")
            return

    successful, total = uploader.process_directory(input_dir, delete_first=args.delete)

    print(f"\nUpload Summary:")
    print(f"Total files found: {total}")
    print(f"Successfully uploaded: {successful}")

    if args.delete:
        print(f"Mode: Delete and re-upload all files")
    else:
        print(f"Skipped (already exist): {total - successful}")
        print(f"Mode: Incremental upload (skip existing)")

    if successful > 0:
        print(f"\n✅ Successfully uploaded {successful} documents to knowledge base {args.kb_id}")
    else:
        if args.delete:
            print(f"\n❌ No documents were uploaded after deletion")
        else:
            print(f"\n📝 No new documents to upload")


if __name__ == "__main__":
    main()
