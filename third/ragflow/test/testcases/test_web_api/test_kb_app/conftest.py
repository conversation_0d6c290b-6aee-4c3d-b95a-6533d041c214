#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import pytest
from common import batch_create_datasets
from libs.auth import RAGFlowWebApiAuth
from pytest import FixtureRequest
from ragflow_sdk import RAGFlow


@pytest.fixture(scope="class")
def add_datasets(request: FixtureRequest, client: RAG<PERSON>low, WebApiAuth: RAG<PERSON>lowWebApiAuth) -> list[str]:
    def cleanup():
        client.delete_datasets(ids=None)

    request.addfinalizer(cleanup)
    return batch_create_datasets(WebApiAuth, 5)


@pytest.fixture(scope="function")
def add_datasets_func(request: FixtureRequest, client: RA<PERSON><PERSON><PERSON>, WebApiAuth: RAG<PERSON><PERSON>WebApiAuth) -> list[str]:
    def cleanup():
        client.delete_datasets(ids=None)

    request.addfinalizer(cleanup)
    return batch_create_datasets(WebApiAuth, 3)
