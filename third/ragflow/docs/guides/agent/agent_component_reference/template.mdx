---
sidebar_position: 11
slug: /template_component
---

# Template component

A component that formats user inputs or the outputs of other components.

---

A **Template** component acts as a content formatter. It is usually the upstream component of an **Interact** component.


## Scenarios

A **Template** component is useful for organizing various sources of data or information into specific formats.

## Configurations

### Content 

Used together with Keys to organize various data or information sources into desired formats. Example:

```text
<h2>{subtitle}</h2>
<div>{content}</div>
```

Where `{subtitle}` and `{content}` are defined keys.

### Key 

A **Template** component relies on keys (variables) to specify its data or information sources. Its immediate upstream component is *not* necessarily its input, and the arrows in the workflow indicate *only* the processing sequence.

Values of keys are categorized into two groups:

- **Component Output**: The value of the key should be a component ID.
- **Begin Input**: The value of the key should be the name of a global variable defined in the **Begin** component.

## Examples

Explore our research report generator agent template, where the **Template** component (component ID: **Article**) organizes user input and the outputs of the **Sections** component into HTML format:

1. Click the **Agent** tab at the top center of the page to access the **Agent** page.
2. Click **+ Create agent** on the top right of the page to open the **agent template** page.
3. On the **agent template** page, hover over the **Research report generator** card and click **Use this template**.
4. Name your new agent and click **OK** to enter the workflow editor.
5. Click on the **Template** component to display its **Configuration** window
