{{- if eq .Values.env.DOC_ENGINE "elasticsearch" -}}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "ragflow.fullname" . }}-es-data
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    {{- include "ragflow.labels" . | nindent 4 }}
    app.kubernetes.io/component: elasticsearch
spec:
  {{- with .Values.elasticsearch.storage.className }}
  storageClassName: {{ . }}
  {{- end }}
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.elasticsearch.storage.capacity }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ragflow.fullname" . }}-es
  labels:
    {{- include "ragflow.labels" . | nindent 4 }}
    app.kubernetes.io/component: elasticsearch
spec:
  replicas: 1
  selector:
    matchLabels:
      {{- include "ragflow.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: elasticsearch
  {{- with .Values.elasticsearch.deployment.strategy }}
  strategy:
    {{- . | toYaml | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels:
      {{- include "ragflow.labels" . | nindent 8 }}
        app.kubernetes.io/component: elasticsearch
      annotations:
        checksum/config-es: {{ include (print $.Template.BasePath "/elasticsearch-config.yaml") . | sha256sum }}
        checksum/config-env: {{ include (print $.Template.BasePath "/env.yaml") . | sha256sum }}
    spec:
      initContainers:
      - name: fix-data-volume-permissions
        image: alpine
        command:
        - sh
        - -c
        - "chown -R 1000:0 /usr/share/elasticsearch/data"
        volumeMounts:
          - mountPath: /usr/share/elasticsearch/data
            name: es-data
      - name: sysctl
        image: busybox
        securityContext:
          privileged: true
          runAsUser: 0
        command: ["sysctl", "-w", "vm.max_map_count=262144"]
      containers:
      - name: elasticsearch
        image: elasticsearch:{{ .Values.env.STACK_VERSION }}
        envFrom:
          - secretRef:
              name: {{ include "ragflow.fullname" . }}-env-config
          - configMapRef:
              name: {{ include "ragflow.fullname" . }}-es-config
        ports:
          - containerPort: 9200
            name: http
          - containerPort: 9300
            name: transport
        volumeMounts:
          - mountPath: /usr/share/elasticsearch/data
            name: es-data
        {{- with .Values.elasticsearch.deployment.resources }}
        resources:
          {{- . | toYaml | nindent 10 }}
        {{- end }}
        securityContext:
          capabilities:
            add:
              - "IPC_LOCK"
          runAsUser: 1000
          # NOTE: fsGroup doesn't seem to
          # work so use init container instead
          # fsGroup: 1000
          allowPrivilegeEscalation: false
      volumes:
        - name: es-data
          persistentVolumeClaim:
            claimName: {{ include "ragflow.fullname" . }}-es-data
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "ragflow.fullname" . }}-es
  labels:
    {{- include "ragflow.labels" . | nindent 4 }}
    app.kubernetes.io/component: elasticsearch
spec:
  selector:
    {{- include "ragflow.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: elasticsearch
  ports:
    - protocol: TCP
      port: 9200
      targetPort: http
  type: {{ .Values.elasticsearch.service.type }}
{{- end -}}
