from rest_framework import serializers
from .models import SearchHistory, SearchResult


class SearchHistorySerializer(serializers.ModelSerializer):
    """搜索历史序列化器"""

    username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = SearchHistory
        fields = '__all__'
        read_only_fields = ('user', 'result_count', 'response_time', 'source_ip', 'user_agent', 'created_at')


class SearchResultSerializer(serializers.ModelSerializer):
    """搜索结果序列化器"""

    class Meta:
        model = SearchResult
        fields = '__all__'


class SearchRequestSerializer(serializers.Serializer):
    """搜索请求序列化器"""

    query = serializers.CharField(max_length=500, help_text="搜索查询")
    page = serializers.IntegerField(default=1, min_value=1, help_text="页码")
    size = serializers.IntegerField(default=10, min_value=1, max_value=200, help_text="每页结果数")
    highlight = serializers.BooleanField(default=True, help_text="是否高亮关键词")


class SearchResponseSerializer(serializers.Serializer):
    """搜索响应序列化器"""

    query = serializers.CharField(help_text="搜索查询")
    total = serializers.IntegerField(help_text="总结果数")
    page = serializers.IntegerField(help_text="当前页码")
    size = serializers.IntegerField(help_text="每页结果数")
    has_next = serializers.BooleanField(help_text="是否有下一页")
    has_previous = serializers.BooleanField(help_text="是否有上一页")
    active_kb_count = serializers.IntegerField(help_text="参与搜索的激活知识库数量")
    response_time = serializers.FloatField(help_text="响应时间(秒)")
    results = serializers.ListField(help_text="搜索结果列表")


class KnowledgeSearchResultSerializer(serializers.Serializer):
    """知识搜索结果序列化器"""

    title = serializers.CharField(help_text="标题")
    content = serializers.CharField(help_text="内容")
    summary = serializers.CharField(help_text="摘要", allow_blank=True)
    source_name = serializers.CharField(help_text="来源知识库名称")
    source_type = serializers.CharField(help_text="来源类型")
    relevance_score = serializers.FloatField(help_text="相关性评分")
    metadata = serializers.DictField(help_text="元数据", default=dict)
    created_at = serializers.DateTimeField(help_text="创建时间", allow_null=True)


class IntelligentAnswerSerializer(serializers.Serializer):
    """智能回答序列化器"""

    question = serializers.CharField(help_text="用户问题")
    answer = serializers.CharField(help_text="AI生成的回答")
    confidence = serializers.FloatField(help_text="置信度评分")
    sources = serializers.ListField(help_text="回答来源信息")
    kb_sources = serializers.ListField(help_text="参与回答的知识库信息")
    response_time = serializers.FloatField(help_text="响应时间(秒)")


class RelatedQuestionsSerializer(serializers.Serializer):
    """相关问题序列化器"""

    questions = serializers.ListField(
        child=serializers.CharField(),
        help_text="相关问题列表"
    )
    categories = serializers.DictField(
        help_text="问题分类",
        default=dict
    )
