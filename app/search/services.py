import time
import json
import requests
from typing import List, Dict, Any
from django.db import models
from knowledge_bases.models import KnowledgeBase


class SearchService:
    """搜索服务类"""

    def __init__(self, user):
        self.user = user

    def get_active_knowledge_bases(self) -> List[KnowledgeBase]:
        """获取激活状态的公开知识库"""
        # 搜索时仅使用公开的知识库
        queryset = KnowledgeBase.objects.filter(is_public=True, status='active')
        return list(queryset)

    def search_knowledge(self, query: str, page: int = 1, size: int = 10, highlight: bool = True) -> Dict[str, Any]:
        """搜索知识库内容"""
        start_time = time.time()
        
        # 获取激活的知识库
        active_kbs = self.get_active_knowledge_bases()
        
        if not active_kbs:
            return {
                'query': query,
                'total': 0,
                'page': page,
                'size': size,
                'has_next': False,
                'has_previous': False,
                'active_kb_count': 0,
                'response_time': time.time() - start_time,
                'results': []
            }

        # 按平台分组知识库
        ragflow_kbs = [kb for kb in active_kbs if kb.platform == 'ragflow']
        dify_kbs = [kb for kb in active_kbs if kb.platform == 'dify']

        all_results = []

        # 搜索RagFlow知识库
        if ragflow_kbs:
            ragflow_results = self._search_ragflow_knowledge_bases(ragflow_kbs, query, page, size, highlight)
            all_results.extend(ragflow_results)

        # 搜索Dify知识库（如果需要的话）
        if dify_kbs:
            dify_results = self._search_dify_knowledge_bases(dify_kbs, query, page, size, highlight)
            all_results.extend(dify_results)

        # 按相关性排序
        all_results.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

        # 分页处理
        total = len(all_results)
        start_idx = (page - 1) * size
        end_idx = start_idx + size
        paginated_results = all_results[start_idx:end_idx]

        response_time = time.time() - start_time

        return {
            'query': query,
            'total': total,
            'page': page,
            'size': size,
            'has_next': end_idx < total,
            'has_previous': page > 1,
            'active_kb_count': len(active_kbs),
            'response_time': response_time,
            'results': paginated_results
        }

    def _search_ragflow_knowledge_bases(self, kbs: List[KnowledgeBase], query: str, page: int, size: int, highlight: bool) -> List[Dict]:
        """搜索RagFlow知识库"""
        results = []
        
        for kb in kbs:
            try:
                kb_results = self._search_single_ragflow_kb(kb, query, page, size, highlight)
                results.extend(kb_results)
            except Exception as e:
                print(f"搜索RagFlow知识库 {kb.name} 失败: {str(e)}")
                continue
        
        return results

    def _search_single_ragflow_kb(self, kb: KnowledgeBase, query: str, page: int, size: int, highlight: bool) -> List[Dict]:
        """搜索单个RagFlow知识库"""
        from knowledge_bases.ragflow_auth_service import get_ragflow_auth_header

        url = f"{kb.api_endpoint.rstrip('/')}/v1/chunk/retrieval_test"

        # 获取认证头（自动处理令牌刷新）
        auth_header = get_ragflow_auth_header(kb)
        if not auth_header:
            print(f"无法获取知识库 {kb.name} 的认证令牌")
            return []

        headers = {
            'Authorization': auth_header,
            'Content-Type': 'application/json'
        }
        
        data = {
            'kb_id': [kb.platform_kb_id],
            'question': query,
            'page': page,
            'size': size,
            'highlight': highlight
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            try:
                result_data = response.json()
                if result_data is None:
                    print(f"RagFlow API返回空数据")
                    return []
                return self._parse_ragflow_results(result_data, kb)
            except Exception as e:
                print(f"解析RagFlow响应失败: {str(e)}")
                return []
        else:
            print(f"RagFlow API调用失败: {response.status_code} - {response.text}")
            return []

    def _parse_ragflow_results(self, data: Dict, kb: KnowledgeBase) -> List[Dict]:
        """解析RagFlow搜索结果"""
        results = []

        try:
            # 根据RagFlow API响应格式解析结果
            if not data or 'data' not in data:
                print(f"RagFlow响应格式错误: {data}")
                return []

            chunks = data.get('data', {}).get('chunks', [])

            for i, chunk in enumerate(chunks):
                if not chunk:
                    continue

                result = {
                    'title': chunk.get('docnm_kwd', chunk.get('document_name', '未知文档')),
                    'content': chunk.get('content_with_weight', chunk.get('content', '')),
                    'summary': chunk.get('content', '')[:200] + '...' if len(chunk.get('content', '')) > 200 else chunk.get('content', ''),
                    'source_name': kb.name,
                    'source_type': 'knowledge_base',
                    'source_id': kb.platform_kb_id,
                    'relevance_score': chunk.get('similarity', 0.0),
                    'rank': i + 1,
                    'metadata': {
                        'kb_id': str(kb.id),
                        'kb_platform': kb.platform,
                        'document_id': chunk.get('doc_id'),
                        'chunk_id': chunk.get('chunk_id'),
                        'positions': chunk.get('positions', [])
                    },
                    'created_at': chunk.get('create_time')
                }
                results.append(result)
        except Exception as e:
            print(f"解析RagFlow结果失败: {str(e)}")

        return results

    def _search_dify_knowledge_bases(self, kbs: List[KnowledgeBase], query: str, page: int, size: int, highlight: bool) -> List[Dict]:
        """搜索Dify知识库（预留接口）"""
        # TODO: 实现Dify知识库搜索
        return []

    def ask_question(self, question: str) -> Dict[str, Any]:
        """基于知识库回答问题"""
        start_time = time.time()
        
        # 获取激活的RagFlow知识库
        active_kbs = self.get_active_knowledge_bases()
        ragflow_kbs = [kb for kb in active_kbs if kb.platform == 'ragflow']
        
        if not ragflow_kbs:
            return {
                'question': question,
                'answer': '抱歉，当前没有可用的知识库来回答您的问题。',
                'confidence': 0.0,
                'sources': [],
                'kb_sources': [],
                'response_time': time.time() - start_time
            }

        # 使用第一个可用的RagFlow知识库回答问题
        kb = ragflow_kbs[0]
        
        try:
            result = self._ask_ragflow_question(kb, question, [kb.platform_kb_id for kb in ragflow_kbs])
            result['response_time'] = time.time() - start_time
            result['kb_sources'] = [{'name': kb.name, 'platform': kb.platform} for kb in ragflow_kbs]
            return result
        except Exception as e:
            print(f"RagFlow问答失败: {str(e)}")
            return {
                'question': question,
                'answer': f'抱歉，回答问题时出现错误: {str(e)}',
                'confidence': 0.0,
                'sources': [],
                'kb_sources': [],
                'response_time': time.time() - start_time
            }

    def stream_ragflow_answer(self, kb: KnowledgeBase, question: str, kb_ids: List[str]):
        """流式获取RagFlow回答"""
        from knowledge_bases.ragflow_auth_service import get_ragflow_auth_header

        url = f"{kb.api_endpoint.rstrip('/')}/v1/conversation/ask"

        # 获取认证头（自动处理令牌刷新）
        auth_header = get_ragflow_auth_header(kb)
        if not auth_header:
            print(f"无法获取知识库 {kb.name} 的认证令牌")
            return

        headers = {
            'Authorization': auth_header,
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
        }

        data = {
            'kb_ids': kb_ids,
            'question': question
        }

        try:
            response = requests.post(url, headers=headers, json=data, timeout=30, stream=True)

            if response.status_code == 200:
                for line in response.iter_lines(decode_unicode=True):
                    if line:
                        line = line.strip()

                        # SSE格式：data: {...}
                        if line.startswith('data:'):
                            data_str = line[5:].strip()

                            # 跳过空数据和结束标记
                            if not data_str or data_str == '[DONE]':
                                continue

                            try:
                                data = json.loads(data_str)

                                # 检查响应格式
                                if 'code' in data and data['code'] == 0 and 'data' in data:
                                    response_data = data['data']

                                    # 如果data是布尔值，表示流结束
                                    if isinstance(response_data, bool):
                                        break

                                    # 返回格式化的数据块
                                    if 'answer' in response_data:
                                        yield {
                                            'type': 'answer',
                                            'content': response_data['answer'],
                                            'reference': response_data.get('reference', {})
                                        }

                            except json.JSONDecodeError as e:
                                print(f"JSON解析错误: {str(e)}, 数据: {data_str}")
                                continue
            else:
                yield {
                    'type': 'error',
                    'message': f'RagFlow API调用失败: {response.status_code}'
                }

        except Exception as e:
            yield {
                'type': 'error',
                'message': f'请求失败: {str(e)}'
            }

    def _ask_ragflow_question(self, kb: KnowledgeBase, question: str, kb_ids: List[str]) -> Dict[str, Any]:
        """向RagFlow提问（处理SSE流式响应）"""
        from knowledge_bases.ragflow_auth_service import get_ragflow_auth_header

        url = f"{kb.api_endpoint.rstrip('/')}/v1/conversation/ask"

        # 获取认证头（自动处理令牌刷新）
        auth_header = get_ragflow_auth_header(kb)
        if not auth_header:
            raise Exception(f"无法获取知识库 {kb.name} 的认证令牌")

        headers = {
            'Authorization': auth_header,
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'  # 接受SSE流
        }

        data = {
            'kb_ids': kb_ids,
            'question': question
        }

        response = requests.post(url, headers=headers, json=data, timeout=30, stream=True)

        if response.status_code == 200:
            try:
                # 解析SSE流式响应
                answer_data = self._parse_sse_response(response)
                return self._parse_ragflow_answer(answer_data, question)
            except Exception as e:
                raise Exception(f"解析RagFlow SSE响应失败: {str(e)}")
        else:
            raise Exception(f"RagFlow API调用失败: {response.status_code} - {response.text}")

    def _parse_sse_response(self, response) -> Dict[str, Any]:
        """解析SSE流式响应"""
        final_answer = ""
        final_reference = {}

        try:
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    line = line.strip()

                    # SSE格式：data: {...}
                    if line.startswith('data:'):
                        data_str = line[5:].strip()  # 移除 'data:' 前缀

                        # 跳过空数据和结束标记
                        if not data_str or data_str == '[DONE]':
                            continue

                        try:
                            data = json.loads(data_str)

                            # 检查响应格式
                            if 'code' in data and data['code'] == 0 and 'data' in data:
                                response_data = data['data']

                                # 如果data是布尔值，表示流结束
                                if isinstance(response_data, bool):
                                    break

                                # 获取最新的完整答案（RagFlow返回的是累积的完整答案）
                                if 'answer' in response_data:
                                    final_answer = response_data['answer']

                                # 获取参考文献（取最后一个有内容的reference）
                                if 'reference' in response_data and response_data['reference']:
                                    final_reference = response_data['reference']

                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {str(e)}, 数据: {data_str}")
                            continue

            return {
                'data': {
                    'answer': final_answer,
                    'reference': final_reference
                }
            }

        except Exception as e:
            print(f"解析SSE流失败: {str(e)}")
            return {
                'data': {
                    'answer': '抱歉，解析回答时出现错误。',
                    'reference': {}
                }
            }

    def _parse_ragflow_answer(self, data: Dict, question: str) -> Dict[str, Any]:
        """解析RagFlow回答结果"""
        try:
            # 根据RagFlow API响应格式解析
            if not data or 'data' not in data:
                print(f"RagFlow回答响应格式错误: {data}")
                return {
                    'question': question,
                    'answer': '抱歉，无法生成回答。',
                    'confidence': 0.0,
                    'sources': []
                }

            answer = data.get('data', {}).get('answer', '抱歉，无法生成回答。')
            reference = data.get('data', {}).get('reference', {})

            sources = []
            # 处理新的reference格式：{'total': 0, 'chunks': [], 'doc_aggs': []}
            if isinstance(reference, dict):
                chunks = reference.get('chunks', [])
                for chunk in chunks:
                    if chunk:
                        sources.append({
                            'document_name': chunk.get('docnm_kwd', chunk.get('document_name', '')),
                            'content': chunk.get('content', ''),
                            'similarity': chunk.get('similarity', 0.0)
                        })
            elif isinstance(reference, list):
                # 兼容旧格式
                for ref in reference:
                    if ref:
                        sources.append({
                            'document_name': ref.get('docnm_kwd', ref.get('document_name', '')),
                            'content': ref.get('content', ''),
                            'similarity': ref.get('similarity', 0.0)
                        })

            return {
                'question': question,
                'answer': answer,
                'confidence': 0.8,  # 默认置信度
                'sources': sources
            }
        except Exception as e:
            print(f"解析RagFlow回答失败: {str(e)}")
            return {
                'question': question,
                'answer': f'解析回答时出现错误: {str(e)}',
                'confidence': 0.0,
                'sources': []
            }

    def get_related_questions(self, question: str) -> Dict[str, Any]:
        """获取相关问题推荐"""
        # 获取激活的RagFlow知识库
        active_kbs = self.get_active_knowledge_bases()
        ragflow_kbs = [kb for kb in active_kbs if kb.platform == 'ragflow']
        
        if not ragflow_kbs:
            return {
                'questions': [],
                'categories': {}
            }

        kb = ragflow_kbs[0]
        
        try:
            return self._get_ragflow_related_questions(kb, question)
        except Exception as e:
            print(f"获取相关问题失败: {str(e)}")
            return {
                'questions': [],
                'categories': {}
            }

    def _get_ragflow_related_questions(self, kb: KnowledgeBase, question: str) -> Dict[str, Any]:
        """从RagFlow获取相关问题"""
        from knowledge_bases.ragflow_auth_service import get_ragflow_auth_header

        url = f"{kb.api_endpoint.rstrip('/')}/v1/conversation/related_questions"

        # 获取认证头（自动处理令牌刷新）
        auth_header = get_ragflow_auth_header(kb)
        if not auth_header:
            raise Exception(f"无法获取知识库 {kb.name} 的认证令牌")

        headers = {
            'Authorization': auth_header,
            'Content-Type': 'application/json'
        }
        
        data = {
            'question': question
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result_data = response.json()
            questions = result_data.get('data', [])
            return {
                'questions': questions,
                'categories': {}  # RagFlow可能不提供分类信息
            }
        else:
            raise Exception(f"RagFlow API调用失败: {response.status_code} - {response.text}")
