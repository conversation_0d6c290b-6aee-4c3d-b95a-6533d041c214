# Generated by Django 5.2.3 on 2025-06-22 10:29

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SearchHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.CharField(max_length=500, verbose_name='搜索查询')),
                ('result_count', models.PositiveIntegerField(default=0, verbose_name='结果数量')),
                ('response_time', models.FloatField(default=0.0, verbose_name='响应时间(秒)')),
                ('source_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='来源IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='搜索时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='search_history', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '搜索历史',
                'verbose_name_plural': '搜索历史',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SearchResult',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=500, verbose_name='标题')),
                ('content', models.TextField(verbose_name='内容')),
                ('summary', models.TextField(blank=True, max_length=1000, verbose_name='摘要')),
                ('source_type', models.CharField(max_length=50, verbose_name='来源类型')),
                ('source_id', models.CharField(max_length=200, verbose_name='来源ID')),
                ('source_name', models.CharField(max_length=200, verbose_name='来源名称')),
                ('relevance_score', models.FloatField(default=0.0, verbose_name='相关性评分')),
                ('rank', models.PositiveIntegerField(verbose_name='排名')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('search_history', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='results', to='search.searchhistory', verbose_name='搜索历史')),
            ],
            options={
                'verbose_name': '搜索结果',
                'verbose_name_plural': '搜索结果',
                'ordering': ['rank'],
            },
        ),
        migrations.AddIndex(
            model_name='searchhistory',
            index=models.Index(fields=['user', '-created_at'], name='search_sear_user_id_71e095_idx'),
        ),
        migrations.AddIndex(
            model_name='searchhistory',
            index=models.Index(fields=['query'], name='search_sear_query_d8a8e0_idx'),
        ),
        migrations.AddIndex(
            model_name='searchresult',
            index=models.Index(fields=['search_history', 'rank'], name='search_sear_search__c77704_idx'),
        ),
        migrations.AddIndex(
            model_name='searchresult',
            index=models.Index(fields=['source_type', 'source_id'], name='search_sear_source__407f06_idx'),
        ),
    ]
