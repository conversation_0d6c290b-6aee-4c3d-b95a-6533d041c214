import uuid
from django.db import models
from django.conf import settings


class SearchHistory(models.Model):
    """搜索历史模型"""

    # 主键
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name='ID')

    # 搜索信息
    query = models.CharField(max_length=500, verbose_name='搜索查询')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='search_history', verbose_name='用户')

    # 搜索结果统计
    result_count = models.PositiveIntegerField(default=0, verbose_name='结果数量')
    response_time = models.FloatField(default=0.0, verbose_name='响应时间(秒)')

    # 搜索来源
    source_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name='来源IP')
    user_agent = models.TextField(blank=True, verbose_name='用户代理')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='搜索时间')

    class Meta:
        verbose_name = '搜索历史'
        verbose_name_plural = '搜索历史'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['query']),
        ]

    def __str__(self):
        return f'{self.user.username}: {self.query[:50]}'


class SearchResult(models.Model):
    """搜索结果模型"""

    # 主键
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name='ID')

    # 关联搜索历史
    search_history = models.ForeignKey(SearchHistory, on_delete=models.CASCADE, related_name='results', verbose_name='搜索历史')

    # 结果信息
    title = models.CharField(max_length=500, verbose_name='标题')
    content = models.TextField(verbose_name='内容')
    summary = models.TextField(max_length=1000, blank=True, verbose_name='摘要')

    # 来源信息
    source_type = models.CharField(max_length=50, verbose_name='来源类型')  # knowledge_base, document, etc.
    source_id = models.CharField(max_length=200, verbose_name='来源ID')
    source_name = models.CharField(max_length=200, verbose_name='来源名称')

    # 相关性评分
    relevance_score = models.FloatField(default=0.0, verbose_name='相关性评分')
    rank = models.PositiveIntegerField(verbose_name='排名')

    # 元数据
    metadata = models.JSONField(default=dict, blank=True, verbose_name='元数据')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '搜索结果'
        verbose_name_plural = '搜索结果'
        ordering = ['rank']
        indexes = [
            models.Index(fields=['search_history', 'rank']),
            models.Index(fields=['source_type', 'source_id']),
        ]

    def __str__(self):
        return f'{self.title[:50]} (排名: {self.rank})'
