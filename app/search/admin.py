from django.contrib import admin
from .models import SearchHistory, SearchResult


@admin.register(SearchHistory)
class SearchHistoryAdmin(admin.ModelAdmin):
    """搜索历史管理"""

    list_display = ('query', 'user', 'result_count', 'response_time', 'created_at')
    list_filter = ('created_at', 'result_count')
    search_fields = ('query', 'user__username')
    ordering = ('-created_at',)
    readonly_fields = ('created_at',)

    fieldsets = (
        ('搜索信息', {
            'fields': ('query', 'user', 'result_count', 'response_time')
        }),
        ('来源信息', {
            'fields': ('source_ip', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )


@admin.register(SearchResult)
class SearchResultAdmin(admin.ModelAdmin):
    """搜索结果管理"""

    list_display = ('title', 'search_history', 'source_name', 'relevance_score', 'rank', 'created_at')
    list_filter = ('source_type', 'created_at')
    search_fields = ('title', 'content', 'source_name')
    ordering = ('search_history', 'rank')
    readonly_fields = ('created_at',)

    fieldsets = (
        ('结果信息', {
            'fields': ('search_history', 'title', 'content', 'summary')
        }),
        ('来源信息', {
            'fields': ('source_type', 'source_id', 'source_name')
        }),
        ('评分信息', {
            'fields': ('relevance_score', 'rank')
        }),
        ('元数据', {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )
