import time
from django.shortcuts import render
from django.http import JsonResponse
from django.db import models
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import SearchHistory, SearchResult
from .serializers import (
    SearchRequestSerializer,
    SearchResponseSerializer,
    IntelligentAnswerSerializer,
    RelatedQuestionsSerializer,
    SearchHistorySerializer
)
from .services import SearchService


def search_page(request):
    """搜索页面视图"""
    query = request.GET.get('q', '')
    page = request.GET.get('page', 1)

    context = {
        'query': query,
        'page': page
    }

    return render(request, 'search.html', context)


class KnowledgeSearchAPIView(APIView):
    """知识搜索API视图"""

    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get(self, request):
        """搜索知识库内容"""
        # 处理查询参数，将 'q' 转换为 'query'
        query_params = request.query_params.copy()
        if 'q' in query_params:
            query_params['query'] = query_params.pop('q')[0]

        serializer = SearchRequestSerializer(data=query_params)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        query = serializer.validated_data['query']
        page = serializer.validated_data['page']
        size = serializer.validated_data['size']
        highlight = serializer.validated_data['highlight']

        # 创建搜索服务
        search_service = SearchService(request.user if request.user.is_authenticated else None)

        try:
            # 执行搜索
            search_result = search_service.search_knowledge(query, page, size, highlight)

            # 记录搜索历史（仅对认证用户）
            if request.user.is_authenticated:
                self._save_search_history(request, query, search_result)

            return Response(search_result)

        except Exception as e:
            return Response({
                'error': f'搜索失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _save_search_history(self, request, query, search_result):
        """保存搜索历史"""
        try:
            # 获取客户端IP
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR')

            # 创建搜索历史记录
            search_history = SearchHistory.objects.create(
                query=query,
                user=request.user,
                result_count=search_result.get('total', 0),
                response_time=search_result.get('response_time', 0.0),
                source_ip=ip,
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            # 保存搜索结果（可选，用于分析）
            for i, result in enumerate(search_result.get('results', [])):
                SearchResult.objects.create(
                    search_history=search_history,
                    title=result.get('title', ''),
                    content=result.get('content', ''),
                    summary=result.get('summary', ''),
                    source_type=result.get('source_type', ''),
                    source_id=result.get('source_id', ''),
                    source_name=result.get('source_name', ''),
                    relevance_score=result.get('relevance_score', 0.0),
                    rank=i + 1,
                    metadata=result.get('metadata', {})
                )

        except Exception as e:
            print(f"保存搜索历史失败: {str(e)}")


from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json

@csrf_exempt
@require_http_methods(["POST"])
def intelligent_answer_view(request):
    """智能问答API视图（SSE流式响应）"""
    from django.http import StreamingHttpResponse

    try:
        data = json.loads(request.body)
        question = data.get('question')
    except json.JSONDecodeError:
        return JsonResponse({
            'error': '无效的JSON数据'
        }, status=400)

    if not question:
        return JsonResponse({
            'error': '问题不能为空'
        }, status=400)

    def generate_answer():
        try:
            # 创建搜索服务
            search_service = SearchService(request.user if request.user.is_authenticated else None)

            # 获取参与回答的知识库信息
            active_kbs = search_service.get_active_knowledge_bases()
            kb_sources = [
                {'name': kb.name, 'platform': kb.platform}
                for kb in active_kbs
            ]

            # 发送初始信息
            yield f"data: {json.dumps({'type': 'start', 'question': question, 'kb_sources': kb_sources})}\n\n"

            # 获取第一个可用的知识库进行问答
            if active_kbs:
                kb = active_kbs[0]
                kb_ids = [kb.platform_kb_id for kb in active_kbs]

                # 直接流式传输RagFlow的响应
                for chunk in search_service.stream_ragflow_answer(kb, question, kb_ids):
                    yield f"data: {json.dumps(chunk)}\n\n"
            else:
                yield f"data: {json.dumps({'type': 'error', 'message': '没有可用的知识库'})}\n\n"

            # 发送结束信号
            yield f"data: {json.dumps({'type': 'end'})}\n\n"

        except Exception as e:
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

    response = StreamingHttpResponse(
        generate_answer(),
        content_type='text/event-stream'
    )
    response['Cache-Control'] = 'no-cache'
    response['Access-Control-Allow-Origin'] = '*'
    return response


class RelatedQuestionsAPIView(APIView):
    """相关问题推荐API视图"""

    permission_classes = [permissions.AllowAny]

    def post(self, request):
        """获取相关问题推荐"""
        question = request.data.get('question')
        if not question:
            return Response({
                'error': '问题不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建搜索服务
        search_service = SearchService(request.user if request.user.is_authenticated else None)

        try:
            # 获取相关问题
            related_result = search_service.get_related_questions(question)

            return Response(related_result)

        except Exception as e:
            return Response({
                'error': f'获取相关问题失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_history_view(request):
    """用户搜索历史"""

    # 获取用户最近的搜索历史
    history = SearchHistory.objects.filter(user=request.user).order_by('-created_at')[:20]

    serializer = SearchHistorySerializer(history, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_stats_view(request):
    """搜索统计"""

    user = request.user

    # 用户搜索统计
    total_searches = SearchHistory.objects.filter(user=user).count()
    recent_searches = SearchHistory.objects.filter(user=user).order_by('-created_at')[:5]

    # 计算平均响应时间
    avg_response_time = SearchHistory.objects.filter(user=user).aggregate(
        avg_time=models.Avg('response_time')
    )['avg_time'] or 0.0

    stats = {
        'total_searches': total_searches,
        'avg_response_time': round(avg_response_time, 2),
        'recent_searches': SearchHistorySerializer(recent_searches, many=True).data
    }

    return Response(stats)
