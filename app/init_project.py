#!/usr/bin/env python
"""
项目初始化脚本
删除数据库后重新初始化项目的完整流程
"""

import os
import sys
import subprocess
import django
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agent_portal.settings')

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"正在执行: {description}")
    print(f"命令: {command}")
    print('='*50)
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print("输出:")
            print(result.stdout)
        if result.stderr:
            print("警告:")
            print(result.stderr)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误代码: {e.returncode}")
        if e.stdout:
            print("输出:")
            print(e.stdout)
        if e.stderr:
            print("错误:")
            print(e.stderr)
        return False

def main():
    """主函数"""
    print("🚀 开始初始化Agent Portal项目...")
    
    # 检查是否在正确的目录
    if not Path('manage.py').exists():
        print("❌ 错误: 请在包含manage.py的目录中运行此脚本")
        sys.exit(1)
    
    # 1. 删除现有数据库（如果存在）
    db_path = Path('db.sqlite3')
    if db_path.exists():
        print(f"🗑️  删除现有数据库: {db_path}")
        db_path.unlink()
    
    # 2. 运行数据库迁移
    if not run_command('python manage.py migrate', '数据库迁移'):
        print("❌ 数据库迁移失败，停止初始化")
        sys.exit(1)
    
    # 3. 创建超级用户
    if not run_command('python manage.py create_superuser', '创建超级用户'):
        print("❌ 创建超级用户失败，停止初始化")
        sys.exit(1)
    
    # 4. 收集静态文件
    if not run_command('python manage.py collectstatic --noinput', '收集静态文件'):
        print("⚠️  收集静态文件失败，但可以继续")
    
    # 5. 创建系统设置
    print("\n" + "="*50)
    print("正在创建系统设置...")
    print("="*50)
    
    try:
        django.setup()
        from core.models import SystemSettings
        
        # 创建默认系统设置
        settings, created = SystemSettings.objects.get_or_create(
            defaults={
                'site_name': 'Agent Portal',
                'site_description': '智能体门户平台',
                'max_conversations_per_user': 5,
                'api_rate_limit_per_minute': 60,
                'allow_registration': True,
                'maintenance_mode': False
            }
        )
        
        if created:
            print("✅ 系统设置创建成功")
        else:
            print("ℹ️  系统设置已存在")
            
    except Exception as e:
        print(f"⚠️  创建系统设置失败: {e}")
    
    # 完成
    print("\n" + "="*60)
    print("🎉 项目初始化完成！")
    print("="*60)
    print("📋 登录信息:")
    print("   用户名: admin")
    print("   密码: admin123")
    print("   管理后台: http://127.0.0.1:8000/admin/")
    print("   前端页面: http://127.0.0.1:8000/")
    print("\n🚀 启动开发服务器:")
    print("   python manage.py runserver")
    print("="*60)

if __name__ == '__main__':
    main()
