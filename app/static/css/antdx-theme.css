/*!
 * Ant Design X Theme for Agent Portal
 * Based on Ant Design X v1.5.0 Design Tokens
 */

:root {
    /* Ant Design X 主色调 */
    --antdx-primary: #1677ff;
    --antdx-primary-hover: #4096ff;
    --antdx-primary-active: #0958d9;
    --antdx-primary-bg: rgba(22, 119, 255, 0.06);
    --antdx-primary-bg-hover: rgba(22, 119, 255, 0.1);
    
    /* 功能色 */
    --antdx-success: #52c41a;
    --antdx-warning: #faad14;
    --antdx-error: #ff4d4f;
    --antdx-info: #1677ff;
    
    /* 渐变色 */
    --antdx-gradient-primary: linear-gradient(135deg, #1677ff 0%, #4096ff 100%);
    --antdx-gradient-secondary: linear-gradient(135deg, #722ed1 0%, #eb2f96 100%);
    
    /* 背景色 */
    --antdx-bg-container: #ffffff;
    --antdx-bg-layout: #f5f5f5;
    --antdx-bg-elevated: #ffffff;
    --antdx-bg-spotlight: #fafafa;
    
    /* 文字颜色 */
    --antdx-text-primary: #262626;
    --antdx-text-secondary: #8c8c8c;
    --antdx-text-tertiary: #bfbfbf;
    --antdx-text-quaternary: #d9d9d9;
    
    /* 边框和分割线 */
    --antdx-border: #d9d9d9;
    --antdx-border-light: #f0f0f0;
    --antdx-border-secondary: #e6f4ff;
    
    /* 阴影 */
    --antdx-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
    --antdx-shadow-md: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    --antdx-shadow-lg: 0 16px 48px 16px rgba(0, 0, 0, 0.08), 0 12px 32px rgba(0, 0, 0, 0.12), 0 8px 16px -8px rgba(0, 0, 0, 0.16);
    
    /* 圆角 */
    --antdx-radius-xs: 2px;
    --antdx-radius-sm: 4px;
    --antdx-radius-md: 6px;
    --antdx-radius-lg: 8px;
    --antdx-radius-xl: 16px;
    
    /* 间距 */
    --antdx-spacing-xs: 4px;
    --antdx-spacing-sm: 8px;
    --antdx-spacing-md: 12px;
    --antdx-spacing-lg: 16px;
    --antdx-spacing-xl: 24px;
    --antdx-spacing-xxl: 32px;
    
    /* 字体 */
    --antdx-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --antdx-font-size-sm: 12px;
    --antdx-font-size-base: 14px;
    --antdx-font-size-lg: 16px;
    --antdx-font-size-xl: 20px;
    
    /* 行高 */
    --antdx-line-height-base: 1.5715;
    --antdx-line-height-lg: 1.5;
    
    /* 过渡动画 */
    --antdx-motion-duration-slow: 0.3s;
    --antdx-motion-duration-mid: 0.2s;
    --antdx-motion-duration-fast: 0.1s;
    --antdx-motion-ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
    --antdx-motion-ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    --antdx-motion-ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
    
    /* Z-index层级 */
    --antdx-z-index-base: 0;
    --antdx-z-index-popup: 1000;
    --antdx-z-index-modal: 1050;
    --antdx-z-index-notification: 1100;
    --antdx-z-index-tooltip: 1200;
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--antdx-font-family);
    font-size: var(--antdx-font-size-base);
    line-height: var(--antdx-line-height-base);
    color: var(--antdx-text-primary);
    background-color: var(--antdx-bg-layout);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Ant Design X 按钮样式 */
.antdx-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid transparent;
    border-radius: var(--antdx-radius-md);
    padding: var(--antdx-spacing-sm) var(--antdx-spacing-lg);
    font-size: var(--antdx-font-size-base);
    font-weight: 500;
    line-height: var(--antdx-line-height-base);
    cursor: pointer;
    transition: all var(--antdx-motion-duration-mid) var(--antdx-motion-ease-out);
    user-select: none;
    text-decoration: none;
}

.antdx-btn:hover {
    text-decoration: none;
}

.antdx-btn-primary {
    background-color: var(--antdx-primary);
    border-color: var(--antdx-primary);
    color: #ffffff;
}

.antdx-btn-primary:hover {
    background-color: var(--antdx-primary-hover);
    border-color: var(--antdx-primary-hover);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: var(--antdx-shadow-sm);
}

.antdx-btn-primary:active {
    background-color: var(--antdx-primary-active);
    border-color: var(--antdx-primary-active);
    transform: translateY(0);
}

/* Ant Design X 卡片样式 */
.antdx-card {
    background: var(--antdx-bg-elevated);
    border: 1px solid var(--antdx-border-light);
    border-radius: var(--antdx-radius-xl);
    box-shadow: var(--antdx-shadow-sm);
    transition: all var(--antdx-motion-duration-mid) var(--antdx-motion-ease-out);
}

.antdx-card:hover {
    box-shadow: var(--antdx-shadow-md);
    transform: translateY(-2px);
}

.antdx-card-body {
    padding: var(--antdx-spacing-xl);
}

/* Ant Design X 输入框样式 */
.antdx-input {
    display: block;
    width: 100%;
    padding: var(--antdx-spacing-sm) var(--antdx-spacing-md);
    font-size: var(--antdx-font-size-base);
    line-height: var(--antdx-line-height-base);
    color: var(--antdx-text-primary);
    background-color: var(--antdx-bg-container);
    border: 1px solid var(--antdx-border);
    border-radius: var(--antdx-radius-md);
    transition: all var(--antdx-motion-duration-mid) var(--antdx-motion-ease-out);
}

.antdx-input:focus {
    border-color: var(--antdx-primary);
    box-shadow: 0 0 0 2px var(--antdx-primary-bg);
    outline: none;
}

.antdx-input::placeholder {
    color: var(--antdx-text-tertiary);
}

/* Ant Design X 消息气泡样式 */
.antdx-message-bubble {
    max-width: 70%;
    margin-bottom: var(--antdx-spacing-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--antdx-spacing-sm);
    animation: antdx-fade-in var(--antdx-motion-duration-mid) var(--antdx-motion-ease-out);
}

.antdx-message-bubble.user {
    margin-left: auto;
    flex-direction: row-reverse;
}

.antdx-message-content {
    background: var(--antdx-bg-elevated);
    border: 1px solid var(--antdx-border-light);
    border-radius: var(--antdx-radius-xl);
    padding: var(--antdx-spacing-md) var(--antdx-spacing-lg);
    box-shadow: var(--antdx-shadow-sm);
    color: var(--antdx-text-primary);
    position: relative;
    max-width: 100%;
    word-wrap: break-word;
    line-height: var(--antdx-line-height-lg);
}

.antdx-message-bubble.user .antdx-message-content {
    background: var(--antdx-primary);
    border-color: var(--antdx-primary);
    color: #ffffff;
}

.antdx-message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: var(--antdx-font-size-base);
    font-weight: 500;
}

.antdx-message-avatar.user {
    background: var(--antdx-primary);
    color: #ffffff;
}

.antdx-message-avatar.assistant {
    background: var(--antdx-bg-elevated);
    color: var(--antdx-primary);
    border: 1px solid var(--antdx-border);
}

/* 动画效果 */
@keyframes antdx-fade-in {
    from {
        opacity: 0;
        transform: translateY(4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes antdx-pulse {
    0% {
        transform: scale(0.8);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
    100% {
        transform: scale(0.8);
        opacity: 0.3;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .antdx-message-bubble {
        max-width: 85%;
    }
    
    .antdx-card-body {
        padding: var(--antdx-spacing-lg);
    }
}
