"""
AI平台配置管理
"""

from typing import Dict, Any, List


class AIConfigManager:
    """AI平台配置管理器"""
    
    @staticmethod
    def get_platform_configs() -> Dict[str, Dict[str, Any]]:
        """获取所有平台的配置信息"""
        return {
            'dify': {
                'name': 'Dify',
                'description': 'Dify是一个开源的LLM应用开发平台',
                'icon': '🔧',
                'required_fields': [
                    {
                        'name': 'api_endpoint',
                        'label': 'API端点',
                        'type': 'url',
                        'placeholder': 'https://api.dify.ai/v1',
                        'help': 'Dify API的基础URL'
                    },
                    {
                        'name': 'api_key',
                        'label': 'API密钥',
                        'type': 'password',
                        'placeholder': 'app-xxx',
                        'help': '从Dify应用设置中获取的API Key'
                    }
                ],
                'optional_fields': [
                    {
                        'name': 'platform_agent_id',
                        'label': '应用ID',
                        'type': 'text',
                        'placeholder': 'app-xxx',
                        'help': 'Dify应用的唯一标识符'
                    }
                ],
                'config_fields': [
                    {
                        'name': 'response_mode',
                        'label': '响应模式',
                        'type': 'select',
                        'options': [
                            {'value': 'blocking', 'label': '阻塞模式'},
                            {'value': 'streaming', 'label': '流式模式'}
                        ],
                        'default': 'blocking'
                    },
                    {
                        'name': 'auto_generate_name',
                        'label': '自动生成对话名称',
                        'type': 'boolean',
                        'default': True
                    }
                ]
            },
            'ragflow': {
                'name': 'RagFlow',
                'description': 'RagFlow是一个基于深度文档理解的RAG引擎',
                'icon': '📚',
                'required_fields': [
                    {
                        'name': 'api_endpoint',
                        'label': 'API端点',
                        'type': 'url',
                        'placeholder': 'https://demo.ragflow.io',
                        'help': 'RagFlow服务的基础URL'
                    },
                    {
                        'name': 'api_key',
                        'label': 'API密钥',
                        'type': 'password',
                        'placeholder': 'ragflow-xxx',
                        'help': '从RagFlow设置中获取的API Token'
                    }
                ],
                'optional_fields': [
                    {
                        'name': 'platform_agent_id',
                        'label': '知识库ID',
                        'type': 'text',
                        'placeholder': 'kb_xxx',
                        'help': '要使用的知识库ID'
                    }
                ],
                'config_fields': [
                    {
                        'name': 'stream',
                        'label': '流式输出',
                        'type': 'boolean',
                        'default': False
                    },
                    {
                        'name': 'temperature',
                        'label': '温度参数',
                        'type': 'number',
                        'min': 0,
                        'max': 1,
                        'step': 0.1,
                        'default': 0.7
                    }
                ]
            },
            'openai': {
                'name': 'OpenAI',
                'description': 'OpenAI GPT系列模型',
                'icon': '🤖',
                'required_fields': [
                    {
                        'name': 'api_key',
                        'label': 'API密钥',
                        'type': 'password',
                        'placeholder': 'sk-xxx',
                        'help': '从OpenAI获取的API Key'
                    }
                ],
                'optional_fields': [
                    {
                        'name': 'api_endpoint',
                        'label': 'API端点',
                        'type': 'url',
                        'placeholder': 'https://api.openai.com/v1',
                        'help': '自定义API端点（可选，默认使用OpenAI官方）'
                    }
                ],
                'config_fields': [
                    {
                        'name': 'model',
                        'label': '模型',
                        'type': 'select',
                        'options': [
                            {'value': 'gpt-3.5-turbo', 'label': 'GPT-3.5 Turbo'},
                            {'value': 'gpt-4', 'label': 'GPT-4'},
                            {'value': 'gpt-4-turbo', 'label': 'GPT-4 Turbo'},
                            {'value': 'gpt-4o', 'label': 'GPT-4o'}
                        ],
                        'default': 'gpt-3.5-turbo'
                    },
                    {
                        'name': 'temperature',
                        'label': '温度参数',
                        'type': 'number',
                        'min': 0,
                        'max': 2,
                        'step': 0.1,
                        'default': 0.7
                    },
                    {
                        'name': 'max_tokens',
                        'label': '最大Token数',
                        'type': 'number',
                        'min': 1,
                        'max': 4000,
                        'default': 1000
                    },
                    {
                        'name': 'top_p',
                        'label': 'Top P',
                        'type': 'number',
                        'min': 0,
                        'max': 1,
                        'step': 0.1,
                        'default': 1
                    }
                ]
            },
            'custom': {
                'name': '自定义',
                'description': '自定义AI平台或本地部署的模型',
                'icon': '⚡',
                'required_fields': [
                    {
                        'name': 'api_endpoint',
                        'label': 'API端点',
                        'type': 'url',
                        'placeholder': 'https://your-api.com/chat',
                        'help': '自定义API的完整URL'
                    }
                ],
                'optional_fields': [
                    {
                        'name': 'api_key',
                        'label': 'API密钥',
                        'type': 'password',
                        'placeholder': 'your-api-key',
                        'help': 'API认证密钥（如果需要）'
                    },
                    {
                        'name': 'platform_agent_id',
                        'label': '模型ID',
                        'type': 'text',
                        'placeholder': 'model-name',
                        'help': '要使用的模型标识符'
                    }
                ],
                'config_fields': [
                    {
                        'name': 'timeout',
                        'label': '超时时间（秒）',
                        'type': 'number',
                        'min': 5,
                        'max': 300,
                        'default': 30
                    },
                    {
                        'name': 'headers',
                        'label': '自定义请求头',
                        'type': 'json',
                        'placeholder': '{"Content-Type": "application/json"}',
                        'help': '额外的HTTP请求头（JSON格式）'
                    }
                ]
            }
        }
    
    @staticmethod
    def get_platform_config(platform: str) -> Dict[str, Any]:
        """获取指定平台的配置信息"""
        configs = AIConfigManager.get_platform_configs()
        return configs.get(platform, {})
    
    @staticmethod
    def validate_config(platform: str, config: Dict[str, Any]) -> List[str]:
        """验证平台配置"""
        errors = []
        platform_config = AIConfigManager.get_platform_config(platform)
        
        if not platform_config:
            errors.append(f"不支持的平台: {platform}")
            return errors
        
        # 检查必需字段
        required_fields = platform_config.get('required_fields', [])
        for field in required_fields:
            field_name = field['name']
            if field_name not in config or not config[field_name]:
                errors.append(f"缺少必需字段: {field['label']}")
        
        # 验证字段类型和值
        all_fields = required_fields + platform_config.get('optional_fields', []) + platform_config.get('config_fields', [])
        
        for field in all_fields:
            field_name = field['name']
            if field_name in config and config[field_name] is not None:
                value = config[field_name]
                field_type = field['type']
                
                if field_type == 'url' and not value.startswith(('http://', 'https://')):
                    errors.append(f"{field['label']} 必须是有效的URL")
                elif field_type == 'number':
                    try:
                        num_value = float(value)
                        if 'min' in field and num_value < field['min']:
                            errors.append(f"{field['label']} 不能小于 {field['min']}")
                        if 'max' in field and num_value > field['max']:
                            errors.append(f"{field['label']} 不能大于 {field['max']}")
                    except (ValueError, TypeError):
                        errors.append(f"{field['label']} 必须是有效的数字")
                elif field_type == 'json':
                    try:
                        import json
                        json.loads(value)
                    except (ValueError, TypeError):
                        errors.append(f"{field['label']} 必须是有效的JSON格式")
        
        return errors
    
    @staticmethod
    def get_default_config(platform: str) -> Dict[str, Any]:
        """获取平台的默认配置"""
        platform_config = AIConfigManager.get_platform_config(platform)
        default_config = {}
        
        config_fields = platform_config.get('config_fields', [])
        for field in config_fields:
            if 'default' in field:
                default_config[field['name']] = field['default']
        
        return default_config
