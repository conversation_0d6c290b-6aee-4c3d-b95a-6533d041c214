from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# API路由
router = DefaultRouter()
router.register(r'tags', views.AgentTagViewSet, basename='agent-tag')
router.register(r'', views.AgentViewSet, basename='agent')

urlpatterns = [
    # DRF路由 - 必须放在前面，让路由器优先处理
    path('', include(router.urls)),

    # 智能体管理API
    path('platforms/', views.PlatformListView.as_view(), name='platforms'),
    path('stats/', views.agent_stats_view, name='agent-stats'),
    path('<int:agent_id>/logs/', views.AgentConnectionLogView.as_view(), name='agent-logs'),
]
