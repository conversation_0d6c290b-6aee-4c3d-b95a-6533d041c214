from rest_framework import serializers
from .models import Agent, <PERSON><PERSON>ag, AgentConnectionLog


class AgentTagSerializer(serializers.ModelSerializer):
    """智能体标签序列化器"""
    
    class Meta:
        model = AgentTag
        fields = '__all__'


class AgentSerializer(serializers.ModelSerializer):
    """智能体序列化器"""

    tags = serializers.SerializerMethodField()
    tag_names = serializers.ListField(
        child=serializers.CharField(max_length=50),
        write_only=True,
        required=False,
        help_text="标签名称列表"
    )
    owner_username = serializers.CharField(source='owner.username', read_only=True)

    class Meta:
        model = Agent
        fields = '__all__'
        read_only_fields = ('owner', 'usage_count', 'success_rate', 'avg_response_time',
                           'created_at', 'updated_at', 'last_used_at')

    def get_tags(self, obj):
        """从 tag_list 字段获取标签"""
        return obj.tag_list or []

    def create(self, validated_data):
        tag_names = validated_data.pop('tag_names', [])
        validated_data['tag_list'] = tag_names
        agent = Agent.objects.create(**validated_data)
        return agent

    def update(self, instance, validated_data):
        tag_names = validated_data.pop('tag_names', None)

        # 更新基本字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # 更新标签
        if tag_names is not None:
            instance.tag_list = tag_names

        instance.save()
        return instance


class AgentCreateSerializer(serializers.ModelSerializer):
    """智能体创建序列化器"""
    
    tag_names = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        help_text="标签名称列表"
    )
    
    class Meta:
        model = Agent
        fields = ('name', 'description', 'avatar', 'platform', 'platform_agent_id',
                 'api_endpoint', 'api_key', 'config_data', 'is_public', 'allow_upload', 'tag_names')
    
    def create(self, validated_data):
        tag_names = validated_data.pop('tag_names', [])
        validated_data['owner'] = self.context['request'].user

        # 将标签名称列表直接存储到 tag_list 字段
        validated_data['tag_list'] = tag_names

        agent = Agent.objects.create(**validated_data)

        return agent


class AgentListSerializer(serializers.ModelSerializer):
    """智能体列表序列化器"""

    tags = serializers.SerializerMethodField()
    owner_username = serializers.CharField(source='owner.username', read_only=True)

    class Meta:
        model = Agent
        fields = ('id', 'name', 'description', 'avatar', 'platform', 'status',
                 'is_public', 'is_online', 'allow_upload', 'usage_count', 'success_rate',
                 'api_endpoint', 'platform_agent_id', 'tags', 'owner_username',
                 'created_at', 'updated_at')

    def get_tags(self, obj):
        """从 tag_list 字段获取标签"""
        return obj.tag_list or []


class AgentConnectionLogSerializer(serializers.ModelSerializer):
    """智能体连接日志序列化器"""
    
    agent_name = serializers.CharField(source='agent.name', read_only=True)
    
    class Meta:
        model = AgentConnectionLog
        fields = '__all__'
        read_only_fields = ('agent', 'created_at')


class AgentTestConnectionSerializer(serializers.Serializer):
    """智能体连接测试序列化器"""
    
    test_message = serializers.CharField(
        default="Hello, this is a connection test.",
        help_text="测试消息"
    )


class PlatformConfigSerializer(serializers.Serializer):
    """平台配置序列化器"""
    
    platform = serializers.ChoiceField(choices=Agent.PLATFORM_CHOICES)
    
    def to_representation(self, instance):
        platform = instance
        
        config_fields = {
            'dify': {
                'required_fields': ['api_endpoint', 'api_key', 'platform_agent_id'],
                'optional_fields': ['config_data'],
                'description': 'Dify平台智能体配置'
            },
            'ragflow': {
                'required_fields': ['api_endpoint', 'api_key'],
                'optional_fields': ['platform_agent_id', 'config_data'],
                'description': 'RagFlow平台智能体配置'
            },
            'openai': {
                'required_fields': ['api_key'],
                'optional_fields': ['api_endpoint', 'config_data'],
                'description': 'OpenAI平台智能体配置'
            },
            'custom': {
                'required_fields': ['api_endpoint'],
                'optional_fields': ['api_key', 'platform_agent_id', 'config_data'],
                'description': '自定义平台智能体配置'
            }
        }
        
        return {
            'platform': platform,
            'config': config_fields.get(platform, {})
        }



