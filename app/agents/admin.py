from django.contrib import admin
from .models import Agent, Agent<PERSON>ag, AgentConnectionLog


@admin.register(Agent)
class AgentAdmin(admin.ModelAdmin):
    """智能体管理"""

    list_display = ('name', 'platform', 'owner', 'status', 'is_public', 'is_online',
                   'usage_count', 'created_at')
    list_filter = ('platform', 'status', 'is_public', 'is_online', 'created_at')
    search_fields = ('name', 'description', 'owner__username')
    ordering = ('-created_at',)
    filter_horizontal = ('tags',)

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'avatar', 'owner', 'tags')
        }),
        ('平台配置', {
            'fields': ('platform', 'platform_agent_id', 'api_endpoint', 'api_key', 'config_data')
        }),
        ('状态信息', {
            'fields': ('status', 'is_public', 'is_online')
        }),
        ('统计信息', {
            'fields': ('usage_count', 'success_rate', 'avg_response_time', 'last_used_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('usage_count', 'success_rate', 'avg_response_time',
                      'created_at', 'updated_at', 'last_used_at')


@admin.register(AgentTag)
class AgentTagAdmin(admin.ModelAdmin):
    """智能体标签管理"""

    list_display = ('name', 'color', 'description', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('name',)


@admin.register(AgentConnectionLog)
class AgentConnectionLogAdmin(admin.ModelAdmin):
    """智能体连接日志管理"""

    list_display = ('agent', 'status', 'response_time', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('agent__name', 'error_message')
    ordering = ('-created_at',)
    readonly_fields = ('agent', 'status', 'response_time', 'error_message', 'created_at')

    def has_add_permission(self, request):
        return False  # 不允许手动添加日志

    def has_change_permission(self, request, obj=None):
        return False  # 不允许修改日志



