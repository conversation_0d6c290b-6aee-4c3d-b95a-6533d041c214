# Generated by Django 5.2.3 on 2025-06-20 06:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AgentTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='标签名')),
                ('color', models.CharField(default='#667eea', max_length=7, verbose_name='颜色')),
                ('description', models.CharField(blank=True, max_length=200, verbose_name='描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '智能体标签',
                'verbose_name_plural': '智能体标签',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Agent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='名称')),
                ('description', models.TextField(blank=True, max_length=500, verbose_name='描述')),
                ('avatar', models.CharField(default='🤖', max_length=10, verbose_name='头像')),
                ('platform', models.CharField(choices=[('dify', 'Dify'), ('ragflow', 'RagFlow'), ('openai', 'OpenAI'), ('custom', '自定义')], max_length=20, verbose_name='平台')),
                ('platform_agent_id', models.CharField(blank=True, max_length=200, verbose_name='平台智能体ID')),
                ('api_endpoint', models.URLField(blank=True, verbose_name='API端点')),
                ('api_key', models.CharField(blank=True, max_length=200, verbose_name='API密钥')),
                ('config_data', models.JSONField(blank=True, default=dict, verbose_name='配置数据')),
                ('status', models.CharField(choices=[('active', '激活'), ('inactive', '未激活'), ('error', '错误')], default='inactive', max_length=20, verbose_name='状态')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('is_online', models.BooleanField(default=False, verbose_name='是否在线')),
                ('usage_count', models.PositiveIntegerField(default=0, verbose_name='使用次数')),
                ('success_rate', models.FloatField(default=0.0, verbose_name='成功率')),
                ('avg_response_time', models.FloatField(default=0.0, verbose_name='平均响应时间(秒)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('last_used_at', models.DateTimeField(blank=True, null=True, verbose_name='最后使用时间')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='agents', to=settings.AUTH_USER_MODEL, verbose_name='所有者')),
                ('tags', models.ManyToManyField(blank=True, to='agents.agenttag', verbose_name='标签')),
            ],
            options={
                'verbose_name': '智能体',
                'verbose_name_plural': '智能体',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AgentConnectionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('success', '成功'), ('failed', '失败'), ('timeout', '超时')], max_length=20, verbose_name='状态')),
                ('response_time', models.FloatField(blank=True, null=True, verbose_name='响应时间(秒)')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='connection_logs', to='agents.agent', verbose_name='智能体')),
            ],
            options={
                'verbose_name': '连接日志',
                'verbose_name_plural': '连接日志',
                'ordering': ['-created_at'],
            },
        ),
    ]
