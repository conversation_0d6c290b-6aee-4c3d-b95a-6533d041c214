# Generated by Django 5.2.3 on 2025-06-22 17:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("agents", "0003_add_uuid_primary_keys"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="knowledgebase",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="knowledgebase",
            name="owner",
        ),
        migrations.AlterField(
            model_name="agent",
            name="platform",
            field=models.CharField(
                choices=[
                    ("dify", "Dify"),
                    ("ragflow", "RagFlow"),
                    ("openai", "OpenAI"),
                    ("custom", "自定义"),
                    ("url", "外部链接"),
                ],
                max_length=20,
                verbose_name="平台",
            ),
        ),
        migrations.DeleteModel(
            name="Knowledge",
        ),
        migrations.DeleteModel(
            name="KnowledgeTag",
        ),
        migrations.DeleteModel(
            name="KnowledgeBase",
        ),
    ]
