from django.db import models
from django.contrib.auth import get_user_model
import json
import uuid

User = get_user_model()


class Agent(models.Model):
    """智能体模型"""

    PLATFORM_CHOICES = [
        ('dify', 'Dify'),
        ('ragflow', 'RagFlow'),
        ('openai', 'OpenAI'),
        ('custom', '自定义'),
        ('url', '外部链接'),
    ]

    STATUS_CHOICES = [
        ('active', '激活'),
        ('inactive', '未激活'),
        ('error', '错误'),
    ]

    # 主键
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name='ID')

    # 基础信息
    name = models.CharField(max_length=100, verbose_name='名称')
    description = models.TextField(max_length=500, blank=True, verbose_name='描述')
    avatar = models.CharField(max_length=10, default='🤖', verbose_name='头像')

    # 平台信息
    platform = models.CharField(max_length=20, choices=PLATFORM_CHOICES, verbose_name='平台')
    platform_agent_id = models.CharField(max_length=200, blank=True, verbose_name='平台智能体ID')

    # 配置信息
    api_endpoint = models.URLField(blank=True, verbose_name='API端点')
    api_key = models.CharField(max_length=200, blank=True, verbose_name='API密钥')
    config_data = models.JSONField(default=dict, blank=True, verbose_name='配置数据')

    # 状态信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='inactive', verbose_name='状态')
    is_public = models.BooleanField(default=False, verbose_name='是否公开')
    is_online = models.BooleanField(default=False, verbose_name='是否在线')

    # 统计信息
    usage_count = models.PositiveIntegerField(default=0, verbose_name='使用次数')
    success_rate = models.FloatField(default=0.0, verbose_name='成功率')
    avg_response_time = models.FloatField(default=0.0, verbose_name='平均响应时间(秒)')

    # 关联信息
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='agents', verbose_name='所有者')
    tags = models.ManyToManyField('AgentTag', blank=True, verbose_name='标签(已废弃)')
    tag_list = models.JSONField(default=list, blank=True, verbose_name='标签列表')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    last_used_at = models.DateTimeField(null=True, blank=True, verbose_name='最后使用时间')

    class Meta:
        verbose_name = '智能体'
        verbose_name_plural = '智能体'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.name} ({self.platform})'

    def get_config(self):
        """获取配置数据"""
        return self.config_data

    def set_config(self, config):
        """设置配置数据"""
        self.config_data = config
        self.save()

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class AgentTag(models.Model):
    """智能体标签"""

    # 主键
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name='ID')

    name = models.CharField(max_length=50, unique=True, verbose_name='标签名')
    color = models.CharField(max_length=7, default='#667eea', verbose_name='颜色')
    description = models.CharField(max_length=200, blank=True, verbose_name='描述')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '智能体标签'
        verbose_name_plural = '智能体标签'
        ordering = ['name']

    def __str__(self):
        return self.name


class AgentConnectionLog(models.Model):
    """智能体连接日志"""

    STATUS_CHOICES = [
        ('success', '成功'),
        ('failed', '失败'),
        ('timeout', '超时'),
    ]

    # 主键
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name='ID')

    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, related_name='connection_logs', verbose_name='智能体')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, verbose_name='状态')
    response_time = models.FloatField(null=True, blank=True, verbose_name='响应时间(秒)')
    error_message = models.TextField(blank=True, verbose_name='错误信息')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '连接日志'
        verbose_name_plural = '连接日志'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.agent.name} - {self.status} ({self.created_at})'



