from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.shortcuts import get_object_or_404
from django.db import models
import time
import requests

from .models import Agent, AgentTag, AgentConnectionLog
from .serializers import (
    AgentSerializer,
    AgentCreateSerializer,
    AgentListSerializer,
    AgentTagSerializer,
    AgentConnectionLogSerializer,
    AgentTestConnectionSerializer
)


class AgentViewSet(viewsets.ModelViewSet):
    """智能体视图集"""

    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['platform', 'status', 'is_public', 'is_online']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'updated_at', 'usage_count', 'name']
    ordering = ['-created_at']

    def get_queryset(self):
        """获取查询集"""
        user = self.request.user
        if user.is_superuser:
            return Agent.objects.all()
        elif user.is_authenticated:
            # 认证用户可以看到所有智能体（包括公开和非公开的）
            return Agent.objects.all()
        else:
            # 匿名用户只能看到公开的智能体
            return Agent.objects.filter(is_public=True).distinct()

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return AgentCreateSerializer
        elif self.action == 'list':
            return AgentListSerializer
        return AgentSerializer

    def create(self, request, *args, **kwargs):
        """创建智能体"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # 创建智能体实例
        agent = serializer.save(owner=request.user)

        # 使用完整的序列化器返回数据
        response_serializer = AgentSerializer(agent)
        headers = self.get_success_headers(response_serializer.data)

        return Response(response_serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def perform_create(self, serializer):
        """创建智能体时设置所有者"""
        serializer.save(owner=self.request.user)

    def perform_update(self, serializer):
        """更新智能体"""
        # 只有所有者或管理员可以更新
        agent = self.get_object()
        if agent.owner != self.request.user and not self.request.user.is_superuser:
            raise permissions.PermissionDenied("您没有权限修改此智能体")
        serializer.save()

    def perform_destroy(self, instance):
        """删除智能体"""
        # 只有所有者或管理员可以删除
        if instance.owner != self.request.user and not self.request.user.is_superuser:
            raise permissions.PermissionDenied("您没有权限删除此智能体")
        instance.delete()

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """测试智能体连接"""
        agent = self.get_object()
        serializer = AgentTestConnectionSerializer(data=request.data)

        if serializer.is_valid():
            test_message = serializer.validated_data['test_message']

            # 记录开始时间
            start_time = time.time()

            try:
                # 根据平台类型进行连接测试
                success = self._test_agent_connection(agent, test_message)
                response_time = time.time() - start_time

                # 记录连接日志
                log_status = 'success' if success else 'failed'
                AgentConnectionLog.objects.create(
                    agent=agent,
                    status=log_status,
                    response_time=response_time
                )

                # 更新智能体在线状态
                agent.is_online = success
                agent.save(update_fields=['is_online'])

                return Response({
                    'success': success,
                    'response_time': response_time,
                    'message': '连接成功' if success else '连接失败'
                })

            except Exception as e:
                response_time = time.time() - start_time

                # 记录错误日志
                AgentConnectionLog.objects.create(
                    agent=agent,
                    status='failed',
                    response_time=response_time,
                    error_message=str(e)
                )

                # 更新智能体在线状态
                agent.is_online = False
                agent.save(update_fields=['is_online'])

                return Response({
                    'success': False,
                    'response_time': response_time,
                    'message': f'连接失败: {str(e)}'
                }, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _test_agent_connection(self, agent, test_message):
        """测试智能体连接的具体实现"""
        from .services import AIServiceFactory

        try:
            service = AIServiceFactory.create_service(agent)
            result = service.test_connection()
            return result['success']
        except Exception as e:
            print(f"测试连接失败: {str(e)}")
            return False


class AgentTagViewSet(viewsets.ModelViewSet):
    """智能体标签视图集"""

    queryset = AgentTag.objects.all()
    serializer_class = AgentTagSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering = ['name']


class PlatformListView(APIView):
    """平台列表视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取支持的平台列表"""
        from .ai_config import AIConfigManager

        platforms = []
        configs = AIConfigManager.get_platform_configs()

        for platform_code, platform_name in Agent.PLATFORM_CHOICES:
            platform_config = configs.get(platform_code, {})
            platforms.append({
                'code': platform_code,
                'name': platform_name,
                'description': platform_config.get('description', ''),
                'icon': platform_config.get('icon', '🤖'),
                'config': platform_config
            })

        return Response(platforms)


class AgentConnectionLogView(APIView):
    """智能体连接日志视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, agent_id):
        """获取智能体连接日志"""
        agent = get_object_or_404(Agent, id=agent_id)

        # 检查权限
        if agent.owner != request.user and not request.user.is_superuser:
            raise permissions.PermissionDenied("您没有权限查看此智能体的日志")

        logs = AgentConnectionLog.objects.filter(agent=agent).order_by('-created_at')[:50]
        serializer = AgentConnectionLogSerializer(logs, many=True)

        return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def agent_stats_view(request):
    """智能体统计视图"""

    user = request.user

    # 用户的智能体统计
    user_agents = Agent.objects.filter(owner=user)

    stats = {
        'total_agents': user_agents.count(),
        'online_agents': user_agents.filter(is_online=True).count(),
        'public_agents': user_agents.filter(is_public=True).count(),
        'platform_distribution': {},
        'recent_activity': []
    }

    # 平台分布统计
    for platform_code, platform_name in Agent.PLATFORM_CHOICES:
        count = user_agents.filter(platform=platform_code).count()
        if count > 0:
            stats['platform_distribution'][platform_name] = count

    # 最近活动
    recent_logs = AgentConnectionLog.objects.filter(
        agent__owner=user
    ).order_by('-created_at')[:10]

    stats['recent_activity'] = AgentConnectionLogSerializer(recent_logs, many=True).data

    return Response(stats)



