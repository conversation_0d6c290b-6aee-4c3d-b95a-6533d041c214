"""
URL configuration for agent_portal project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

urlpatterns = [
    path('admin/', admin.site.urls),

    # API URLs
    path('api/auth/', include('accounts.urls')),
    path('api/agents/', include('agents.urls')),
    path('api/conversations/', include('conversations.urls')),
    path('api/knowledge-bases/', include('knowledge_bases.urls')),

    # Frontend URLs
    path('', include('core.urls')),
    path('search/', include('search.urls')),
]

# 提供媒体文件服务
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# 提供静态文件服务（强制在所有环境下启用）
from django.views.static import serve
from django.urls import re_path
import os

# 强制提供静态文件服务，无论DEBUG设置如何
urlpatterns += [
    re_path(r'^static/(?P<path>.*)$', serve, {
        'document_root': settings.STATICFILES_DIRS[0],
    }),
]
