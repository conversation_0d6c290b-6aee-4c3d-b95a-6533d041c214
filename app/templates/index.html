{% extends 'base.html' %}

{% block title %}{{ system_settings.site_name }} - {{ system_settings.site_title }}{% endblock %}

{% block extra_css %}

/* Ant Design X 首页样式 */
main {
    padding-top: 0 !important;
}

.hero-section {
    background: var(--antdx-gradient-primary);
    min-height: 50vh;
    display: flex;
    align-items: center;
    color: white;
    position: relative;
    overflow: hidden;
    margin-top: 0;
    padding-top: 76px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.05) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.05) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Ant Design X 风格的搜索框 */
.search-container {
    max-width: 800px;
    margin: 0 auto;
}

.search-input {
    height: 56px;
    font-size: 16px;
    border-radius: var(--antdx-radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0 24px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--antdx-shadow-md);
    color: var(--antdx-text-primary);
    transition: all 0.2s ease;
}

.search-input:focus {
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: var(--antdx-shadow-lg), 0 0 0 2px rgba(255, 255, 255, 0.2);
    outline: none;
}

.search-btn {
    height: 56px;
    padding: 0 24px;
    border-radius: 0 var(--antdx-radius-lg) var(--antdx-radius-lg) 0;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 500;
    font-size: 16px;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--antdx-shadow-md);
}



/* Ant Design X 风格的模型快速访问卡片 */
.model-quick-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--antdx-radius-xl);
    padding: 12px 20px;
    text-decoration: none;
    color: var(--antdx-text-primary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 160px;
    box-shadow: var(--antdx-shadow-sm);
    font-weight: 500;
}

.model-quick-card:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(255, 255, 255, 0.4);
    color: var(--antdx-primary);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--antdx-shadow-md);
}

.model-quick-card .model-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--antdx-gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    font-weight: 500;
}

.model-quick-card .model-info h6 {
    margin: 0;
    font-weight: 500;
    font-size: 14px;
    color: var(--antdx-text-primary);
}

.model-quick-card .model-info small {
    color: var(--antdx-text-secondary);
    font-size: 12px;
}

/* Ant Design X 风格的智能体卡片 */
.agent-card {
    transition: all 0.2s ease;
    cursor: pointer;
    height: 100%;
    border: 1px solid var(--antdx-border-light);
    border-radius: var(--antdx-radius-xl);
    background: var(--antdx-bg-elevated);
    box-shadow: var(--antdx-shadow-sm);
}

.agent-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--antdx-shadow-md);
    border-color: var(--antdx-border);
}

.agent-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: var(--antdx-gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin: 0 auto 16px;
    color: white;
}

.platform-badge {
    font-size: 12px;
    padding: 4px 12px;
    border-radius: var(--antdx-radius-xl);
    font-weight: 500;
}

/* Ant Design X 风格的标签 */
.tag-pill {
    background: var(--antdx-primary-bg);
    color: var(--antdx-primary);
    border: 1px solid var(--antdx-border);
    border-radius: var(--antdx-radius-xl);
    padding: 4px 12px;
    text-decoration: none;
    display: inline-block;
    margin: 4px;
    transition: all 0.2s ease;
    font-size: 12px;
    font-weight: 500;
}

.tag-pill:hover {
    background: var(--antdx-primary);
    color: white;
    border-color: var(--antdx-primary);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--antdx-shadow-sm);
}

/* 筛选按钮样式 */
.category-filter .btn {
    border-radius: var(--antdx-radius-xl);
    padding: 8px 16px;
    margin: 0 4px 8px 0;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
}

.category-filter .btn-outline-primary {
    border-color: var(--antdx-border);
    color: var(--antdx-text-secondary);
    background: var(--antdx-bg-elevated);
}

.category-filter .btn-outline-primary:hover,
.category-filter .btn-outline-primary.active {
    background: var(--antdx-primary);
    border-color: var(--antdx-primary);
    color: white;
    box-shadow: var(--antdx-shadow-sm);
}

/* Ant Design X 风格的marketplace区域 */
.marketplace-section {
    background: var(--antdx-bg-layout);
    padding: 64px 0;
}

.status-online {
    width: 8px;
    height: 8px;
    background: #52c41a;
    border-radius: 50%;
    display: inline-block;
    margin-left: 8px;
    position: relative;
}

.status-online::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    width: 12px;
    height: 12px;
    background: #52c41a;
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.status-offline {
    width: 8px;
    height: 8px;
    background: var(--antdx-text-tertiary);
    border-radius: 50%;
    display: inline-block;
    margin-left: 8px;
}

@keyframes pulse {
    0% {
        transform: scale(0.8);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
    100% {
        transform: scale(0.8);
        opacity: 0.3;
    }
}
{% endblock %}

{% block content %}


    <!-- Hero区域 -->
    <section class="hero-section">
        <div class="container hero-content">
            <div class="text-center">
                <h1 class="display-3 fw-bold mb-4">{{ system_settings.site_title }}</h1>
                <p class="lead mb-5 opacity-90">
                    {{ system_settings.site_description }}
                </p>
                
                {% if system_settings.enable_search %}
                <div class="search-container mb-5">
                    <div class="input-group">
                        <input type="text" class="form-control search-input"
                               placeholder="输入您的问题，开始与AI对话..."
                               id="searchInput"
                               onkeypress="if(event.key==='Enter') handleSearch()">
                        <button class="btn search-btn" type="button" onclick="handleSearch()">
                            <i class="bi bi-search me-2"></i>搜索
                        </button>
                    </div>
                </div>
                {% endif %}
                
                <!-- 大模型快速聊天入口 -->
                <div class="d-flex flex-wrap justify-content-center gap-3" id="modelQuickAccess">
                    <!-- 大模型聊天入口将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </section>

    <!-- 智能体广场 -->
    <section class="marketplace-section">
        <div class="container">
            <div class="row align-items-center mb-5">
                <div class="col-md-8">
                    <h2 class="fw-bold mb-3">
                        <i class="bi bi-fire text-warning me-2"></i>智能体广场
                    </h2>
                    <p class="text-muted">发现和使用各种AI智能体，提升您的工作效率</p>
                </div>
            </div>

            <!-- 标签筛选 -->
            <div class="category-filter mb-4" id="tagFilter">
                <button class="btn btn-outline-primary active" onclick="filterAgentsByTag('all')">全部</button>
                <!-- 动态标签按钮将在这里生成 -->
            </div>

            <!-- 智能体网格 -->
            <div class="row g-4" id="agentGrid">
                <!-- 智能体卡片将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

{% endblock %}

{% block extra_js %}
    <script>
        // 智能体数据
        let agents = [];

        // 获取平台颜色
        function getPlatformColor(platform) {
            const colors = {
                'ragflow': 'primary',
                'RagFlow': 'primary',
                'dify': 'success',
                'Dify': 'success',
                'openai': 'info',
                'OpenAI': 'info',
                'custom': 'warning',
                'Custom': 'warning'
            };
            return colors[platform] || 'secondary';
        }

        // 渲染智能体卡片
        function renderAgents(agentsToRender = agents) {
            const grid = document.getElementById('agentGrid');
            grid.innerHTML = agentsToRender.map(agent => `
                <div class="col-lg-4 col-md-6">
                    <div class="card agent-card glass-card h-100" onclick="goToChat('${agent.id}')">
                        <div class="card-body text-center p-4">
                            <div class="agent-avatar">${agent.avatar || '🤖'}</div>
                            <h5 class="card-title fw-bold mb-2">${agent.name}</h5>
                            <span class="badge bg-${getPlatformColor(agent.platform)} platform-badge mb-3">
                                ${agent.platform}
                                <span class="${agent.is_online ? 'status-online' : 'status-offline'}"></span>
                            </span>
                            <p class="card-text text-muted mb-3">${agent.description}</p>
                            <div class="mb-3">
                                ${(agent.tags || []).slice(0, 3).map(tag =>
                                    `<span class="badge bg-light text-primary me-1">${typeof tag === 'string' ? tag : tag.name}</span>`
                                ).join('')}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-graph-up me-1"></i>
                                使用次数: ${(agent.usage_count || 0).toLocaleString()}
                            </small>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 按标签筛选智能体
        function filterAgentsByTag(tagName) {
            // 更新按钮状态
            document.querySelectorAll('.category-filter .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 筛选数据
            const filtered = tagName === 'all'
                ? agents
                : agents.filter(agent => {
                    if (!agent.tags || agent.tags.length === 0) return false;
                    return agent.tags.some(tag => {
                        const tagValue = typeof tag === 'string' ? tag : tag.name;
                        return tagValue === tagName;
                    });
                });

            renderAgents(filtered);
        }

        // 搜索功能
        function handleSearch() {
            const searchInput = document.getElementById('searchInput');
            if (!searchInput) return; // 如果搜索输入框不存在，直接返回

            const query = searchInput.value;
            if (query.trim()) {
                window.location.href = `/search/?q=${encodeURIComponent(query)}`;
            }
        }

        // 跳转到聊天页面或外部链接
        function goToChat(agentId) {
            // 查找对应的智能体
            const agent = agents.find(a => a.id === agentId);

            if (!agent) {
                alert('智能体不存在');
                return;
            }

            if (agent.platform === 'url') {
                // 如果是URL类型，直接在新标签页打开外部链接
                if (agent.api_endpoint) {
                    window.open(agent.api_endpoint, '_blank');
                } else {
                    alert('该智能体的目标URL未配置');
                }
                return;
            }

            // 检查智能体是否公开，如果不公开则需要用户登录
            const token = localStorage.getItem('token');
            const isLoggedIn = token && token.trim() !== '';

            if (!agent.is_public && !isLoggedIn) {
                // 智能体不公开且用户未登录，提示登录
                if (confirm('该智能体需要登录后才能使用，是否前往登录？')) {
                    window.location.href = '/auth/login/';
                }
                return;
            }

            // 其他类型跳转到聊天页面
            window.location.href = `{% url 'chat' %}?agent=${agentId}`;
        }

        // 回车搜索（仅在搜索输入框存在时添加事件监听器）
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleSearch();
                }
            });
        }

        // 从智能体列表中提取标签
        function extractTagsFromAgents(agentList) {
            const tagSet = new Set();

            agentList.forEach(agent => {
                if (agent.tags && Array.isArray(agent.tags)) {
                    agent.tags.forEach(tag => {
                        const tagName = typeof tag === 'string' ? tag : tag.name;
                        if (tagName && tagName.trim()) {
                            tagSet.add(tagName.trim());
                        }
                    });
                }
            });

            // 转换为数组并排序
            return Array.from(tagSet).sort();
        }

        // 渲染标签筛选按钮
        function renderTagFilter(tags) {
            const tagFilter = document.getElementById('tagFilter');
            const allButton = tagFilter.querySelector('.btn.active'); // 保留"全部"按钮

            // 清除除了"全部"按钮之外的所有按钮
            const existingButtons = tagFilter.querySelectorAll('.btn:not(.active)');
            existingButtons.forEach(btn => btn.remove());

            // 添加标签按钮
            tags.forEach(tag => {
                const tagName = typeof tag === 'string' ? tag : tag.name;
                const button = document.createElement('button');
                button.className = 'btn btn-outline-primary';
                button.textContent = tagName;
                button.onclick = () => filterAgentsByTag(tagName);
                tagFilter.appendChild(button);
            });
        }

        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('token');
            const headers = {
                'Content-Type': 'application/json',
            };

            if (token && token.trim() !== '') {
                headers['Authorization'] = `Token ${token}`;
            }

            return headers;
        }

        // 从API获取智能体数据
        async function loadAgentsFromAPI() {
            try {
                console.log('正在从API加载智能体数据...');
                const response = await fetch(`${API_BASE_URL}/agents/`, {
                    method: 'GET',
                    headers: getAuthHeaders()
                });
                if (response.ok) {
                    const data = await response.json();
                    console.log('API返回数据:', data);

                    // 处理分页结果
                    const agentList = data.results || data;
                    if (Array.isArray(agentList) && agentList.length > 0) {
                        agents = agentList;
                        console.log('成功加载智能体:', agents.length, '个');
                        renderAgents();
                        // 从智能体中提取标签并渲染标签筛选器
                        const tags = extractTagsFromAgents(agents);
                        console.log('从智能体中提取的标签:', tags);
                        renderTagFilter(tags);
                    } else {
                        console.log('API返回空数据');
                        // 显示空状态
                        renderAgents();
                        // 清空标签筛选器（只保留"全部"按钮）
                        renderTagFilter([]);
                    }
                } else {
                    console.error('API请求失败:', response.status, response.statusText);
                    // 显示空状态
                    renderAgents();
                }
            } catch (error) {
                console.error('加载智能体数据时出错:', error);
                // 显示空状态
                renderAgents();
            }
        }

        // 加载大模型列表
        async function loadLLMProviders() {
            try {
                console.log('正在加载大模型列表...');
                // 使用enabled端点，会根据用户登录状态返回相应的大模型
                const response = await fetch(`${API_BASE_URL}/llm-providers/enabled/`, {
                    method: 'GET',
                    headers: getAuthHeaders()
                });
                if (response.ok) {
                    const providers = await response.json();
                    console.log('成功加载大模型:', providers.length, '个');

                    // 渲染快速聊天入口
                    renderModelQuickAccess(providers);
                } else {
                    console.error('加载大模型失败:', response.status);
                }
            } catch (error) {
                console.error('加载大模型时出错:', error);
            }
        }

        // 渲染大模型快速聊天入口
        function renderModelQuickAccess(providers) {
            const container = document.getElementById('modelQuickAccess');

            if (providers.length === 0) {
                container.innerHTML = '';
                return;
            }

            // 最多显示4个大模型
            const displayProviders = providers.slice(0, 4);

            const quickAccessHtml = displayProviders.map(provider => {
                const providerIcon = getProviderIcon(provider.provider);
                const providerName = getProviderDisplayName(provider.provider);

                return `
                    <a href="/chat/?model=${provider.id}" class="model-quick-card">
                        <div class="model-icon">
                            ${providerIcon}
                        </div>
                        <div class="model-info">
                            <h6>${provider.name}</h6>
                            <small>${providerName}</small>
                        </div>
                    </a>
                `;
            }).join('');

            container.innerHTML = quickAccessHtml;
        }

        // 获取提供商图标
        function getProviderIcon(provider) {
            const iconMap = {
                'qwen': '千',
                'deepseek': 'DS',
                'custom': '🤖'
            };
            return iconMap[provider] || provider.charAt(0).toUpperCase();
        }

        // 获取提供商显示名称
        function getProviderDisplayName(provider) {
            const providerMap = {
                'qwen': '阿里云千问',
                'deepseek': 'DeepSeek',
                'custom': '自定义'
            };
            return providerMap[provider] || provider;
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadAgentsFromAPI();
            loadLLMProviders();
        });
    </script>
{% endblock %}
