{% extends 'base.html' %}

{% block title %}AI对话 - Agent Portal{% endblock %}

{% block extra_css %}
/* Ant Design X 主题配色 */
:root {
    /* Ant Design X 主色调 */
    --antdx-primary: #1677ff;
    --antdx-primary-hover: #4096ff;
    --antdx-primary-active: #0958d9;

    /* 聊天气泡配色 */
    --antdx-user-bg: #1677ff;
    --antdx-assistant-bg: #f5f5f5;
    --antdx-user-text: #ffffff;
    --antdx-assistant-text: #262626;

    /* 边框和分割线 */
    --antdx-border: #d9d9d9;
    --antdx-border-light: #f0f0f0;

    /* 背景色 */
    --antdx-bg-container: #ffffff;
    --antdx-bg-layout: #f5f5f5;
    --antdx-bg-elevated: #ffffff;

    /* 文字颜色 */
    --antdx-text-primary: #262626;
    --antdx-text-secondary: #8c8c8c;
    --antdx-text-tertiary: #bfbfbf;

    /* 阴影 */
    --antdx-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
    --antdx-shadow-md: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

    /* 圆角 */
    --antdx-radius-sm: 4px;
    --antdx-radius-md: 6px;
    --antdx-radius-lg: 8px;
    --antdx-radius-xl: 16px;
}

body {
    height: 100vh;
    overflow: hidden;
    margin: 0;
    padding: 0;
    background: var(--antdx-bg-layout);
    color: var(--antdx-text-primary);
}

.chat-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--antdx-bg-layout);
}

.chat-header {
    background: var(--antdx-bg-elevated);
    border-bottom: 1px solid var(--antdx-border-light);
    padding: 16px 0;
    flex-shrink: 0;
    box-shadow: var(--antdx-shadow-sm);
}

/* 防止标题和标签闪现的过渡效果 */
#currentAgentName, #currentAgentPlatform {
    transition: opacity 0.2s ease;
}

.agent-card-chat {
    transition: opacity 0.2s ease;
}

.chat-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.chat-sidebar {
    width: 280px;
    background: var(--antdx-bg-elevated);
    border-right: 1px solid var(--antdx-border-light);
    padding: 24px;
    overflow-y: auto;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--antdx-bg-container);
    position: relative;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    position: relative;
    z-index: 1;
    min-height: 0;
    background: var(--antdx-bg-container);
}

.chat-input-area {
    background: var(--antdx-bg-elevated);
    border-top: 1px solid var(--antdx-border-light);
    padding: 16px 24px;
    position: relative;
    z-index: 10;
    flex-shrink: 0;
    box-shadow: var(--antdx-shadow-sm);
}



/* Ant Design X 风格的消息气泡 */
.message-bubble {
    max-width: 70%;
    margin-bottom: 16px;
    animation: messageSlideIn 0.3s ease-out;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.message-bubble.user {
    margin-left: auto;
    flex-direction: row-reverse;
}

.message-content {
    background: var(--antdx-assistant-bg);
    border-radius: var(--antdx-radius-xl);
    padding: 12px 16px;
    box-shadow: var(--antdx-shadow-sm);
    border: 1px solid var(--antdx-border-light);
    color: var(--antdx-assistant-text);
    position: relative;
    max-width: 100%;
    word-wrap: break-word;
    line-height: 1.5;
}

.message-bubble.user .message-content {
    background: var(--antdx-user-bg);
    color: var(--antdx-user-text);
    border-color: var(--antdx-primary);
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 500;
}

.user-avatar {
    background: var(--antdx-primary);
    color: white;
}

.assistant-avatar {
    background: var(--antdx-bg-elevated);
    color: var(--antdx-primary);
    border: 1px solid var(--antdx-border);
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    gap: 8px;
    font-size: 12px;
    color: var(--antdx-text-secondary);
}

.message-header.user {
    justify-content: flex-end;
    flex-direction: row-reverse;
}

.message-name {
    font-weight: 500;
    font-size: 12px;
    color: var(--antdx-text-secondary);
}

.message-time {
    font-size: 11px;
    color: var(--antdx-text-tertiary);
    margin-top: 4px;
}

/* Ant Design X 风格的智能体卡片 */
.agent-card-chat {
    background: var(--antdx-bg-elevated);
    color: var(--antdx-text-primary);
    border-radius: var(--antdx-radius-lg);
    padding: 16px;
    margin-bottom: 24px;
    text-align: center;
    border: 1px solid var(--antdx-border-light);
    box-shadow: var(--antdx-shadow-sm);
}

/* Ant Design X 风格的会话列表 */
.conversation-item {
    padding: 12px;
    border-radius: var(--antdx-radius-md);
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    background: var(--antdx-bg-container);
}

.conversation-item:hover {
    background: var(--antdx-bg-elevated);
    border-color: var(--antdx-border);
    box-shadow: var(--antdx-shadow-sm);
}

.conversation-item.active {
    background: rgba(22, 119, 255, 0.06);
    border-color: var(--antdx-primary);
    color: var(--antdx-primary);
}

/* Ant Design X 风格的输入框 */
.chat-input {
    border-radius: var(--antdx-radius-md);
    border: 1px solid var(--antdx-border) !important;
    background: var(--antdx-bg-container) !important;
    padding: 8px 12px;
    resize: none !important;
    min-height: 40px;
    max-height: 120px;
    font-size: 14px;
    width: 100%;
    z-index: 20;
    position: relative;
    line-height: 1.5;
    overflow-y: hidden;
    transition: all 0.2s ease;
    box-sizing: border-box;
    color: var(--antdx-text-primary);
}

.chat-input:focus {
    background: var(--antdx-bg-container) !important;
    border-color: var(--antdx-primary) !important;
    box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
    outline: none;
}

.chat-input::placeholder {
    color: var(--antdx-text-tertiary);
}

.send-btn {
    background: var(--antdx-primary);
    border: none;
    border-radius: var(--antdx-radius-md);
    color: white;
    padding: 8px 16px;
    margin-left: 8px;
    min-width: 64px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    position: relative;
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
}

.send-btn:hover {
    background: var(--antdx-primary-hover);
    color: white;
    transform: none;
    box-shadow: var(--antdx-shadow-sm);
}

.send-btn:active {
    background: var(--antdx-primary-active);
    transform: translateY(1px);
}

.send-btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--antdx-text-secondary) !important;
    border-color: var(--antdx-text-secondary) !important;
    transition: all 0.2s ease;
}

.send-btn.disabled:hover {
    background: var(--antdx-text-secondary) !important;
    transform: none;
    box-shadow: none;
}

.send-btn:disabled {
    pointer-events: none;
}

/* 文件上传按钮样式 */
.upload-btn {
    background: var(--antdx-bg-container);
    border: 1px solid var(--antdx-border-color);
    border-radius: var(--antdx-radius-md);
    color: var(--antdx-text);
    padding: 8px 16px;
    min-width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
}

.upload-btn:hover {
    background: var(--antdx-bg-container-hover);
    border-color: var(--antdx-primary);
    color: var(--antdx-primary);
    transform: none;
    box-shadow: var(--antdx-shadow-sm);
}

.upload-btn:active {
    background: var(--antdx-bg-container-active);
    transform: translateY(1px);
}

.upload-btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--antdx-bg-container-disabled) !important;
    border-color: var(--antdx-border-color-disabled) !important;
    color: var(--antdx-text-disabled) !important;
}

.upload-btn.uploading {
    background: var(--antdx-primary-bg);
    border-color: var(--antdx-primary);
    color: var(--antdx-primary);
}

/* 上传按钮旋转动画 */
.upload-btn.uploading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 已上传文件显示样式 */
.uploaded-file-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: var(--antdx-primary-bg);
    border: 1px solid var(--antdx-primary-border);
    border-radius: var(--antdx-radius-sm);
    font-size: 11px;
    color: var(--antdx-primary);
    max-width: 150px;
    animation: slideInUp 0.3s ease;
}

.uploaded-file-item .file-icon {
    font-size: 12px;
}

.uploaded-file-item .file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
}

.uploaded-file-item .remove-file {
    cursor: pointer;
    padding: 1px;
    border-radius: 2px;
    transition: all 0.2s ease;
    font-size: 10px;
}

.uploaded-file-item .remove-file:hover {
    background: var(--antdx-error-bg);
    color: var(--antdx-error);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 消息中的文件显示样式 */
.message-files {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.message-file-item {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 3px 6px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: var(--antdx-radius-sm);
    font-size: 11px;
    color: var(--antdx-text-secondary);
    max-width: 120px;
}

.user .message-file-item {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
}

.message-file-item .file-icon {
    font-size: 12px;
    flex-shrink: 0;
}

.message-file-item .file-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
}

/* Ant Design X 风格的动画效果 */
.typing-indicator {
    padding: 8px 0;
}

.typing-cursor {
    animation: blink 1s infinite;
    font-weight: 500;
    color: var(--antdx-primary);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ant Design X 风格的加载动画 */
@keyframes antdx-fade-in {
    from {
        opacity: 0;
        transform: translateY(4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-bubble {
    animation: antdx-fade-in 0.2s ease-out;
}



/* Ant Design X 风格的按钮 */
.btn-gradient {
    background: var(--antdx-primary);
    border: 1px solid var(--antdx-primary);
    color: white;
    border-radius: var(--antdx-radius-md);
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-gradient:hover {
    background: var(--antdx-primary-hover);
    border-color: var(--antdx-primary-hover);
    color: white;
    box-shadow: var(--antdx-shadow-sm);
}

.btn-outline-primary {
    border-color: var(--antdx-primary);
    color: var(--antdx-primary);
    background: transparent;
    border-radius: var(--antdx-radius-md);
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-outline-primary:hover {
    background: var(--antdx-primary);
    border-color: var(--antdx-primary);
    color: white;
}

.btn-outline-secondary {
    border-color: var(--antdx-border);
    color: var(--antdx-text-secondary);
    background: transparent;
    border-radius: var(--antdx-radius-md);
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-outline-secondary:hover {
    background: var(--antdx-bg-elevated);
    border-color: var(--antdx-border);
    color: var(--antdx-text-primary);
}

        /* 会话历史样式 */
        .conversation-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            background: rgba(255, 255, 255, 0.05);
        }

        .conversation-item:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
            color: #667eea;
        }

        .conversation-item.active {
            background: rgba(13, 110, 253, 0.1);
            border-color: rgba(13, 110, 253, 0.3);
        }

        .conversation-item .dropdown-toggle {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .conversation-item:hover .dropdown-toggle {
            opacity: 1;
        }

@media (max-width: 768px) {
    .chat-sidebar {
        display: none;
    }

    .chat-layout {
        height: 100vh;
        top: 0;
    }

    .chat-messages {
        padding: 1rem;
    }

    .chat-input-area {
        padding: 1rem;
        min-height: 100px;
    }
}
{% endblock %}

{% block content %}
{% csrf_token %}
    <!-- 隐藏导航栏 -->
    <style>
        nav.navbar {
            display: none !important;
        }
        main {
            padding-top: 0 !important;
        }
    </style>
    <div class="chat-layout">
        <!-- 顶部工具栏 -->
        <div class="chat-header">
            <div class="container-fluid">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-secondary me-3" onclick="window.location.href='{% url 'home' %}'">
                            <i class="bi bi-house"></i> 返回首页
                        </button>
                        <button class="btn btn-outline-primary me-3" onclick="startNewSession()" title="开始新会话">
                            <i class="bi bi-arrow-clockwise"></i> 新会话
                        </button>
                        <h4 class="mb-0 fw-bold">与 <span id="currentAgentName">{% if current_item_name %}{{ current_item_name }}{% else %}AI助手{% endif %}</span> 对话</h4>
                        <span class="badge {% if chat_mode == 'model' %}bg-info{% elif error %}bg-danger{% else %}bg-primary{% endif %} ms-2" id="currentAgentPlatform">{% if current_item_platform %}{{ current_item_platform }}{% else %}智能助手{% endif %}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="chat-body {% if chat_mode == 'model' %}model-mode{% endif %}">
            <!-- 左侧边栏 -->
            <div class="chat-sidebar" {% if chat_mode == 'model' %}style="display: none;"{% endif %}>
                <button class="btn btn-gradient w-100 mb-3" onclick="createNewConversation()">
                    <i class="bi bi-plus-lg me-2"></i>新建对话
                </button>

                <!-- 当前智能体 -->
                <div class="agent-card-chat" id="currentAgentCard">
                    {% if current_item and chat_mode == 'agent' %}
                        <div class="fs-2 mb-2">{{ current_item.avatar|default:"🤖" }}</div>
                        <h6 class="fw-bold mb-1">{{ current_item.name }}</h6>
                        <small class="opacity-75">{{ current_item.description|default:"智能对话助手" }}</small>
                        <div class="mt-2">
                            <span class="badge bg-light text-dark">在线</span>
                        </div>
                    {% elif error %}
                        <div class="text-center py-3">
                            <div class="fs-2 mb-2">⚠️</div>
                            <h6 class="fw-bold mb-1 text-warning">访问受限</h6>
                            <small class="opacity-75">{{ error }}</small>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <div class="spinner-border spinner-border-sm text-light" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2 small">加载中...</div>
                        </div>
                    {% endif %}
                </div>

                <!-- 智能体切换 -->
                <div class="mb-3">
                    <label class="form-label fw-bold text-muted">切换智能体</label>
                    <select class="form-select" id="agentSelect">
                        <!-- 智能体选项将从服务器加载 -->
                    </select>
                </div>



                <hr>

                <!-- 对话设置 -->
                <div class="mb-3" id="conversationSettings">
                    <h6 class="fw-bold text-muted mb-3">
                        <i class="bi bi-gear me-2"></i>对话设置
                    </h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-danger btn-sm" onclick="clearCurrentConversationHistory()">
                            <i class="bi bi-trash me-2"></i>清空历史
                        </button>
                    </div>
                </div>

                <hr>

                <!-- 对话历史 -->
                <div>
                    <h6 class="fw-bold text-muted mb-3">
                        <i class="bi bi-clock-history me-2"></i>对话历史
                    </h6>
                    <div id="conversationHistory">
                        <!-- 对话历史将从服务器加载 -->
                        <div class="text-center py-3 text-muted" id="conversationHistoryLoading">
                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                            加载中...
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主对话区域 -->
            <div class="chat-main">
                <div class="chat-messages" id="chatMessages">
                    <!-- 消息将通过JavaScript动态添加 -->
                </div>

                <!-- 输入区域 -->
                <div class="chat-input-area">
                    <div class="d-flex align-items-start gap-2" style="width: 100%;">
                        <div class="flex-grow-1 position-relative">
                            <textarea class="form-control chat-input"
                                      id="messageInput"
                                      placeholder="输入您的问题..."
                                      rows="1"></textarea>
                            <div class="mt-1" style="font-size: 12px; color: var(--antdx-text-tertiary);">
                                <i class="bi bi-info-circle me-1"></i>
                                支持多行输入 • Shift+Enter 换行 • Enter 发送
                                <span id="fileUploadHint" style="display: none;"> • 支持文件上传</span>
                            </div>
                            <!-- 已上传文件显示区域 -->
                            <div id="uploadedFilesArea" class="mt-2" style="display: none;">
                                <div class="d-flex flex-wrap gap-2" id="uploadedFilesList">
                                    <!-- 已上传的文件将在这里显示 -->
                                </div>
                            </div>
                        </div>
                        <!-- 文件上传按钮 -->
                        <div class="d-flex gap-1">
                            <input type="file" id="fileInput" style="display: none;" multiple
                                   accept=".txt,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.webp,.bmp,.md,.csv,.json,.xml,.html">
                            <button class="btn upload-btn" onclick="triggerFileUpload()" title="上传文件" style="margin-top: 0;">
                                <i class="bi bi-paperclip"></i>
                            </button>
                            <button class="btn send-btn" onclick="sendMessage()" title="发送消息 (Enter)" style="margin-top: 0;">
                                <i class="bi bi-send"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

{% endblock %}

{% block extra_js %}
<script>
        // 从服务器端获取的数据
        const serverData = {
            chatMode: '{{ chat_mode|default:"" }}',
            modelId: '{{ model_id|default:"" }}',
            agentId: '{{ agent_id|default:"" }}',
            query: '{{ query|default:"" }}',
            currentItemName: '{{ current_item_name|default:"" }}',
            currentItemPlatform: '{{ current_item_platform|default:"" }}',
            hasError: '{{ error|default:"" }}' !== '',
            errorMessage: '{{ error|default:"" }}'
        };

        // 智能体数据将从服务器加载
        let agents = {};
        let agentsList = [];
        let llmProviders = [];


        // 聊天模式：'agent' 或 'model'
        let chatMode = serverData.chatMode || 'agent';
        let currentModelId = serverData.modelId || null;
        let currentModelProvider = null;

        let currentAgent = null;
        let currentUser = null;
        let isTyping = false;
        let currentConversationId = null;

        // 删除WebSocket相关代码，使用SSE替代

        // 删除WebSocket消息处理，使用SSE替代

        // 获取当前用户信息
        async function loadCurrentUser() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    currentUser = null;
                    return;
                }

                const response = await fetch(`${API_BASE_URL}/auth/profile/`, {
                    headers: {
                        'Authorization': `Token ${token}`
                    }
                });

                if (response.ok) {
                    currentUser = await response.json();
                    console.log('当前用户信息:', currentUser);
                } else {
                    currentUser = null;
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                currentUser = null;
            }
        }

        // 初始化页面
        async function initPage() {
            try {
                // 清理可能残留的打字指示器
                const existingTypingIndicator = document.getElementById('typingIndicator');
                if (existingTypingIndicator) {
                    existingTypingIndicator.remove();
                }

                // 清理所有可能的打字指示器元素
                const typingElements = document.querySelectorAll('.dot-pulse, .typing-indicator');
                typingElements.forEach(element => {
                    const parentMessage = element.closest('.message-bubble');
                    if (parentMessage) {
                        parentMessage.remove();
                    }
                });

                // 加载用户信息
                await loadCurrentUser();

                // 如果服务器端已经有错误，显示错误信息
                if (serverData.hasError) {
                    addMessage('assistant', serverData.errorMessage);
                    return;
                }

                // 根据模式加载相应的数据
                if (serverData.chatMode === 'model') {
                    // 大模型模式只需要加载大模型提供商
                    await loadLLMProviders();
                } else {
                    // 智能体模式只需要加载智能体数据
                    await loadAgents();
                }

                // 使用服务器端数据初始化
                if (serverData.chatMode === 'model' && serverData.modelId) {
                    // 大模型模式 - 服务器端已经验证过权限和存在性
                    chatMode = 'model';
                    currentModelId = serverData.modelId;

                    // 查找大模型信息
                    const modelProvider = llmProviders.find(p => p.id == currentModelId);
                    if (modelProvider) {
                        currentModelProvider = modelProvider;
                        // 页面标题已经在服务器端设置好了，这里只需要设置JavaScript变量
                    }

                    // 侧边栏已在服务器端隐藏，无需JavaScript处理

                } else if (serverData.chatMode === 'agent' && serverData.agentId) {
                    // 智能体模式 - 服务器端已经验证过权限和存在性
                    chatMode = 'agent';

                    // 查找智能体信息
                    const selectedAgent = agents[serverData.agentId];
                    if (selectedAgent) {
                        currentAgent = selectedAgent;
                        // 设置智能体选择器的值
                        const agentSelect = document.getElementById('agentSelect');
                        if (agentSelect) {
                            agentSelect.value = serverData.agentId;
                        }
                    }
                } else {
                    // 回退到原有逻辑
                    const urlParams = new URLSearchParams(window.location.search);
                    const agentId = urlParams.get('agent');
                    const modelId = urlParams.get('model');

                    if (modelId) {
                        // 大模型模式 - 需要先加载大模型提供商数据
                        chatMode = 'model';
                        currentModelId = modelId;
                        await loadLLMProviders();
                        await initializeModelMode();
                    } else {
                        // 智能体模式 - 需要先加载智能体数据
                        await loadAgents();
                        chatMode = 'agent';
                        const selectedAgentId = agentId || (agentsList.length > 0 ? agentsList[0].id : null);

                        // 设置当前智能体
                        if (selectedAgentId && agents[selectedAgentId]) {
                        const selectedAgent = agents[selectedAgentId];

                        // 检查智能体访问权限
                        if (!selectedAgent.is_public && !currentUser) {
                            // 智能体不公开且用户未登录
                            addMessage('assistant', `抱歉，智能体"${selectedAgent.name}"需要登录后才能使用。请先登录您的账户。`);

                            // 显示登录提示
                            const chatMessages = document.getElementById('chatMessages');
                            const loginPrompt = document.createElement('div');
                            loginPrompt.className = 'text-center py-4';
                            loginPrompt.innerHTML = `
                                <div class="alert alert-info">
                                    <i class="bi bi-lock me-2"></i>
                                    该智能体需要登录后才能使用
                                    <div class="mt-3">
                                        <a href="/auth/login/" class="btn btn-primary me-2">登录</a>
                                        <a href="/auth/register/" class="btn btn-outline-primary">注册</a>
                                    </div>
                                </div>
                            `;
                            chatMessages.appendChild(loginPrompt);
                            return;
                        }

                        currentAgent = selectedAgent;
                        updateAgentDisplay();
                        document.getElementById('agentSelect').value = selectedAgentId;
                    } else if (agentsList.length > 0) {
                        // 从可用的智能体中选择第一个用户有权限访问的
                        const availableAgent = agentsList.find(agent =>
                            agent.is_public || currentUser
                        );

                        if (availableAgent) {
                            currentAgent = availableAgent;
                            updateAgentDisplay();
                            document.getElementById('agentSelect').value = currentAgent.id;
                        } else {
                            // 没有可用的智能体
                            addMessage('assistant', '抱歉，当前没有可用的智能体。请联系管理员或登录您的账户。');
                        }
                    }
                    }
                }

                // 显示欢迎消息（不创建会话）
                if (currentAgent || currentModelProvider) {
                    if (currentAgent) {
                        addMessage('assistant', `你好！我是${currentAgent.name}，有什么可以帮助您的吗？`);
                    } else if (currentModelProvider) {
                        addMessage('assistant', `你好！我是${currentModelProvider.name}，基于${getProviderDisplayName(currentModelProvider.provider)}。有什么可以帮助您的吗？`);
                    }
                }

                // 如果有搜索查询，自动发送
                const query = serverData.query || new URLSearchParams(window.location.search).get('q');
                if (query) {
                    document.getElementById('messageInput').value = query;
                    setTimeout(() => sendMessage(), 500);
                }

                // 只在智能体模式下加载用户历史会话列表
                if (chatMode === 'agent') {
                    await loadUserConversations();
                }

                // 更新UI以适应匿名/登录状态
                updateUIForUserState();

            } catch (error) {
                console.error('初始化页面失败:', error);
                addMessage('assistant', '抱歉，系统初始化失败，请刷新页面重试。');
            }
        }

        // 初始化大模型模式
        async function initializeModelMode() {
            try {
                // 查找指定的大模型
                const modelProvider = llmProviders.find(p => p.id == currentModelId);
                if (!modelProvider) {
                    throw new Error('指定的大模型不存在');
                }

                // 设置当前大模型提供商（必须在调用addMessage之前）
                currentModelProvider = modelProvider;

                // 更新页面标题和显示
                document.title = `与${modelProvider.name}对话 - Agent Portal`;

                // 侧边栏已在服务器端隐藏，无需JavaScript处理

                // 更新聊天头部显示
                updateModelDisplay();

                // 显示欢迎消息
                addMessage('assistant', `你好！我是${modelProvider.name}，基于${getProviderDisplayName(modelProvider.provider)}。有什么可以帮助您的吗？`);

                console.log('大模型模式初始化完成:', modelProvider);

            } catch (error) {
                console.error('初始化大模型模式失败:', error);
                addMessage('assistant', '抱歉，无法连接到指定的大模型，请返回首页重新选择。');
            }
        }

        // 更新大模型显示
        function updateModelDisplay() {
            if (chatMode === 'model' && currentModelProvider) {
                // 更新页面标题和标签
                document.getElementById('currentAgentName').textContent = currentModelProvider.name;
                const platformBadge = document.getElementById('currentAgentPlatform');
                platformBadge.textContent = getProviderDisplayName(currentModelProvider.provider);
                platformBadge.className = 'badge bg-info ms-2'; // 大模型使用不同颜色

                // 更新页面title
                document.title = `与${currentModelProvider.name}对话 - Agent Portal`;
            }
        }

        // 获取提供商显示名称
        function getProviderDisplayName(provider) {
            const providerMap = {
                'qwen': '阿里云千问',
                'deepseek': 'DeepSeek',
                'custom': '自定义'
            };
            return providerMap[provider] || provider;
        }

        // 更新UI以适应用户状态
        function updateUIForUserState() {
            const token = localStorage.getItem('token');
            const isLoggedIn = token && currentUser;

            // 新建对话按钮
            const newConversationBtn = document.querySelector('button[onclick="createNewConversation()"]');
            if (newConversationBtn) {
                if (isLoggedIn) {
                    newConversationBtn.style.display = 'block';
                } else {
                    newConversationBtn.style.display = 'none';
                }
            }

            // 对话设置区域
            const conversationSettings = document.getElementById('conversationSettings');
            if (conversationSettings) {
                if (isLoggedIn) {
                    // 登录用户显示正常的对话设置
                    conversationSettings.innerHTML = `
                        <h6 class="fw-bold text-muted mb-3">
                            <i class="bi bi-gear me-2"></i>对话设置
                        </h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-danger btn-sm" onclick="clearCurrentConversationHistory()">
                                <i class="bi bi-trash me-2"></i>清空历史
                            </button>
                        </div>
                    `;
                } else {
                    // 匿名用户显示提示信息
                    conversationSettings.innerHTML = `
                        <h6 class="fw-bold text-muted mb-3">
                            <i class="bi bi-gear me-2"></i>对话设置
                        </h6>
                        <div class="text-center py-3 text-muted">
                            <i class="bi bi-incognito opacity-50" style="font-size: 1.5rem;"></i>
                            <div class="mt-2 small">匿名模式不可导出会话历史</div>
                        </div>
                    `;
                }
            }
        }

        // 加载智能体数据
        async function loadAgents() {
            try {
                const headers = {};
                const token = localStorage.getItem('token');
                if (token) {
                    headers['Authorization'] = `Token ${token}`;
                }

                const response = await fetch(`${API_BASE_URL}/agents/`, {
                    headers: headers
                });

                if (response.ok) {
                    const data = await response.json();
                    agentsList = data.results || data;

                    // 构建agents对象
                    agents = {};
                    agentsList.forEach(agent => {
                        agents[agent.id] = agent;
                    });

                    // 更新选择器
                    updateAgentSelector();

                    console.log('智能体数据加载成功:', agentsList);
                } else {
                    throw new Error('加载智能体失败');
                }
            } catch (error) {
                console.error('加载智能体失败:', error);
                // 使用默认智能体
                agentsList = [{ id: 1, name: 'RAG知识助手', platform: 'ragflow', avatar: '📚' }];
                agents = { 1: agentsList[0] };
                updateAgentSelector();
            }
        }

        // 更新智能体选择器
        function updateAgentSelector() {
            const selector = document.getElementById('agentSelect');
            selector.innerHTML = '';

            agentsList.forEach(agent => {
                const option = document.createElement('option');
                option.value = agent.id;
                option.textContent = `${agent.avatar || '🤖'} ${agent.name}`;
                selector.appendChild(option);
            });

            // 设置当前选中的智能体
            if (currentAgent) {
                selector.value = currentAgent.id;
            } else if (serverData.agentId) {
                selector.value = serverData.agentId;
            }
        }

        // 加载大模型提供商
        async function loadLLMProviders() {
            try {
                const headers = {};
                const token = localStorage.getItem('token');
                if (token) {
                    headers['Authorization'] = `Token ${token}`;
                }

                const response = await fetch(`${API_BASE_URL}/llm-providers/enabled/`, {
                    headers: headers
                });

                if (response.ok) {
                    llmProviders = await response.json();
                    console.log('大模型提供商加载成功:', llmProviders);
                } else {
                    console.warn('加载大模型提供商失败，使用默认配置');
                    llmProviders = [];
                }
            } catch (error) {
                console.error('加载大模型提供商失败:', error);
                llmProviders = [];
            }
        }



        // 获取提供商显示名称
        function getProviderDisplayName(provider) {
            const providerMap = {
                'qwen': '千问',
                'deepseek': 'DeepSeek',
                'custom': '自定义'
            };
            return providerMap[provider] || provider;
        }

        // 创建或获取对话
        async function createOrGetConversation() {
            if (!currentAgent) {
                throw new Error('没有选择智能体');
            }

            try {
                // 构建请求头
                const headers = {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                };

                // 只有在有有效token时才添加Authorization头
                const token = localStorage.getItem('token');
                if (token && token.trim() !== '') {
                    headers['Authorization'] = `Token ${token}`;
                }

                const response = await fetch(`${API_BASE_URL}/conversations/`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        agent_id: currentAgent.id,
                        title: `与${currentAgent.name}的对话`
                    })
                });

                if (response.ok) {
                    const conversation = await response.json();
                    if (conversation && conversation.id) {
                        currentConversationId = conversation.id;
                        console.log('对话创建成功:', conversation);
                        console.log('对话ID:', currentConversationId);
                        return conversation;
                    } else {
                        throw new Error('对话创建返回无效数据');
                    }
                } else {
                    const errorText = await response.text();
                    console.error('创建对话失败:', response.status, errorText);
                    throw new Error(`创建对话失败: ${response.status}`);
                }
            } catch (error) {
                console.error('创建对话失败:', error);
                currentConversationId = null; // 确保清空无效的ID
                throw error;
            }
        }

        // 新建会话
        async function createNewConversation() {
            // 大模型模式下不支持会话管理
            if (chatMode === 'model') {
                addMessage('assistant', '大模型直接对话模式不支持会话管理');
                return;
            }

            if (!currentAgent) {
                addMessage('assistant', '请先选择一个智能体');
                return;
            }

            try {
                // 创建新对话
                await createOrGetConversation();

                // 清空消息历史
                document.getElementById('chatMessages').innerHTML = '';

                // 添加欢迎消息
                addMessage('assistant', `你好！我是${currentAgent.name}，有什么可以帮助您的吗？`);

                // 重新加载会话列表
                await loadUserConversations();

                console.log('新建会话成功，对话ID:', currentConversationId);
            } catch (error) {
                console.error('新建会话失败:', error);
                addMessage('assistant', '抱歉，新建会话失败，请稍后重试。');
            }
        }

        // 开始新会话（刷新按钮功能）
        async function startNewSession() {
            try {
                if (chatMode === 'model') {
                    // 大模型模式：清空聊天历史，重新开始
                    document.getElementById('chatMessages').innerHTML = '';

                    // 添加欢迎消息
                    if (currentModelProvider) {
                        addMessage('assistant', `你好！我是${currentModelProvider.name}，基于${getProviderDisplayName(currentModelProvider.provider)}。有什么可以帮助您的吗？`);
                    } else {
                        addMessage('assistant', '你好！有什么可以帮助您的吗？');
                    }

                    console.log('大模型模式新会话已开始');
                } else {
                    // 智能体模式：创建新对话
                    await createNewConversation();
                }
            } catch (error) {
                console.error('开始新会话失败:', error);
                addMessage('assistant', '抱歉，开始新会话失败，请稍后重试。');
            }
        }

        // 加载对话历史
        async function loadConversationHistory() {
            if (!currentConversationId) return;

            try {
                const response = await fetch(`${API_BASE_URL}/conversations/${currentConversationId}/messages/`, {
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const messages = data.results || data;

                    // 清空现有消息
                    document.getElementById('chatMessages').innerHTML = '';

                    // 添加历史消息
                    messages.forEach(message => {
                        // 只有助手消息才显示参考来源
                        const sources = message.type === 'assistant' ? message.sources : null;
                        addMessage(message.type, message.content, sources, message.created_at);
                    });

                    // 如果没有历史消息，添加欢迎消息
                    if (messages.length === 0 && currentAgent) {
                        addMessage('assistant', `你好！我是${currentAgent.name}，有什么可以帮助您的吗？`);
                    }

                    console.log('对话历史加载成功:', messages);
                } else {
                    // 如果加载失败，添加欢迎消息
                    if (currentAgent) {
                        addMessage('assistant', `你好！我是${currentAgent.name}，有什么可以帮助您的吗？`);
                    }
                }
            } catch (error) {
                console.error('加载对话历史失败:', error);
                // 如果加载失败，添加欢迎消息
                if (currentAgent) {
                    addMessage('assistant', `你好！我是${currentAgent.name}，有什么可以帮助您的吗？`);
                }
            }
        }

        // 加载用户历史会话列表
        async function loadUserConversations() {
            const historyContainer = document.getElementById('conversationHistory');
            const loadingElement = document.getElementById('conversationHistoryLoading');

            // 检查是否为登录用户
            const token = localStorage.getItem('token');
            const isLoggedIn = token && currentUser;

            if (!isLoggedIn) {
                // 匿名用户不显示历史会话
                if (loadingElement) {
                    loadingElement.remove();
                }
                historyContainer.innerHTML = `
                    <div class="text-center py-3 text-muted">
                        <i class="bi bi-incognito opacity-50" style="font-size: 2rem;"></i>
                        <div class="mt-2">匿名模式</div>
                        <div class="small">不保存对话历史</div>
                    </div>
                `;
                return;
            }

            try {
                // 构建请求头
                const headers = {};
                if (token && token.trim() !== '') {
                    headers['Authorization'] = `Token ${token}`;
                }

                const response = await fetch(`${API_BASE_URL}/conversations/?ordering=-updated_at&limit=20`, {
                    headers: headers
                });

                if (response.ok) {
                    const data = await response.json();
                    const conversations = data.results || data;

                    // 移除加载指示器
                    if (loadingElement) {
                        loadingElement.remove();
                    }

                    if (conversations.length === 0) {
                        historyContainer.innerHTML = `
                            <div class="text-center py-3 text-muted">
                                <i class="bi bi-chat-dots opacity-50" style="font-size: 2rem;"></i>
                                <div class="mt-2">暂无历史会话</div>
                            </div>
                        `;
                        return;
                    }

                    // 渲染会话列表
                    historyContainer.innerHTML = conversations.map(conversation => `
                        <div class="conversation-item ${conversation.id === currentConversationId ? 'active' : ''}"
                             onclick="switchToConversation('${conversation.id}')"
                             data-conversation-id="${conversation.id}">
                            <div class="d-flex align-items-start">
                                <div class="flex-grow-1">
                                    <div class="fw-bold small mb-1">${conversation.title}</div>
                                    <div class="text-muted small">
                                        ${conversation.agent?.name || '未知智能体'} •
                                        ${conversation.message_count || 0}条消息
                                    </div>
                                    <div class="text-muted small">
                                        ${formatRelativeTime(conversation.updated_at)}
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown" onclick="event.stopPropagation()">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="event.stopPropagation(); renameConversation('${conversation.id}')">
                                            <i class="bi bi-pencil me-2"></i>重命名
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="event.stopPropagation(); deleteConversation('${conversation.id}')">
                                            <i class="bi bi-trash me-2"></i>删除
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `).join('');

                    console.log('用户会话列表加载成功:', conversations);
                } else {
                    // 移除加载指示器
                    if (loadingElement) {
                        loadingElement.remove();
                    }
                    historyContainer.innerHTML = `
                        <div class="text-center py-3 text-muted">
                            <i class="bi bi-exclamation-triangle opacity-50"></i>
                            <div class="mt-2">加载失败</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('加载用户会话列表失败:', error);
                const historyContainer = document.getElementById('conversationHistory');
                const loadingElement = document.getElementById('conversationHistoryLoading');

                if (loadingElement) {
                    loadingElement.remove();
                }
                historyContainer.innerHTML = `
                    <div class="text-center py-3 text-muted">
                        <i class="bi bi-wifi-off opacity-50"></i>
                        <div class="mt-2">网络错误</div>
                    </div>
                `;
            }
        }

        // 切换到指定会话
        async function switchToConversation(conversationId) {
            try {
                // 更新当前会话ID
                currentConversationId = conversationId;

                // 获取会话详情
                const response = await fetch(`${API_BASE_URL}/conversations/${conversationId}/`, {
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`
                    }
                });

                if (response.ok) {
                    const conversation = await response.json();

                    // 更新当前智能体
                    if (conversation.agent && agents[conversation.agent.id]) {
                        currentAgent = agents[conversation.agent.id];
                        updateAgentDisplay();
                        document.getElementById('agentSelect').value = currentAgent.id;
                    }

                    // 加载会话消息
                    await loadConversationHistory();

                    // 更新会话列表中的活跃状态
                    document.querySelectorAll('.conversation-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    document.querySelector(`[data-conversation-id="${conversationId}"]`)?.classList.add('active');

                    console.log('切换到会话:', conversation);
                } else {
                    console.error('获取会话详情失败');
                }
            } catch (error) {
                console.error('切换会话失败:', error);
            }
        }

        // 重命名会话
        async function renameConversation(conversationId) {
            const newTitle = prompt('请输入新的会话标题:');
            if (!newTitle || !newTitle.trim()) return;

            try {
                const response = await fetch(`${API_BASE_URL}/conversations/${conversationId}/`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        title: newTitle.trim()
                    })
                });

                if (response.ok) {
                    // 重新加载会话列表
                    await loadUserConversations();
                    console.log('会话重命名成功');
                } else {
                    alert('重命名失败，请稍后重试');
                }
            } catch (error) {
                console.error('重命名会话失败:', error);
                alert('重命名失败，请稍后重试');
            }
        }

        // 删除会话
        async function deleteConversation(conversationId) {
            if (!confirm('确定要删除这个会话吗？此操作不可撤销。')) return;

            try {
                const response = await fetch(`${API_BASE_URL}/conversations/${conversationId}/`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });

                if (response.ok) {
                    // 如果删除的是当前会话，创建新会话
                    if (conversationId === currentConversationId) {
                        await createNewConversation();
                    }

                    // 重新加载会话列表
                    await loadUserConversations();
                    console.log('会话删除成功');
                } else if (response.status === 404) {
                    // 会话不存在，可能已经被删除了
                    console.log('会话不存在，可能已经被删除');

                    // 如果删除的是当前会话，创建新会话
                    if (conversationId === currentConversationId) {
                        await createNewConversation();
                    }

                    // 重新加载会话列表
                    await loadUserConversations();
                } else {
                    const errorText = await response.text();
                    console.error('删除失败:', response.status, errorText);
                    alert('删除失败，请稍后重试');
                }
            } catch (error) {
                console.error('删除会话失败:', error);
                alert('删除失败，请稍后重试');
            }
        }

        // 清空当前对话历史
        async function clearCurrentConversationHistory() {
            if (!currentConversationId) {
                alert('当前没有活跃的对话');
                return;
            }

            // 确认对话框
            if (!confirm('确定要清空当前对话的所有历史消息吗？此操作不可撤销。')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/conversations/${currentConversationId}/clear_messages/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();

                    // 清空界面上的消息
                    document.getElementById('chatMessages').innerHTML = '';

                    // 添加欢迎消息
                    if (currentAgent) {
                        addMessage('assistant', `你好！我是${currentAgent.name}，有什么可以帮助您的吗？`);
                    }

                    // 重新加载对话历史列表
                    await loadUserConversations();

                    console.log('对话历史清空成功:', data.message);
                } else {
                    alert('清空失败，请稍后重试');
                }
            } catch (error) {
                console.error('清空对话历史失败:', error);
                alert('清空失败，请稍后重试');
            }
        }

        // 格式化相对时间
        function formatRelativeTime(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return '刚刚';
            if (diffMins < 60) return `${diffMins}分钟前`;
            if (diffHours < 24) return `${diffHours}小时前`;
            if (diffDays < 7) return `${diffDays}天前`;

            return date.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 格式化消息时间
        function formatMessageTime(timestamp) {
            if (!timestamp) {
                // 如果没有时间戳，使用当前时间
                return new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            }

            const date = new Date(timestamp);
            const now = new Date();
            const diffMs = now - date;
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            // 如果是今天的消息，只显示时间
            if (diffDays === 0) {
                return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            }
            // 如果是昨天的消息
            else if (diffDays === 1) {
                return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            }
            // 如果是一周内的消息
            else if (diffDays < 7) {
                const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                return weekdays[date.getDay()] + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            }
            // 更早的消息显示日期和时间
            else {
                return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }) + ' ' +
                       date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            }
        }

        // 获取CSRF Token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // 更新智能体显示
        function updateAgentDisplay() {
            // 只在必要时更新页面标题（如果服务器端没有设置）
            if (!serverData.currentItemName) {
                document.getElementById('currentAgentName').textContent = currentAgent.name;
                const platformBadge = document.getElementById('currentAgentPlatform');
                platformBadge.textContent = currentAgent.platform;
                platformBadge.className = 'badge bg-primary ms-2';
            }

            // 更新侧边栏卡片（如果不是从服务器端渲染的）
            const sidebarCard = document.getElementById('currentAgentCard');
            if (sidebarCard.querySelector('.spinner-border')) {
                sidebarCard.innerHTML = `
                    <div class="fs-2 mb-2">${currentAgent.avatar}</div>
                    <h6 class="fw-bold mb-1">${currentAgent.name}</h6>
                    <small class="opacity-75">${currentAgent.description}</small>
                    <div class="mt-2">
                        <span class="badge bg-light text-dark">在线</span>
                    </div>
                `;
            }
        }

        // 智能体切换 - 跳转到新页面
        document.getElementById('agentSelect').addEventListener('change', function(e) {
            const agentId = e.target.value;
            if (agentId && agentId !== currentAgent?.id?.toString()) {
                // 跳转到新的智能体对话页面
                window.location.href = `/chat/?agent=${agentId}`;
            }
        });



        // Markdown解析函数
        function parseMarkdown(content) {
            if (typeof marked === 'undefined') {
                console.warn('Marked.js未加载，使用纯文本显示');
                return content.replace(/\n/g, '<br>');
            }

            try {
                // 配置marked选项
                marked.setOptions({
                    breaks: true,        // 支持换行符转换为<br>
                    gfm: true,          // 启用GitHub风格的Markdown
                    sanitize: false,    // 不清理HTML（因为我们信任AI回答的内容）
                    smartLists: true,   // 智能列表
                    smartypants: true   // 智能标点符号
                });

                // 解析Markdown
                return marked.parse(content);
            } catch (error) {
                console.error('Markdown解析失败:', error);
                // 降级处理：简单的换行转换
                return content.replace(/\n/g, '<br>');
            }
        }

        // 添加消息
        function addMessage(type, content, sources = null, timestamp = null, files = null) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-bubble ${type}`;

            const sourcesHtml = (type === 'assistant' && sources && sources.length > 0) ? `
                <div class="mt-2 pt-2 border-top border-light">
                    <small class="opacity-75">参考来源：</small>
                    ${sources.map(source => `<a href="#" class="text-decoration-none ms-2"><i class="bi bi-link-45deg"></i>${source}</a>`).join('')}
                </div>
            ` : '';

            if (type === 'user') {
                // 用户消息布局 - Ant Design X 风格
                const userName = currentUser ? (currentUser.first_name || currentUser.username) : '用户';
                messageDiv.innerHTML = `
                    <div class="message-avatar user-avatar">
                        <i class="bi bi-person"></i>
                    </div>
                    <div style="flex: 1;">
                        <div class="message-header user">
                            <div class="message-name">${userName}</div>
                        </div>
                        <div class="message-content">
                            ${content}
                            ${files && files.length > 0 ? generateFilesHtml(files) : ''}
                        </div>
                        <div class="message-time">
                            ${formatMessageTime(timestamp)}
                        </div>
                    </div>
                `;
            } else {
                // 助手消息布局 - Ant Design X 风格，对助手回复进行Markdown解析
                const parsedContent = parseMarkdown(content);

                // 根据聊天模式获取头像和名称
                let avatar, name;
                if (chatMode === 'model' && currentModelProvider) {
                    avatar = '<i class="bi bi-robot"></i>';
                    name = currentModelProvider.name;
                } else if (currentAgent) {
                    avatar = currentAgent.avatar;
                    name = currentAgent.name;
                } else {
                    avatar = '<i class="bi bi-robot"></i>';
                    name = 'AI助手';
                }

                messageDiv.innerHTML = `
                    <div class="message-avatar assistant-avatar">
                        ${avatar}
                    </div>
                    <div style="flex: 1;">
                        <div class="message-header">
                            <div class="message-name">${name}</div>
                        </div>
                        <div class="message-content">
                            ${parsedContent}
                            ${sourcesHtml}
                        </div>
                        <div class="message-time">
                            ${formatMessageTime(timestamp)}
                        </div>
                    </div>
                `;
            }

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // 返回创建的消息元素，以便后续更新
            return messageDiv;
        }

        // 显示打字指示器 - Ant Design X 风格
        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chatMessages');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message-bubble assistant';
            typingDiv.id = 'typingIndicator';

            // 根据聊天模式获取头像和名称
            let avatar, name;
            if (chatMode === 'model' && currentModelProvider) {
                avatar = '<i class="bi bi-robot"></i>';
                name = currentModelProvider.name;
            } else if (currentAgent) {
                avatar = currentAgent.avatar;
                name = currentAgent.name;
            } else {
                avatar = '<i class="bi bi-robot"></i>';
                name = 'AI助手';
            }

            typingDiv.innerHTML = `
                <div class="message-avatar assistant-avatar">
                    ${avatar}
                </div>
                <div style="flex: 1;">
                    <div class="message-header">
                        <div class="message-name">${name}</div>
                    </div>
                    <div class="message-content">
                        <div class="typing-indicator">
                            <div class="dot-pulse"></div>
                        </div>
                    </div>
                </div>
            `;
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 隐藏打字指示器
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // 更新发送按钮状态
        function updateSendButtonState() {
            const sendButton = document.querySelector('.send-btn');
            const messageInput = document.getElementById('messageInput');
            
            if (isTyping) {
                // 禁用发送按钮
                sendButton.disabled = true;
                sendButton.classList.add('disabled');
                sendButton.title = 'Agent is responding...';
                
                // 更新按钮内容显示等待状态
                sendButton.innerHTML = '<i class="bi bi-hourglass-split"></i>';
            } else {
                // 启用发送按钮
                sendButton.disabled = false;
                sendButton.classList.remove('disabled');
                sendButton.title = 'Send message (Enter)';
                
                // 恢复原始按钮内容
                sendButton.innerHTML = '<i class="bi bi-send"></i>';
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            // 检查智能体访问权限（针对智能体模式）
            if (chatMode === 'agent' && currentAgent) {
                const token = localStorage.getItem('token');
                const isLoggedIn = token && currentUser;

                if (!currentAgent.is_public && !isLoggedIn) {
                    addMessage('assistant', '抱歉，该智能体需要登录后才能使用。请先登录您的账户。');
                    return;
                }
            }

            // 检查是否为登录用户
            const token = localStorage.getItem('token');
            const isLoggedIn = token && currentUser;

            // 只有在智能体模式下才需要创建对话
            if (chatMode === 'agent' && isLoggedIn && !currentConversationId) {
                try {
                    await createOrGetConversation();
                    // 确保对话创建成功
                    if (!currentConversationId) {
                        addMessage('assistant', '抱歉，对话创建失败，请刷新页面重试。');
                        return;
                    }
                } catch (error) {
                    console.error('创建对话失败:', error);
                    addMessage('assistant', '抱歉，对话创建失败，请刷新页面重试。');
                    return;
                }
            }

            // 添加用户消息到界面（包含文件信息）
            addMessage('user', message, null, null, uploadedFiles.length > 0 ? [...uploadedFiles] : null);
            input.value = '';

            // 使用autosize更新高度
            if (typeof autosize !== 'undefined') {
                autosize.update(input);
            }

            // 设置正在输入状态，但不显示打字指示器
            isTyping = true;
            updateSendButtonState();

            // 根据聊天模式发送消息
            if (chatMode === 'model') {
                sendMessageToModel(message);
            } else {
                sendMessageViaAPI(message);
            }

            // 清空已上传的文件列表
            clearUploadedFiles();
        }

        // 通过API发送消息（使用SSE流式响应）
        async function sendMessageViaAPI(message) {
            const token = localStorage.getItem('token');
            const isLoggedIn = token && currentUser;

            // 对于登录用户，验证对话ID
            if (isLoggedIn && (!currentConversationId || currentConversationId === 'null')) {
                console.error('无效的对话ID:', currentConversationId);
                addMessage('assistant', '对话ID无效，请刷新页面重试。');
                isTyping = false;
                updateSendButtonState();
                return;
            }

            console.log('发送消息:', {
                conversation_id: currentConversationId || 'anonymous',
                content: message,
                type: 'user',
                is_anonymous: !isLoggedIn
            });

            try {
                // 构建SSE URL
                let sseUrl = `${API_BASE_URL}/conversations/send-message-stream/?content=${encodeURIComponent(message)}&type=user`;

                if (isLoggedIn && currentConversationId) {
                    sseUrl += `&conversation_id=${encodeURIComponent(currentConversationId)}`;
                } else {
                    // 匿名用户，添加智能体ID
                    sseUrl += `&agent_id=${encodeURIComponent(currentAgent.id)}&anonymous=true`;
                }

                // 添加文件信息
                if (uploadedFiles.length > 0) {
                    const fileIds = uploadedFiles.map(file => file.file_id).join(',');
                    const fileNames = uploadedFiles.map(file => file.name).join(',');
                    sseUrl += `&file_ids=${encodeURIComponent(fileIds)}`;
                    sseUrl += `&file_names=${encodeURIComponent(fileNames)}`;
                }



                // 使用SSE接收流式响应
                const eventSource = new EventSource(sseUrl);

                let assistantMessageDiv = null;
                let assistantContent = '';

                eventSource.onmessage = function(event) {
                    const data = JSON.parse(event.data);

                    if (data.type === 'user_message') {
                        // 用户消息已经显示了，不需要处理
                    } else if (data.type === 'assistant_start') {
                        // 开始接收助手回复，直接创建消息容器
                        const messagesContainer = document.getElementById('chatMessages');
                        assistantMessageDiv = document.createElement('div');
                        assistantMessageDiv.className = 'message-bubble assistant';
                        assistantMessageDiv.innerHTML = `
                            <div class="message-avatar assistant-avatar">
                                ${currentAgent.avatar}
                            </div>
                            <div style="flex: 1;">
                                <div class="message-header">
                                    <div class="message-name">${currentAgent.name}</div>
                                </div>
                                <div class="message-content">
                                    <!-- 内容将通过流式输出填充 -->
                                </div>
                                <div class="message-time">
                                    ${formatMessageTime()}
                                </div>
                            </div>
                        `;
                        messagesContainer.appendChild(assistantMessageDiv);
                        messagesContainer.scrollTop = messagesContainer.scrollHeight;
                    } else if (data.type === 'assistant_status') {
                        // 接收状态消息（如 'xxx is running...'），显示但准备被覆盖
                        if (assistantMessageDiv) {
                            const contentDiv = assistantMessageDiv.querySelector('.message-content');

                            // 显示状态消息，添加特殊样式表示这是临时状态
                            contentDiv.innerHTML = `<span class="text-muted">${data.content}</span>`;

                            // 滚动到底部
                            const messagesContainer = document.getElementById('chatMessages');
                            messagesContainer.scrollTop = messagesContainer.scrollHeight;
                        }
                    } else if (data.type === 'assistant_chunk') {
                        // 接收助手回复的片段，实现真正的流式输出
                        if (assistantMessageDiv) {
                            const contentDiv = assistantMessageDiv.querySelector('.message-content');

                            // 如果是第一个chunk，清除所有之前的内容（包括状态消息和打字光标）
                            if (assistantContent === '') {
                                contentDiv.innerHTML = '';
                            }

                            // 累积内容
                            assistantContent += data.content;

                            // 对累积的内容进行Markdown解析并实时更新显示
                            const parsedContent = parseMarkdown(assistantContent);
                            contentDiv.innerHTML = parsedContent;

                            // 滚动到底部
                            const messagesContainer = document.getElementById('chatMessages');
                            messagesContainer.scrollTop = messagesContainer.scrollHeight;
                        }
                    } else if (data.type === 'assistant_complete') {
                        // 助手回复完成，添加参考来源（内容已在流式过程中解析）
                        if (assistantMessageDiv && data.sources && data.sources.length > 0) {
                            const contentDiv = assistantMessageDiv.querySelector('.message-content');

                            // 添加参考来源
                            const sourcesDiv = document.createElement('div');
                            sourcesDiv.className = 'mt-2 pt-2 border-top border-light';
                            sourcesDiv.innerHTML = `
                                <small class="opacity-75">参考来源：</small>
                                ${data.sources.map(source => `<a href="#" class="text-decoration-none ms-2"><i class="bi bi-link-45deg"></i>${source}</a>`).join('')}
                            `;
                            contentDiv.appendChild(sourcesDiv);
                        }
                        eventSource.close();
                        isTyping = false;
                        updateSendButtonState();
                    } else if (data.type === 'error') {
                        addMessage('assistant', data.message || '抱歉，发送消息失败，请稍后重试。');
                        eventSource.close();
                        isTyping = false;
                        updateSendButtonState();
                    }
                };

                eventSource.onerror = function(event) {
                    console.error('SSE连接错误:', event);
                    addMessage('assistant', '网络错误，请检查连接后重试。');
                    eventSource.close();
                    isTyping = false;
                    updateSendButtonState();
                };

            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('assistant', '网络错误，请检查连接后重试。');
                isTyping = false;
                updateSendButtonState();
            }
        }

        // 回车发送消息，Shift+Enter换行
        document.getElementById('messageInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                if (e.shiftKey) {
                    // Shift+Enter: 允许换行，不做任何处理
                    return;
                } else {
                    // 单独Enter: 只有在不是正在输入状态时才发送消息
                    e.preventDefault();
                    if (!isTyping) {
                        sendMessage();
                    }
                }
            }
        });



        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('聊天页面初始化...');

            // 立即清理任何残留的打字指示器
            setTimeout(() => {
                const typingElements = document.querySelectorAll('.dot-pulse, .typing-indicator, #typingIndicator');
                typingElements.forEach(element => {
                    const parentMessage = element.closest('.message-bubble');
                    if (parentMessage) {
                        parentMessage.remove();
                    } else {
                        element.remove();
                    }
                });
            }, 100);

            // 确保输入框正确显示
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                console.log('✅ 输入框已加载');

                // 使用autosize库自动调整高度
                if (typeof autosize !== 'undefined') {
                    // 设置最大高度限制
                    messageInput.style.maxHeight = '150px';

                    // 初始化autosize
                    autosize(messageInput);

                    // 监听autosize事件
                    messageInput.addEventListener('autosize:resized', function() {
                        // 如果超过最大高度，显示滚动条
                        if (this.scrollHeight > 150) {
                            this.style.overflowY = 'auto';
                        } else {
                            this.style.overflowY = 'hidden';
                        }
                    });

                    console.log('✅ Autosize已初始化');
                } else {
                    console.warn('⚠️ Autosize库未加载');
                }

                // 自动聚焦到输入框
                messageInput.focus();
            } else {
                console.error('❌ 输入框未找到');
            }

            // 初始化发送按钮状态
            updateSendButtonState();

            // 初始化文件上传功能
            updateFileUploadHint();

            initPage();
        });

        // 获取当前聊天历史记录
        function getChatHistory() {
            const messagesContainer = document.getElementById('chatMessages');
            const messageElements = messagesContainer.querySelectorAll('.message-bubble');
            const history = [];

            messageElements.forEach(messageElement => {
                // 跳过打字指示器
                if (messageElement.id === 'typingIndicator') {
                    return;
                }

                const isUser = messageElement.classList.contains('user');
                const isAssistant = messageElement.classList.contains('assistant');

                if (isUser || isAssistant) {
                    const contentElement = messageElement.querySelector('.message-content');
                    if (contentElement) {
                        // 获取纯文本内容，去除HTML标签
                        const content = contentElement.textContent || contentElement.innerText || '';
                        if (content.trim()) {
                            history.push({
                                role: isUser ? 'user' : 'assistant',
                                content: content.trim()
                            });
                        }
                    }
                }
            });

            return history;
        }

        // 直接与大模型对话
        async function sendMessageToModel(message) {
            try {
                // 获取当前聊天历史记录
                const chatHistory = getChatHistory();

                console.log('发送消息到大模型:', {
                    model_id: currentModelId,
                    message: message,
                    history: chatHistory
                });

                // 构建请求数据 - 包含历史聊天记录
                const requestData = {
                    model_id: currentModelId,  // 后端期望的参数名
                    message: message,
                    history: chatHistory  // 添加历史聊天记录
                };

                // 发送POST请求到大模型API - 修正URL路径
                const response = await fetch(`${API_BASE_URL}/llm-providers/chat/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': localStorage.getItem('token') ? `Token ${localStorage.getItem('token')}` : ''
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                // 处理流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                let assistantMessageDiv = null;
                let assistantContent = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                if (data.type === 'chunk') {
                                    // 创建或更新助手消息
                                    if (!assistantMessageDiv) {
                                        assistantMessageDiv = addMessage('assistant', '');
                                    }

                                    assistantContent += data.content;
                                    updateMessageContent(assistantMessageDiv, assistantContent);

                                } else if (data.type === 'complete') {
                                    console.log('大模型响应完成');
                                    break;
                                } else if (data.type === 'error') {
                                    throw new Error(data.message || '大模型响应错误');
                                }
                            } catch (e) {
                                console.warn('解析SSE数据失败:', line, e);
                            }
                        }
                    }
                }

            } catch (error) {
                console.error('发送消息到大模型失败:', error);
                addMessage('assistant', `抱歉，发送消息失败：${error.message}`);
            } finally {
                isTyping = false;
                updateSendButtonState();
            }
        }

        // 更新消息内容（支持Markdown渲染）
        function updateMessageContent(messageDiv, content) {
            const contentElement = messageDiv.querySelector('.message-content');
            if (contentElement) {
                // 如果有marked库，使用Markdown渲染
                if (typeof marked !== 'undefined') {
                    contentElement.innerHTML = marked.parse(content);
                } else {
                    contentElement.textContent = content;
                }

                // 滚动到底部
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }

        // 文件上传相关变量
        let uploadedFiles = [];
        let isUploading = false;

        // 触发文件选择
        function triggerFileUpload() {
            if (isUploading) return;
            document.getElementById('fileInput').click();
        }

        // 处理文件选择
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            if (files.length === 0) return;

            // 上传选中的文件
            uploadFiles(files);

            // 清空input，允许重复选择同一文件
            e.target.value = '';
        });

        // 上传文件
        async function uploadFiles(files) {
            if (!currentAgent) {
                alert('请先选择智能体');
                return;
            }

            isUploading = true;
            updateUploadButtonState();

            for (const file of files) {
                try {
                    await uploadSingleFile(file);
                } catch (error) {
                    console.error('文件上传失败:', error);
                    alert(`文件 ${file.name} 上传失败: ${error.message}`);
                }
            }

            isUploading = false;
            updateUploadButtonState();
        }

        // 上传单个文件
        async function uploadSingleFile(file) {
            try {
                // 准备表单数据
                const formData = new FormData();
                formData.append('file', file);
                formData.append('agent_id', currentAgent.id);

                // 发送上传请求
                const response = await fetch(`${API_BASE_URL}/conversations/upload-file/`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Authorization': localStorage.getItem('token') ? `Token ${localStorage.getItem('token')}` : ''
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // 上传成功，保存文件信息
                    const fileInfo = {
                        file_id: result.file_id,
                        name: result.file_name,
                        size: result.file_size
                    };

                    uploadedFiles.push(fileInfo);
                    console.log('文件上传成功:', fileInfo);

                    // 显示已上传的文件
                    displayUploadedFile(fileInfo);
                } else {
                    throw new Error(result.error || '上传失败');
                }
            } catch (error) {
                throw error;
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 更新上传按钮状态
        function updateUploadButtonState() {
            const uploadButton = document.querySelector('.upload-btn');
            if (isUploading) {
                uploadButton.classList.add('uploading');
                uploadButton.disabled = true;
                uploadButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                uploadButton.title = '正在上传...';
            } else {
                uploadButton.classList.remove('uploading');
                uploadButton.disabled = false;
                uploadButton.innerHTML = '<i class="bi bi-paperclip"></i>';
                uploadButton.title = '上传文件';
            }
        }

        // 更新文件上传提示
        function updateFileUploadHint() {
            const hint = document.getElementById('fileUploadHint');
            if (chatMode === 'agent' && currentAgent) {
                hint.style.display = 'inline';
            } else {
                hint.style.display = 'none';
            }
        }

        // 显示已上传的文件
        function displayUploadedFile(fileInfo) {
            const uploadedFilesArea = document.getElementById('uploadedFilesArea');
            const uploadedFilesList = document.getElementById('uploadedFilesList');

            // 创建文件显示项
            const fileItem = document.createElement('div');
            fileItem.className = 'uploaded-file-item';
            fileItem.setAttribute('data-file-id', fileInfo.file_id);

            // 获取文件类型图标
            const fileIcon = getFileTypeIcon(fileInfo.name);
            const fileName = fileInfo.name.length > 15 ? fileInfo.name.substring(0, 12) + '...' : fileInfo.name;

            fileItem.innerHTML = `
                <i class="bi ${fileIcon} file-icon"></i>
                <span class="file-name" title="${fileInfo.name}">${fileName}</span>
                <i class="bi bi-x remove-file" onclick="removeUploadedFile('${fileInfo.file_id}')" title="移除文件"></i>
            `;

            uploadedFilesList.appendChild(fileItem);
            uploadedFilesArea.style.display = 'block';
        }

        // 移除已上传的文件
        function removeUploadedFile(fileId) {
            // 从文件列表中移除
            uploadedFiles = uploadedFiles.filter(file => file.file_id !== fileId);

            // 移除DOM元素
            const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
            if (fileItem) {
                fileItem.remove();
            }

            // 如果没有文件了，隐藏显示区域
            const uploadedFilesList = document.getElementById('uploadedFilesList');
            const uploadedFilesArea = document.getElementById('uploadedFilesArea');
            if (uploadedFilesList.children.length === 0) {
                uploadedFilesArea.style.display = 'none';
            }
        }

        // 获取文件类型图标
        function getFileTypeIcon(fileName) {
            const extension = fileName.split('.').pop().toLowerCase();
            const iconMap = {
                'pdf': 'bi-file-earmark-pdf-fill',
                'doc': 'bi-file-earmark-word-fill',
                'docx': 'bi-file-earmark-word-fill',
                'xls': 'bi-file-earmark-excel-fill',
                'xlsx': 'bi-file-earmark-excel-fill',
                'ppt': 'bi-file-earmark-ppt-fill',
                'pptx': 'bi-file-earmark-ppt-fill',
                'txt': 'bi-file-earmark-text-fill',
                'png': 'bi-file-earmark-image-fill',
                'jpg': 'bi-file-earmark-image-fill',
                'jpeg': 'bi-file-earmark-image-fill',
                'gif': 'bi-file-earmark-image-fill',
                'webp': 'bi-file-earmark-image-fill',
                'bmp': 'bi-file-earmark-image-fill'
            };
            return iconMap[extension] || 'bi-file-earmark-fill';
        }

        // 生成文件显示HTML
        function generateFilesHtml(files) {
            if (!files || files.length === 0) return '';

            const filesHtml = files.map(file => {
                const fileIcon = getFileTypeIcon(file.name);
                const fileName = file.name.length > 20 ? file.name.substring(0, 17) + '...' : file.name;
                return `
                    <div class="message-file-item">
                        <i class="bi ${fileIcon} file-icon"></i>
                        <span class="file-name" title="${file.name}">${fileName}</span>
                    </div>
                `;
            }).join('');

            return `
                <div class="message-files">
                    ${filesHtml}
                </div>
            `;
        }

        // 清空已上传的文件列表
        function clearUploadedFiles() {
            uploadedFiles = [];
            const uploadedFilesList = document.getElementById('uploadedFilesList');
            const uploadedFilesArea = document.getElementById('uploadedFilesArea');
            uploadedFilesList.innerHTML = '';
            uploadedFilesArea.style.display = 'none';
        }


</script>
{% endblock %}
