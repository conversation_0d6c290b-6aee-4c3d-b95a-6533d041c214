{% extends 'base.html' %}

{% block title %}智能体管理 - Agent Portal{% endblock %}

{% block extra_css %}
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(255, 255, 255, 0.2);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar-glass {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--glass-border);
        }

        main {
            padding-top: 0 !important;
        }

        .page-header {
            background: var(--primary-gradient);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
            margin-top: 0;
            padding-top: calc(3rem + 76px);
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }



        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            color: white;
        }



        .modal-glass {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 16px;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }



        .stats-card {
            background: var(--secondary-gradient);
            color: white;
            border-radius: 12px;
        }

        /* 卡片样式优化 */
        .card {
            border: 1px solid rgba(0, 0, 0, 0.125);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        .btn-group .btn {
            border-radius: 6px;
            margin: 0 2px;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }


{% endblock %}

{% block content %}
    {% csrf_token %}

    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-3">智能体管理</h1>
                    <p class="lead mb-0">管理您的AI智能体，配置连接参数，监控使用状态</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="stats-card p-3 text-center">
                        <h3 class="mb-1" id="totalAgents">0</h3>
                        <small>已配置智能体</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container mb-5">
        <!-- 搜索和筛选 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text bg-white border-end-0">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control border-start-0" placeholder="搜索智能体..." id="searchInput">
                </div>
            </div>
            <div class="col-md-8">
                <div class="d-flex gap-2">
                    <select class="form-select" id="platformFilter">
                        <option value="">所有平台</option>
                        <option value="RagFlow">RagFlow</option>
                        <option value="Dify">Dify</option>
                        <option value="OpenAI">OpenAI</option>
                        <option value="Custom">自定义</option>
                    </select>
                    <select class="form-select" id="statusFilter">
                        <option value="">所有状态</option>
                        <option value="online">在线</option>
                        <option value="offline">离线</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 智能体列表 -->
        <div class="glass-card">
            <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center" style="padding: 1.5rem; border-radius: 16px 16px 0 0;">
                <h6 class="mb-0 fw-bold">
                    <i class="bi bi-robot me-2"></i>已配置的智能体
                </h6>
                <button class="btn btn-sm btn-gradient" onclick="openAddAgentModal()">
                    <i class="bi bi-plus-lg me-1"></i>添加智能体
                </button>
            </div>
            <div class="card-body" style="padding: 0 1.5rem 1.5rem;" id="agentsList">
                <!-- 智能体列表将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 智能体模态框（添加/编辑共用） -->
    <div class="modal fade" id="agentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content modal-glass">
                <div class="modal-header border-0">
                    <h5 class="modal-title fw-bold" id="agentModalTitle">添加新智能体</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="agentForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">智能体名称</label>
                                <input type="text" class="form-control" id="agentName" placeholder="输入智能体名称" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">平台类型</label>
                                <select class="form-select" id="agentPlatform" required>
                                    <option value="">选择平台</option>
                                    <option value="ragflow">RagFlow</option>
                                    <option value="dify">Dify</option>
                                    <option value="openai">OpenAI</option>
                                    <option value="custom">自定义</option>
                                    <option value="url">外部链接</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">描述</label>
                            <textarea class="form-control" id="agentDescription" rows="3" placeholder="描述智能体的功能和用途"></textarea>
                        </div>
                        <!-- 通用配置字段 -->
                        <div id="genericConfig">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">API端点</label>
                                    <input type="text" class="form-control" id="agentEndpoint" placeholder="https://api.example.com">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">API密钥</label>
                                    <input type="password" class="form-control" id="agentApiKey" placeholder="输入API密钥">
                                </div>
                            </div>
                        </div>

                        <!-- RagFlow专用配置字段 -->
                        <div id="ragflowConfig" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>RagFlow配置说明</h6>
                                <p class="mb-2">RagFlow需要以下三个参数：</p>
                                <ul class="mb-0">
                                    <li><strong>Base URL</strong>: RagFlow服务器地址</li>
                                    <li><strong>Agent ID</strong>: 在RagFlow中创建的智能体ID</li>
                                    <li><strong>API Key</strong>: RagFlow提供的API密钥</li>
                                </ul>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Base URL <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="ragflowBaseUrl" placeholder="http://10.7.0.50:7080" required>
                                    <div class="form-text">RagFlow服务器的基础URL，例如：http://10.7.0.50:7080</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Agent ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="ragflowAgentId" placeholder="340f56c64da611f084300242ac130006" required>
                                    <div class="form-text">RagFlow中的智能体ID，32位字符串</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">API Key <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="ragflowApiKey" placeholder="ragflow-NkYzE0OTk4MzU3NTExZjA5ZmMwMDI0Mm" required>
                                    <div class="form-text">RagFlow的API密钥，以ragflow-开头</div>
                                </div>
                            </div>
                        </div>

                        <!-- Dify专用配置字段 -->
                        <div id="difyConfig" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>Dify配置说明</h6>
                                <p class="mb-2">Dify需要以下参数：</p>
                                <ul class="mb-0">
                                    <li><strong>Base URL</strong>: Dify服务器地址</li>
                                    <li><strong>API Key</strong>: Dify应用的API密钥（以app-开头）</li>
                                    <li><strong>App ID</strong>: Dify应用ID（可选，用于工作流应用）</li>
                                </ul>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Base URL <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="difyBaseUrl" placeholder="http://10.7.0.50" required>
                                    <div class="form-text">Dify服务器的基础URL，例如：http://10.7.0.50</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">API Key <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="difyApiKey" placeholder="app-dJ0MroBS66kCo1XwLusLWAPl" required>
                                    <div class="form-text">Dify应用的API密钥，以app-开头</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">App ID</label>
                                    <input type="text" class="form-control" id="difyAppId" placeholder="2d3dc0ae-ac0d-4196-809f-4ad9a3afe1ba">
                                    <div class="form-text">Dify应用ID（工作流应用需要）</div>
                                </div>
                            </div>
                        </div>

                        <!-- URL专用配置字段 -->
                        <div id="urlConfig" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>外部链接配置说明</h6>
                                <p class="mb-2">外部链接类型的智能体将直接跳转到指定URL：</p>
                                <ul class="mb-0">
                                    <li><strong>目标URL</strong>: 点击智能体时要跳转的完整URL地址</li>
                                    <li>支持任何外部网站或应用的链接</li>
                                    <li>链接将在新标签页中打开</li>
                                </ul>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">目标URL <span class="text-danger">*</span></label>
                                    <input type="url" class="form-control" id="urlTarget" placeholder="https://example.com/agent" required>
                                    <div class="form-text">完整的URL地址，包含http://或https://</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">标签</label>
                            <input type="text" class="form-control" id="agentTags" placeholder="用逗号或空格分隔，如：文档检索,问答 中文">
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="agentIsPublic">
                                <label class="form-check-label fw-bold" for="agentIsPublic">
                                    <i class="bi bi-globe me-1"></i>公开智能体
                                </label>
                                <div class="form-text">开启后，未登录用户也可以看到并使用此智能体</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="agentAllowUpload" checked>
                                <label class="form-check-label fw-bold" for="agentAllowUpload">
                                    <i class="bi bi-paperclip me-1"></i>允许上传文件
                                </label>
                                <div class="form-text">开启后，用户可以在对话中上传文件</div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-gradient" id="agentModalSubmitBtn" onclick="submitAgent()">
                        <i class="bi bi-plus-lg me-1" id="agentModalSubmitIcon"></i>
                        <span id="agentModalSubmitText">添加智能体</span>
                    </button>
                </div>
            </div>
        </div>
    </div>



{% endblock %}

{% block extra_js %}
<script>
        // 智能体数据
        let agents = [];

        let editingAgentId = null;
        let isEditMode = false; // 标记是否为编辑模式

        // 获取平台颜色
        function getPlatformColor(platform) {
            const colors = {
                'ragflow': 'primary',
                'RagFlow': 'primary',
                'dify': 'success',
                'Dify': 'success',
                'openai': 'info',
                'OpenAI': 'info',
                'custom': 'warning',
                'Custom': 'warning',
                'url': 'dark',
                '外部链接': 'dark'
            };
            return colors[platform] || 'secondary';
        }

        // 渲染智能体
        function renderAgents(agentsToRender = agents) {
            renderAgentsList(agentsToRender);
            // 更新统计
            document.getElementById('totalAgents').textContent = agents.length;
        }

        // 渲染智能体列表视图
        function renderAgentsList(agentsToRender) {
            const container = document.getElementById('agentsList');

            // 如果没有数据，显示空状态
            if (!agentsToRender || agentsToRender.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-robot fs-1 opacity-50 mb-3"></i>
                        <h5 class="mb-2">暂无智能体</h5>
                        <p class="mb-0">点击上方"添加智能体"按钮开始配置</p>
                    </div>
                `;
                return;
            }

            const agentsHtml = agentsToRender.map(agent => `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="me-3">
                                        <div class="icon-wrapper" style="width: 40px; height: 40px; border-radius: 50%; background: var(--primary-gradient); color: white; font-size: 1.2rem; display: inline-flex; align-items: center; justify-content: center;">
                                            ${agent.avatar}
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="card-title mb-1">${agent.name}</h6>
                                        <div class="d-flex align-items-center gap-2 mb-1">
                                            <span class="badge bg-${getPlatformColor(agent.platform)}">${agent.platform.toUpperCase()}</span>
                                            <span class="badge bg-${agent.isOnline ? 'success' : 'secondary'}">
                                                <i class="bi bi-circle-fill me-1" style="font-size: 0.5rem;"></i>
                                                ${agent.isOnline ? '在线' : '离线'}
                                            </span>
                                            <span class="badge bg-${agent.is_public ? 'info' : 'primary'} ${agent.is_public ? '' : 'text-white'}">
                                                <i class="bi bi-${agent.is_public ? 'globe' : 'lock'} me-1"></i>
                                                ${agent.is_public ? '公开' : '私有'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <p class="card-text text-muted mb-2">
                                    <small>${agent.description}</small>
                                </p>
                                <div class="mb-2">
                                    ${(agent.tags || []).slice(0, 3).map(tag =>
                                        `<span class="badge bg-light text-primary me-1" style="font-size: 0.7rem;">${typeof tag === 'string' ? tag : (tag.name || tag)}</span>`
                                    ).join('')}
                                    ${agent.tags && agent.tags.length > 3 ? `<span class="text-muted small">+${agent.tags.length - 3}</span>` : ''}
                                </div>
                                <p class="card-text text-muted mb-0">
                                    <small>
                                        <strong>使用次数:</strong> ${(agent.usageCount || 0).toLocaleString()} |
                                        <strong>ID:</strong> ${agent.id}
                                    </small>
                                </p>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="goToChat('${agent.id}')" title="聊天">
                                    <i class="bi bi-chat-dots"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="editAgent('${agent.id}')" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="testAgent('${agent.id}')" title="测试">
                                    <i class="bi bi-wifi"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteAgent('${agent.id}')" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = agentsHtml;
        }



        // 加载智能体列表
        function loadAgents() {
            // 这里应该从API加载智能体数据
            // 暂时使用空数组作为示例
            agents = [];
            renderAgents();
        }

        // 跳转到聊天页面或外部链接
        function goToChat(agentId) {
            // 查找对应的智能体
            const agent = agents.find(a => a.id === agentId);

            if (agent && agent.platform === 'url') {
                // 如果是URL类型，直接在新标签页打开外部链接
                if (agent.endpoint) {
                    window.open(agent.endpoint, '_blank');
                } else {
                    showToast('该智能体的目标URL未配置', 'error');
                }
            } else {
                // 其他类型跳转到聊天页面
                window.location.href = `{% url 'chat' %}?agent=${agentId}`;
            }
        }

        // 打开添加智能体modal
        function openAddAgentModal() {
            isEditMode = false;
            editingAgentId = null;

            // 设置modal标题和按钮
            document.getElementById('agentModalTitle').textContent = '添加新智能体';
            document.getElementById('agentModalSubmitIcon').className = 'bi bi-plus-lg me-1';
            document.getElementById('agentModalSubmitText').textContent = '添加智能体';

            // 重置表单
            document.getElementById('agentForm').reset();

            // 显示modal
            const modal = new bootstrap.Modal(document.getElementById('agentModal'));
            modal.show();
        }

        // 清空表单
        function clearAgentForm() {
            document.getElementById('agentForm').reset();

            // 清空所有平台特定字段
            document.getElementById('ragflowBaseUrl').value = '';
            document.getElementById('ragflowAgentId').value = '';
            document.getElementById('ragflowApiKey').value = '';
            document.getElementById('difyBaseUrl').value = '';
            document.getElementById('difyApiKey').value = '';
            document.getElementById('difyAppId').value = '';
            document.getElementById('agentEndpoint').value = '';
            document.getElementById('agentApiKey').value = '';
            document.getElementById('urlTarget').value = '';

            // 重置公开设置
            document.getElementById('agentIsPublic').checked = false;

            // 重置允许上传设置
            document.getElementById('agentAllowUpload').checked = true;

            // 重置平台配置显示
            handlePlatformChange();
        }

        // 统一的提交函数
        function submitAgent() {
            if (isEditMode) {
                saveAgent();
            } else {
                addAgent();
            }
        }

        // 添加智能体
        async function addAgent() {
            const form = document.getElementById('agentForm');
            const platform = document.getElementById('agentPlatform').value;

            let endpoint, apiKey, agentId;

            // 根据平台获取不同的配置
            if (platform === 'ragflow') {
                endpoint = document.getElementById('ragflowBaseUrl').value;
                agentId = document.getElementById('ragflowAgentId').value;
                apiKey = document.getElementById('ragflowApiKey').value;
            } else if (platform === 'dify') {
                endpoint = document.getElementById('difyBaseUrl').value;
                agentId = document.getElementById('difyAppId').value;
                apiKey = document.getElementById('difyApiKey').value;
            } else if (platform === 'url') {
                endpoint = document.getElementById('urlTarget').value;
                apiKey = ''; // URL类型不需要API密钥
                agentId = '';
            } else {
                endpoint = document.getElementById('agentEndpoint').value;
                apiKey = document.getElementById('agentApiKey').value;
            }

            // 验证必填字段
            if (!document.getElementById('agentName').value.trim()) {
                showToast('请输入智能体名称', 'error');
                return;
            }

            if (!platform) {
                showToast('请选择平台类型', 'error');
                return;
            }

            // 根据平台验证特定字段
            if (platform === 'ragflow' || platform === 'RagFlow') {
                if (!document.getElementById('ragflowBaseUrl').value.trim()) {
                    showToast('请输入RagFlow Base URL', 'error');
                    return;
                }
                if (!document.getElementById('ragflowAgentId').value.trim()) {
                    showToast('请输入RagFlow Agent ID', 'error');
                    return;
                }
                if (!document.getElementById('ragflowApiKey').value.trim()) {
                    showToast('请输入RagFlow API Key', 'error');
                    return;
                }
            } else if (platform === 'dify' || platform === 'Dify') {
                if (!document.getElementById('difyBaseUrl').value.trim()) {
                    showToast('请输入Dify Base URL', 'error');
                    return;
                }
                if (!document.getElementById('difyApiKey').value.trim()) {
                    showToast('请输入Dify API Key', 'error');
                    return;
                }
            } else if (platform === 'url') {
                if (!document.getElementById('urlTarget').value.trim()) {
                    showToast('请输入目标URL', 'error');
                    return;
                }
            } else {
                if (!endpoint || !endpoint.trim()) {
                    showToast('请输入API端点', 'error');
                    return;
                }
                if (!apiKey || !apiKey.trim()) {
                    showToast('请输入API密钥', 'error');
                    return;
                }
            }

            // 构建新智能体数据
            const newAgentData = {
                name: document.getElementById('agentName').value,
                platform: platform,
                description: document.getElementById('agentDescription').value,
                api_endpoint: endpoint,
                api_key: apiKey,
                platform_agent_id: agentId || '',
                is_public: document.getElementById('agentIsPublic').checked,
                allow_upload: document.getElementById('agentAllowUpload').checked,
                tag_names: parseTagsInput(document.getElementById('agentTags').value)
            };

            try {
                // 发送POST请求到后端API
                const response = await fetch('/api/agents/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(newAgentData)
                });

                if (response.ok) {
                    const createdAgent = await response.json();

                    // 添加到本地数据
                    const newAgent = {
                        id: createdAgent.id,
                        name: createdAgent.name,
                        platform: createdAgent.platform,
                        description: createdAgent.description,
                        endpoint: createdAgent.api_endpoint,
                        apiKey: '****', // 隐藏真实API密钥
                        platformAgentId: createdAgent.platform_agent_id,
                        tags: createdAgent.tags ? createdAgent.tags.map(tag => tag.name || tag) : [],
                        avatar: createdAgent.avatar || getRandomAvatar(),
                        usageCount: createdAgent.usage_count || 0,
                        isOnline: createdAgent.is_online || false,
                        is_public: createdAgent.is_public || false,
                        status: createdAgent.status
                    };

                    agents.push(newAgent);
                    renderAgents();

                    // 关闭模态框并重置表单
                    const modal = bootstrap.Modal.getInstance(document.getElementById('agentModal'));
                    modal.hide();
                    clearAgentForm();

                    // 显示成功提示
                    showToast('智能体添加成功！', 'success');
                } else {
                    const error = await response.json();
                    showToast(`添加失败: ${error.detail || error.message || '未知错误'}`, 'danger');
                }

            } catch (error) {
                console.error('添加智能体失败:', error);
                showToast('添加失败: 网络错误', 'danger');
            }
        }

        // 编辑智能体
        async function editAgent(agentId) {
            try {
                // 从API获取完整的智能体信息
                const response = await fetch(`/api/agents/${agentId}/`, {
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    showToast('获取智能体信息失败', 'danger');
                    return;
                }

                const agent = await response.json();

                // 设置编辑模式
                isEditMode = true;
                editingAgentId = agentId;

                // 设置modal标题和按钮
                document.getElementById('agentModalTitle').textContent = '编辑智能体';
                document.getElementById('agentModalSubmitIcon').className = 'bi bi-check-lg me-1';
                document.getElementById('agentModalSubmitText').textContent = '保存更改';

                // 填充表单
                document.getElementById('agentName').value = agent.name || '';
                document.getElementById('agentPlatform').value = agent.platform || '';
                document.getElementById('agentDescription').value = agent.description || '';
                document.getElementById('agentTags').value = agent.tags ? agent.tags.map(tag => tag.name || tag).join(', ') : '';
                document.getElementById('agentIsPublic').checked = agent.is_public || false;
                document.getElementById('agentAllowUpload').checked = agent.allow_upload !== undefined ? agent.allow_upload : true;

                // 根据平台填充对应的字段
                if (agent.platform === 'ragflow') {
                    document.getElementById('ragflowBaseUrl').value = agent.api_endpoint || '';
                    document.getElementById('ragflowApiKey').value = agent.api_key || '';
                    document.getElementById('ragflowAgentId').value = agent.platform_agent_id || '';
                } else if (agent.platform === 'dify') {
                    document.getElementById('difyBaseUrl').value = agent.api_endpoint || '';
                    document.getElementById('difyApiKey').value = agent.api_key || '';
                    document.getElementById('difyAppId').value = agent.platform_agent_id || '';
                } else if (agent.platform === 'url') {
                    document.getElementById('urlTarget').value = agent.api_endpoint || '';
                } else {
                    document.getElementById('agentEndpoint').value = agent.api_endpoint || '';
                    document.getElementById('agentApiKey').value = agent.api_key || '';
                }

                // 触发平台变化事件以显示正确的配置区域
                handlePlatformChange();

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('agentModal'));
                modal.show();

            } catch (error) {
                console.error('获取智能体信息失败:', error);
                showToast('获取智能体信息失败: 网络错误', 'danger');
            }
        }

        // 保存智能体更改
        async function saveAgent() {
            if (!editingAgentId) return;

            const platform = document.getElementById('agentPlatform').value;
            let endpoint, apiKey, platformAgentId;

            // 根据平台获取对应的配置
            if (platform === 'ragflow') {
                endpoint = document.getElementById('ragflowBaseUrl').value;
                apiKey = document.getElementById('ragflowApiKey').value;
                platformAgentId = document.getElementById('ragflowAgentId').value;
            } else if (platform === 'dify') {
                endpoint = document.getElementById('difyBaseUrl').value;
                apiKey = document.getElementById('difyApiKey').value;
                platformAgentId = document.getElementById('difyAppId').value;
            } else if (platform === 'url') {
                endpoint = document.getElementById('urlTarget').value;
                apiKey = ''; // URL类型不需要API密钥
                platformAgentId = '';
            } else {
                endpoint = document.getElementById('agentEndpoint').value;
                apiKey = document.getElementById('agentApiKey').value;
            }

            // 构建更新数据
            const updateData = {
                name: document.getElementById('agentName').value,
                platform: platform,
                description: document.getElementById('agentDescription').value,
                api_endpoint: endpoint,
                api_key: apiKey,
                platform_agent_id: platformAgentId,
                is_public: document.getElementById('agentIsPublic').checked,
                allow_upload: document.getElementById('agentAllowUpload').checked,
                tag_names: parseTagsInput(document.getElementById('agentTags').value)
            };

            try {
                // 发送PUT请求到后端API
                const response = await fetch(`/api/agents/${editingAgentId}/`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(updateData)
                });

                if (response.ok) {
                    const updatedAgent = await response.json();

                    // 更新本地数据
                    const agentIndex = agents.findIndex(a => a.id === editingAgentId);
                    if (agentIndex !== -1) {
                        agents[agentIndex] = {
                            ...agents[agentIndex],
                            name: updatedAgent.name,
                            platform: updatedAgent.platform,
                            description: updatedAgent.description,
                            endpoint: updatedAgent.api_endpoint,
                            apiKey: '****', // 隐藏真实API密钥
                            platformAgentId: updatedAgent.platform_agent_id,
                            is_public: updatedAgent.is_public || false,
                            tags: updatedAgent.tags ? updatedAgent.tags.map(tag => tag.name || tag) : []
                        };
                    }

                    renderAgents();

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('agentModal'));
                    modal.hide();
                    clearAgentForm();

                    showToast('智能体更新成功！', 'success');
                    isEditMode = false;
                    editingAgentId = null;
                } else {
                    const error = await response.json();
                    showToast(`更新失败: ${error.detail || error.message || '未知错误'}`, 'danger');
                }

            } catch (error) {
                console.error('更新智能体失败:', error);
                showToast('更新失败: 网络错误', 'danger');
            }
        }

        // 删除智能体
        async function deleteAgent(agentId) {
            const agent = agents.find(a => a.id === agentId);
            if (!agent) return;

            if (!confirm(`确定要删除智能体"${agent.name}"吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/agents/${agentId}/`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCsrfToken()
                    }
                });

                if (response.ok) {
                    // 从本地数据中移除
                    agents = agents.filter(a => a.id !== agentId);
                    renderAgents();
                    showToast(`智能体"${agent.name}"已删除`, 'warning');
                } else {
                    const error = await response.json();
                    showToast(`删除失败: ${error.detail || '未知错误'}`, 'danger');
                }

            } catch (error) {
                console.error('删除智能体失败:', error);
                showToast('删除失败: 网络错误', 'danger');
            }
        }

        // 测试智能体连接
        async function testAgent(agentId) {
            const agent = agents.find(a => a.id === agentId);
            if (!agent) return;

            showToast('正在测试连接...', 'info');

            try {
                const response = await fetch(`/api/agents/${agentId}/test_connection/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify({
                        test_message: '测试连接'
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    // 更新本地数据
                    agent.isOnline = true;
                    showToast(`${agent.name} 连接测试成功！响应时间: ${(result.response_time * 1000).toFixed(0)}ms`, 'success');
                } else {
                    agent.isOnline = false;
                    showToast(`${agent.name} 连接测试失败: ${result.message || '未知错误'}`, 'danger');
                }

                renderAgents();

            } catch (error) {
                console.error('测试连接失败:', error);
                agent.isOnline = false;
                showToast(`${agent.name} 连接测试失败: 网络错误`, 'danger');
                renderAgents();
            }
        }

        // 解析标签输入，支持逗号和空格分隔
        function parseTagsInput(input) {
            if (!input || !input.trim()) return [];

            // 先按逗号分割，然后对每个部分按空格分割
            return input.split(/[,\s]+/)
                       .map(tag => tag.trim())
                       .filter(tag => tag.length > 0);
        }

        // 获取随机头像
        function getRandomAvatar() {
            const avatars = ['🤖', '🎯', '⚡', '🔥', '💎', '🚀', '🎨', '🔬', '📡', '🎪'];
            return avatars[Math.floor(Math.random() * avatars.length)];
        }

        // 获取CSRF Token
        function getCsrfToken() {
            return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 创建toast容器（如果不存在）
            let toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }
            
            // 创建toast
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast" role="alert">
                    <div class="toast-header">
                        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} text-${type} me-2"></i>
                        <strong class="me-auto">Agent Portal</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">${message}</div>
                </div>
            `;
            
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            
            // 显示toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();
            
            // 自动清理
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // 搜索和筛选功能
        function filterAgents() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const platformFilter = document.getElementById('platformFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            const filtered = agents.filter(agent => {
                const matchesSearch = agent.name.toLowerCase().includes(searchTerm) || 
                                    agent.description.toLowerCase().includes(searchTerm) ||
                                    agent.tags.some(tag => tag.toLowerCase().includes(searchTerm));
                
                const matchesPlatform = !platformFilter || agent.platform === platformFilter;
                const matchesStatus = !statusFilter || 
                                    (statusFilter === 'online' && agent.isOnline) ||
                                    (statusFilter === 'offline' && !agent.isOnline);
                
                return matchesSearch && matchesPlatform && matchesStatus;
            });
            
            renderAgents(filtered);
        }

        // 绑定搜索和筛选事件
        document.getElementById('searchInput').addEventListener('input', filterAgents);
        document.getElementById('platformFilter').addEventListener('change', filterAgents);
        document.getElementById('statusFilter').addEventListener('change', filterAgents);

        // 从API加载智能体数据
        async function loadAgentsFromAPI() {
            try {
                const response = await fetch('/api/agents/', {
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    // 转换API数据格式以匹配前端期望的格式
                    agents = (data.results || data).map(agent => ({
                        id: agent.id,
                        name: agent.name,
                        description: agent.description,
                        platform: agent.platform,
                        avatar: agent.avatar || '🤖',
                        usageCount: agent.usage_count || 0,
                        isOnline: agent.is_online,
                        is_public: agent.is_public || false,
                        tags: agent.tags ? agent.tags.map(tag => tag.name || tag) : [],
                        endpoint: agent.api_endpoint,
                        apiKey: '****', // 隐藏真实API密钥
                        status: agent.status,
                        platformAgentId: agent.platform_agent_id
                    }));
                    renderAgents();
                    updateStats();
                } else {
                    console.error('Failed to load agents from API');
                    // 显示空状态
                    renderAgents();
                }
            } catch (error) {
                console.error('Error loading agents:', error);
                // 显示空状态
                renderAgents();
            }
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalAgents').textContent = agents.length;
        }

        // 处理平台选择变化
        function handlePlatformChange() {
            const platform = document.getElementById('agentPlatform').value;
            const genericConfig = document.getElementById('genericConfig');
            const ragflowConfig = document.getElementById('ragflowConfig');
            const difyConfig = document.getElementById('difyConfig');
            const urlConfig = document.getElementById('urlConfig');

            // 隐藏所有配置区域
            genericConfig.style.display = 'none';
            ragflowConfig.style.display = 'none';
            difyConfig.style.display = 'none';
            urlConfig.style.display = 'none';

            // 根据平台显示对应配置
            switch(platform) {
                case 'ragflow':
                case 'RagFlow':
                    ragflowConfig.style.display = 'block';
                    break;
                case 'dify':
                case 'Dify':
                    difyConfig.style.display = 'block';
                    break;
                case 'url':
                    urlConfig.style.display = 'block';
                    break;
                case 'openai':
                case 'OpenAI':
                case 'custom':
                case 'Custom':
                default:
                    genericConfig.style.display = 'block';
                    break;
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadAgentsFromAPI();

            // 平台选择变化事件
            document.getElementById('agentPlatform').addEventListener('change', handlePlatformChange);

            // 编辑modal的平台选择变化事件
            if (document.getElementById('editAgentPlatform')) {
                document.getElementById('editAgentPlatform').addEventListener('change', function() {
                    // 这里可以添加编辑时的平台特定配置显示逻辑
                    console.log('编辑模式平台变化:', this.value);
                });
            }

            // 初始化时显示默认配置
            handlePlatformChange();
        });
    </script>
{% endblock %}
