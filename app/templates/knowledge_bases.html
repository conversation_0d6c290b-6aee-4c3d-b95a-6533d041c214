{% extends 'base.html' %}

{% block title %}知识库管理 - Agent Portal{% endblock %}

{% block extra_css %}
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(255, 255, 255, 0.2);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar-glass {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--glass-border);
        }

        main {
            padding-top: 0 !important;
        }

        .page-header {
            background: var(--primary-gradient);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
            margin-top: 0;
            padding-top: calc(3rem + 76px);
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }



        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            color: white;
        }



        .modal-glass {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 16px;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }



        .stats-card {
            background: var(--secondary-gradient);
            color: white;
            border-radius: 12px;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.1);
        }

        /* 卡片样式优化 */
        .card {
            border: 1px solid rgba(0, 0, 0, 0.125);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        .btn-group .btn {
            border-radius: 6px;
            margin: 0 2px;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        /* 空状态样式 */
        .empty-state {
            padding: 2rem;
            text-align: center;
        }

        .empty-state i {
            display: block;
            margin-bottom: 1rem;
        }

        .empty-state h4, .empty-state h5 {
            color: #6c757d;
            margin-bottom: 0.5rem;
        }

        .empty-state p {
            color: #adb5bd;
            margin-bottom: 1.5rem;
        }
    </style>
{% endblock %}

{% block content %}
    {% csrf_token %}

    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-3">知识库管理</h1>
                    <p class="lead mb-0">管理您的知识库，连接RagFlow平台，搜索和组织知识内容</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="stats-card p-3 text-center">
                        <h3 class="mb-1" id="totalKnowledgeBases">0</h3>
                        <small>已配置知识库</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container mb-5">
        <!-- 搜索和筛选 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text bg-white border-end-0">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control border-start-0" placeholder="搜索知识库..." id="searchInput">
                </div>
            </div>
            <div class="col-md-8">
                <div class="d-flex gap-2">
                    <select class="form-select" id="platformFilter">
                        <option value="">所有平台</option>
                        <option value="dify" disabled style="color: #6c757d;">Dify (暂不支持)</option>
                        <option value="ragflow">RagFlow</option>
                    </select>
                    <select class="form-select" id="statusFilter">
                        <option value="">所有状态</option>
                        <option value="active">激活</option>
                        <option value="inactive">未激活</option>
                        <option value="error">错误</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 知识库列表 -->
        <div class="glass-card">
            <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center" style="padding: 1.5rem; border-radius: 16px 16px 0 0;">
                <h6 class="mb-0 fw-bold">
                    <i class="bi bi-book me-2"></i>已配置的知识库
                </h6>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-info" onclick="showRagflowAuthModal()">
                        <i class="bi bi-shield-lock me-1"></i>RAGFLOW认证设置
                    </button>
                    <button class="btn btn-sm btn-gradient" onclick="openAddKnowledgeBaseModal()">
                        <i class="bi bi-plus-lg me-1"></i>添加知识库
                    </button>
                </div>
            </div>
            <div class="card-body" style="padding: 0 1.5rem 1.5rem;" id="knowledgeBasesList">
                <!-- 知识库列表将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 知识库模态框（添加/编辑共用） -->
    <div class="modal fade" id="knowledgeBaseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content modal-glass">
                <div class="modal-header border-0">
                    <h5 class="modal-title fw-bold" id="knowledgeBaseModalTitle">添加新知识库</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="knowledgeBaseForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">知识库名称</label>
                                <input type="text" class="form-control" id="kbName" placeholder="输入知识库名称" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">平台类型</label>
                                <select class="form-select" id="kbPlatform" required>
                                    <option value="">选择平台</option>
                                    <option value="dify" disabled style="color: #6c757d;">Dify (暂不支持)</option>
                                    <option value="ragflow">RagFlow</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">描述</label>
                            <textarea class="form-control" id="kbDescription" rows="3" placeholder="描述知识库的内容和用途"></textarea>
                        </div>

                        <!-- Dify专用配置字段 -->
                        <div id="difyConfig" style="display: none;">
                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>Dify 暂不支持</h6>
                                <p class="mb-0">Dify 平台集成功能正在开发中，暂时无法使用。请选择 RagFlow 平台。</p>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold text-muted">API端点 <span class="text-muted">*</span></label>
                                    <input type="text" class="form-control" id="difyEndpoint" placeholder="https://api.dify.ai/v1" disabled>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold text-muted">API密钥 <span class="text-muted">*</span></label>
                                    <input type="password" class="form-control" id="difyApiKey" placeholder="dataset-xxxxx" disabled>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-muted">知识库ID <span class="text-muted">*</span></label>
                                <input type="text" class="form-control" id="difyKbId" placeholder="知识库的唯一标识符" disabled>
                            </div>
                        </div>

                        <!-- RagFlow专用配置字段 -->
                        <div id="ragflowConfig" style="display: none;">
                            <div class="alert alert-success" id="ragflowGlobalConfigAlert">
                                <h6><i class="bi bi-check-circle me-2"></i>使用全局认证配置</h6>
                                <p class="mb-2">RagFlow知识库将自动使用全局认证设置，无需单独配置认证信息。</p>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-success me-2">全局配置已启用</span>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="showRagflowAuthModal()">
                                        <i class="bi bi-gear me-1"></i>管理全局设置
                                    </button>
                                </div>
                            </div>

                            <div class="alert alert-warning" id="ragflowGlobalConfigMissing" style="display: none;">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>需要配置全局认证</h6>
                                <p class="mb-2">请先配置RagFlow全局认证设置，然后再添加知识库。</p>
                                <button type="button" class="btn btn-sm btn-warning" onclick="showRagflowAuthModal()">
                                    <i class="bi bi-shield-lock me-1"></i>立即配置
                                </button>
                            </div>

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">知识库ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="ragflowKbId" placeholder="知识库的唯一标识符">
                                    <div class="form-text">从RagFlow平台获取的知识库唯一标识符</div>
                                </div>
                            </div>

                            <!-- 高级配置选项（可选） -->
                            <div class="card border-0 bg-light mt-3" style="display: none;" id="ragflowAdvancedConfig">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-gear me-2"></i>高级配置
                                        <small class="text-muted">(可选)</small>
                                    </h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="useIndependentConfig">
                                        <label class="form-check-label" for="useIndependentConfig">
                                            使用独立认证配置
                                        </label>
                                        <div class="form-text">启用此选项可为该知识库单独配置认证信息</div>
                                    </div>

                                    <div id="independentConfigFields" style="display: none;" class="mt-3">
                                        <div class="row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label fw-bold">API端点</label>
                                                <input type="text" class="form-control" id="ragflowIndependentEndpoint" placeholder="http://your-ragflow-server:7080">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label fw-bold">用户名</label>
                                                <input type="text" class="form-control" id="ragflowIndependentUsername" placeholder="RagFlow用户名">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label fw-bold">密码</label>
                                                <input type="password" class="form-control" id="ragflowIndependentPassword" placeholder="RagFlow密码">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="kbIsPublic">
                                <label class="form-check-label" for="kbIsPublic">
                                    公开知识库（其他用户可以查看）
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-gradient" id="knowledgeBaseModalSubmitBtn" onclick="submitKnowledgeBase()">
                        <i class="bi bi-plus-lg me-1" id="knowledgeBaseModalSubmitIcon"></i>
                        <span id="knowledgeBaseModalSubmitText">添加知识库</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- RagFlow认证设置模态框 -->
    <div class="modal fade" id="ragflowAuthModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content modal-glass">
                <div class="modal-header border-0">
                    <h5 class="modal-title fw-bold">
                        <i class="bi bi-shield-lock me-2 text-info"></i>RAGFLOW认证设置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle me-2"></i>全局认证配置</h6>
                        <p class="mb-0">配置RagFlow平台的全局认证信息，用于所有RagFlow知识库的搜索功能。</p>
                    </div>
                    <form id="ragflowAuthForm">
                        <div class="mb-3">
                            <label class="form-label fw-bold">服务器地址 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="ragflowAuthUrl" placeholder="http://10.7.0.50:7080" required>
                            <div class="form-text">RagFlow服务器的完整URL地址</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">用户名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="ragflowAuthUsername" placeholder="RagFlow用户名" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="ragflowAuthPassword" placeholder="RagFlow密码" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">令牌刷新间隔(小时)</label>
                            <input type="number" class="form-control" id="ragflowAuthRefreshInterval" value="12" min="1" max="48">
                            <div class="form-text">认证令牌自动刷新的时间间隔，建议12小时</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-info" onclick="testRagflowConnection()">
                        <i class="bi bi-wifi me-1"></i>测试连接
                    </button>
                    <button type="button" class="btn btn-gradient" onclick="saveRagflowAuth()">
                        <i class="bi bi-check-lg me-1"></i>保存设置
                    </button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
<script>
        // 知识库数据
        let knowledgeBases = [];

        let editingKbId = null;
        let isEditMode = false; // 标记是否为编辑模式

        // 获取平台颜色
        function getPlatformColor(platform) {
            const colors = {
                'dify': 'success',
                'ragflow': 'primary'
            };
            return colors[platform] || 'secondary';
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'active': 'success',
                'inactive': 'secondary',
                'error': 'danger'
            };
            return colors[status] || 'secondary';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'active': '激活',
                'inactive': '未激活',
                'error': '错误'
            };
            return texts[status] || status;
        }

        // 获取配置状态徽章
        function getConfigStatusBadge(kb) {
            if (!kb.config_status) {
                return '';
            }

            const config = kb.config_status;
            let badgeClass = '';
            let icon = '';
            let title = '';

            switch (config.status) {
                case 'configured':
                    badgeClass = 'bg-success';
                    icon = 'bi-check-circle';
                    title = config.description;
                    break;
                case 'missing':
                    badgeClass = 'bg-warning';
                    icon = 'bi-exclamation-triangle';
                    title = config.description;
                    break;
                case 'incomplete':
                    badgeClass = 'bg-danger';
                    icon = 'bi-x-circle';
                    title = config.description;
                    break;
                default:
                    badgeClass = 'bg-secondary';
                    icon = 'bi-question-circle';
                    title = '未知状态';
            }

            const typeText = config.type === 'global' ? '全局配置' : '独立配置';

            return `
                <div class="mb-2">
                    <span class="badge ${badgeClass} config-badge" title="${title}">
                        <i class="bi ${icon} me-1"></i>${typeText}
                    </span>
                </div>
            `;
        }

        // 渲染知识库
        function renderKnowledgeBases(kbsToRender = knowledgeBases) {
            renderKnowledgeBasesList(kbsToRender);
            // 更新统计
            document.getElementById('totalKnowledgeBases').textContent = knowledgeBases.length;
        }

        // 渲染知识库列表视图
        function renderKnowledgeBasesList(kbsToRender) {
            const container = document.getElementById('knowledgeBasesList');

            // 如果没有数据，显示空状态
            if (!kbsToRender || kbsToRender.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-book fs-1 opacity-50 mb-3"></i>
                        <h5 class="mb-2">暂无知识库</h5>
                        <p class="mb-0">点击上方"添加知识库"按钮开始配置</p>
                    </div>
                `;
                return;
            }

            const kbsHtml = kbsToRender.map(kb => `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="me-3">
                                        <div class="icon-wrapper" style="width: 40px; height: 40px; border-radius: 50%; background: var(--primary-gradient); color: white; font-size: 1.2rem; display: inline-flex; align-items: center; justify-content: center;">
                                            📚
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="card-title mb-1">${kb.name}</h6>
                                        <div class="d-flex align-items-center gap-2 mb-1">
                                            <span class="badge bg-${getPlatformColor(kb.platform)}">${kb.platform.toUpperCase()}</span>
                                            <span class="badge bg-${getStatusColor(kb.status)}">
                                                <i class="bi bi-circle-fill me-1" style="font-size: 0.5rem;"></i>
                                                ${getStatusText(kb.status)}
                                            </span>
                                            ${kb.config_status ? `
                                                <span class="badge ${kb.config_status.status === 'configured' ? 'bg-success' : 
                                                    kb.config_status.status === 'missing' ? 'bg-warning' : 'bg-danger'}" 
                                                    title="${kb.config_status.description}">
                                                    ${kb.config_status.type === 'global' ? '全局配置' : '独立配置'}
                                                </span>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                                <p class="card-text text-muted mb-2">
                                    <small>${kb.description}</small>
                                </p>
                                <p class="card-text text-muted mb-0">
                                    <small>
                                        <strong>ID:</strong> ${kb.id}
                                    </small>
                                </p>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-secondary" onclick="editKnowledgeBase('${kb.id}')" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="syncKnowledgeBase('${kb.id}')" title="同步">
                                    <i class="bi bi-arrow-repeat"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteKnowledgeBase('${kb.id}')" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = kbsHtml;
        }



        // 加载知识库列表
        function loadKnowledgeBases() {
            // 这里应该从API加载知识库数据
            // 暂时使用空数组作为示例
            knowledgeBases = [];
            renderKnowledgeBases();
        }



        // 打开添加知识库modal
        function openAddKnowledgeBaseModal() {
            isEditMode = false;
            editingKbId = null;

            // 设置modal标题和按钮
            document.getElementById('knowledgeBaseModalTitle').textContent = '添加新知识库';
            document.getElementById('knowledgeBaseModalSubmitIcon').className = 'bi bi-plus-lg me-1';
            document.getElementById('knowledgeBaseModalSubmitText').textContent = '添加知识库';

            // 重置表单
            document.getElementById('knowledgeBaseForm').reset();

            // 重置平台配置显示
            handleKbPlatformChange();

            // 显示modal
            const modal = new bootstrap.Modal(document.getElementById('knowledgeBaseModal'));
            modal.show();
        }

        // 统一的提交函数
        function submitKnowledgeBase() {
            if (isEditMode) {
                saveKnowledgeBase();
            } else {
                addKnowledgeBase();
            }
        }

        // 清空表单
        function clearKnowledgeBaseForm() {
            document.getElementById('knowledgeBaseForm').reset();

            // 清空所有平台特定字段
            document.getElementById('difyEndpoint').value = '';
            document.getElementById('difyApiKey').value = '';
            document.getElementById('difyKbId').value = '';
            document.getElementById('ragflowKbId').value = '';

            // 重置平台配置显示
            handleKbPlatformChange();
        }

        // 添加知识库
        async function addKnowledgeBase() {
            const form = document.getElementById('knowledgeBaseForm');
            const platform = document.getElementById('kbPlatform').value;

            let endpoint, apiKey, kbId;

            // 根据平台获取不同的配置
            if (platform === 'dify') {
                showToast('Dify 平台暂不支持，请选择 RagFlow 平台', 'warning');
                return;
            } else if (platform === 'ragflow') {
                // RagFlow可以使用全局认证设置或独立配置
                kbId = document.getElementById('ragflowKbId').value;

                // 检查是否使用独立配置
                const useIndependent = document.getElementById('useIndependentConfig').checked;
                if (useIndependent) {
                    endpoint = document.getElementById('ragflowIndependentEndpoint').value;
                    const username = document.getElementById('ragflowIndependentUsername').value;
                    const password = document.getElementById('ragflowIndependentPassword').value;

                    // 验证独立配置字段
                    if (!endpoint || !endpoint.trim()) {
                        showToast('使用独立配置时请输入API端点', 'danger');
                        return;
                    }
                    if (!username || !username.trim()) {
                        showToast('使用独立配置时请输入用户名', 'danger');
                        return;
                    }
                    if (!password || !password.trim()) {
                        showToast('使用独立配置时请输入密码', 'danger');
                        return;
                    }
                }
            } else {
                showToast('请选择平台类型', 'danger');
                return;
            }

            // 验证必填字段
            if (!document.getElementById('kbName').value.trim()) {
                showToast('请输入知识库名称', 'danger');
                return;
            }

            // 根据平台验证字段
            if (platform === 'dify') {
                if (!endpoint || !endpoint.trim()) {
                    showToast('请输入API端点', 'danger');
                    return;
                }
                if (!apiKey || !apiKey.trim()) {
                    showToast('请输入API密钥', 'danger');
                    return;
                }
            }

            if (!kbId || !kbId.trim()) {
                showToast('请输入知识库ID', 'danger');
                return;
            }

            // 构建新知识库数据
            const newKbData = {
                name: document.getElementById('kbName').value,
                platform: platform,
                description: document.getElementById('kbDescription').value,
                platform_kb_id: kbId,
                is_public: document.getElementById('kbIsPublic').checked
            };

            // 根据平台添加认证信息
            if (platform === 'dify') {
                newKbData.api_endpoint = endpoint;
                newKbData.api_key = apiKey;
            } else if (platform === 'ragflow') {
                // 检查是否使用独立配置
                const useIndependent = document.getElementById('useIndependentConfig').checked;
                if (useIndependent) {
                    // 使用独立配置
                    newKbData.api_endpoint = endpoint;
                    newKbData.username = document.getElementById('ragflowIndependentUsername').value;
                    newKbData.password = document.getElementById('ragflowIndependentPassword').value;
                    newKbData.uses_global_config = false;
                } else {
                    // 使用全局配置（默认）
                    newKbData.uses_global_config = true;
                }
            }

            try {
                // 发送POST请求到后端API
                const response = await fetch('/api/knowledge-bases/knowledge-bases/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(newKbData)
                });

                if (response.ok) {
                    const createdKb = await response.json();

                    // 添加到本地数据
                    const newKb = {
                        id: createdKb.id,
                        name: createdKb.name,
                        platform: createdKb.platform,
                        description: createdKb.description,
                        api_endpoint: createdKb.api_endpoint,
                        api_key: '****', // 隐藏真实API密钥
                        platform_kb_id: createdKb.platform_kb_id,
                        status: createdKb.status,
                        is_public: createdKb.is_public,

                        created_at: createdKb.created_at
                    };

                    knowledgeBases.push(newKb);
                    renderKnowledgeBases();

                    // 关闭模态框并重置表单
                    const modal = bootstrap.Modal.getInstance(document.getElementById('knowledgeBaseModal'));
                    modal.hide();
                    clearKnowledgeBaseForm();

                    // 显示成功提示
                    showToast('知识库添加成功！', 'success');
                } else {
                    const error = await response.json();
                    console.error('Knowledge base creation failed:', {
                        status: response.status,
                        statusText: response.statusText,
                        error: error,
                        requestData: newKbData
                    });

                    let errorMessage = '添加失败: ';
                    if (error.detail) {
                        errorMessage += error.detail;
                    } else if (error.message) {
                        errorMessage += error.message;
                    } else if (typeof error === 'object') {
                        // 处理字段验证错误
                        const fieldErrors = [];
                        for (const [field, messages] of Object.entries(error)) {
                            if (Array.isArray(messages)) {
                                fieldErrors.push(`${field}: ${messages.join(', ')}`);
                            } else {
                                fieldErrors.push(`${field}: ${messages}`);
                            }
                        }
                        errorMessage += fieldErrors.join('; ');
                    } else {
                        errorMessage += '未知错误';
                    }

                    showToast(errorMessage, 'danger');
                }

            } catch (error) {
                console.error('添加知识库失败:', error);
                showToast('添加失败: 网络错误', 'danger');
            }
        }

        // 编辑知识库
        async function editKnowledgeBase(kbId) {
            try {
                // 从API获取完整的知识库信息
                const response = await fetch(`/api/knowledge-bases/knowledge-bases/${kbId}/`, {
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    showToast('获取知识库信息失败', 'danger');
                    return;
                }

                const kb = await response.json();

                // 设置编辑模式
                isEditMode = true;
                editingKbId = kbId;

                // 设置modal标题和按钮
                document.getElementById('knowledgeBaseModalTitle').textContent = '编辑知识库';
                document.getElementById('knowledgeBaseModalSubmitIcon').className = 'bi bi-check-lg me-1';
                document.getElementById('knowledgeBaseModalSubmitText').textContent = '保存更改';

                // 填充表单
                document.getElementById('kbName').value = kb.name || '';
                document.getElementById('kbPlatform').value = kb.platform || '';
                document.getElementById('kbDescription').value = kb.description || '';
                document.getElementById('kbIsPublic').checked = kb.is_public || false;

                // 根据平台填充对应的字段
                if (kb.platform === 'dify') {
                    document.getElementById('difyEndpoint').value = kb.api_endpoint || '';
                    document.getElementById('difyApiKey').value = kb.api_key || '';
                    document.getElementById('difyKbId').value = kb.platform_kb_id || '';
                } else if (kb.platform === 'ragflow') {
                    document.getElementById('ragflowKbId').value = kb.platform_kb_id || '';
                }

                // 触发平台变化事件以显示正确的配置区域
                handleKbPlatformChange();

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('knowledgeBaseModal'));
                modal.show();

            } catch (error) {
                console.error('获取知识库信息失败:', error);
                showToast('获取知识库信息失败: 网络错误', 'danger');
            }
        }

        // 保存知识库更改
        async function saveKnowledgeBase() {
            if (!editingKbId) return;

            const platform = document.getElementById('kbPlatform').value;
            let endpoint, apiKey, kbId;

            // 根据平台获取对应的配置
            if (platform === 'dify') {
                showToast('Dify 平台暂不支持，请选择 RagFlow 平台', 'warning');
                return;
            } else if (platform === 'ragflow') {
                kbId = document.getElementById('ragflowKbId').value;
            }

            // 构建更新数据
            const updateData = {
                name: document.getElementById('kbName').value,
                platform: platform,
                description: document.getElementById('kbDescription').value,
                platform_kb_id: kbId,
                is_public: document.getElementById('kbIsPublic').checked
            };

            // 根据平台添加认证信息
            if (platform === 'dify') {
                updateData.api_endpoint = endpoint;
                updateData.api_key = apiKey;
            }
            // RagFlow不需要在这里设置认证信息，使用全局设置

            try {
                // 发送PUT请求到后端API
                const response = await fetch(`/api/knowledge-bases/knowledge-bases/${editingKbId}/`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(updateData)
                });

                if (response.ok) {
                    const updatedKb = await response.json();

                    // 更新本地数据
                    const kbIndex = knowledgeBases.findIndex(k => k.id === editingKbId);
                    if (kbIndex !== -1) {
                        knowledgeBases[kbIndex] = {
                            ...knowledgeBases[kbIndex],
                            name: updatedKb.name,
                            platform: updatedKb.platform,
                            description: updatedKb.description,
                            api_endpoint: updatedKb.api_endpoint,
                            api_key: '****', // 隐藏真实API密钥
                            platform_kb_id: updatedKb.platform_kb_id,
                            is_public: updatedKb.is_public
                        };
                    }

                    renderKnowledgeBases();

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('knowledgeBaseModal'));
                    modal.hide();
                    clearKnowledgeBaseForm();

                    showToast('知识库更新成功！', 'success');
                    isEditMode = false;
                    editingKbId = null;
                } else {
                    const error = await response.json();
                    showToast(`更新失败: ${error.detail || error.message || '未知错误'}`, 'danger');
                }

            } catch (error) {
                console.error('更新知识库失败:', error);
                showToast('更新失败: 网络错误', 'danger');
            }
        }

        // 删除知识库
        async function deleteKnowledgeBase(kbId) {
            const kb = knowledgeBases.find(k => k.id === kbId);
            if (!kb) return;

            if (!confirm(`确定要删除知识库"${kb.name}"吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/knowledge-bases/knowledge-bases/${kbId}/`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCsrfToken()
                    }
                });

                if (response.ok) {
                    // 从本地数据中移除
                    knowledgeBases = knowledgeBases.filter(k => k.id !== kbId);
                    renderKnowledgeBases();
                    showToast(`知识库"${kb.name}"已删除`, 'warning');
                } else {
                    const error = await response.json();
                    showToast(`删除失败: ${error.detail || '未知错误'}`, 'danger');
                }

            } catch (error) {
                console.error('删除知识库失败:', error);
                showToast('删除失败: 网络错误', 'danger');
            }
        }

        // 同步知识库（连接测试）
        async function syncKnowledgeBase(kbId) {
            const kb = knowledgeBases.find(k => k.id === kbId);
            if (!kb) return;

            showToast('正在测试连接并同步知识库...', 'info');

            try {
                // 先测试连接
                const testResponse = await fetch(`/api/knowledge-bases/knowledge-bases/${kbId}/test_connection/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                        'X-CSRFToken': getCsrfToken()
                    }
                });

                const testResult = await testResponse.json();

                if (testResponse.ok && testResult.success) {
                    // 连接成功，执行同步
                    const syncResponse = await fetch(`/api/knowledge-bases/knowledge-bases/${kbId}/sync/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                            'X-CSRFToken': getCsrfToken()
                        }
                    });

                    const syncResult = await syncResponse.json();

                    if (syncResponse.ok && syncResult.success) {
                        // 更新本地数据
                        kb.status = 'active';
                        showToast(`${kb.name} 连接测试和同步成功！响应时间: ${(testResult.response_time * 1000).toFixed(0)}ms`, 'success');
                    } else {
                        kb.status = 'error';
                        showToast(`${kb.name} 同步失败: ${syncResult.message || '未知错误'}`, 'danger');
                    }
                } else {
                    kb.status = 'error';
                    showToast(`${kb.name} 连接测试失败: ${testResult.message || '未知错误'}`, 'danger');
                }

                renderKnowledgeBases();

            } catch (error) {
                console.error('同步知识库失败:', error);
                kb.status = 'error';
                showToast(`${kb.name} 同步失败: 网络错误`, 'danger');
                renderKnowledgeBases();
            }
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 创建toast容器（如果不存在）
            let toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // 创建toast
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast" role="alert">
                    <div class="toast-header">
                        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} text-${type} me-2"></i>
                        <strong class="me-auto">Agent Portal</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">${message}</div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();

            // 自动清理
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // 搜索和筛选功能
        function filterKnowledgeBases() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const platformFilter = document.getElementById('platformFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            const filtered = knowledgeBases.filter(kb => {
                const matchesSearch = kb.name.toLowerCase().includes(searchTerm) ||
                                    kb.description.toLowerCase().includes(searchTerm);

                const matchesPlatform = !platformFilter || kb.platform === platformFilter;
                const matchesStatus = !statusFilter || kb.status === statusFilter;

                return matchesSearch && matchesPlatform && matchesStatus;
            });

            renderKnowledgeBases(filtered);
        }

        // 处理平台选择变化
        function handleKbPlatformChange() {
            const platform = document.getElementById('kbPlatform').value;
            const difyConfig = document.getElementById('difyConfig');
            const ragflowConfig = document.getElementById('ragflowConfig');

            // 隐藏所有配置区域
            difyConfig.style.display = 'none';
            ragflowConfig.style.display = 'none';

            // 根据平台显示对应配置
            switch(platform) {
                case 'dify':
                    difyConfig.style.display = 'block';
                    // 显示不支持提示
                    showToast('Dify 平台暂不支持，请选择 RagFlow 平台', 'warning');
                    // 重置选择
                    setTimeout(() => {
                        document.getElementById('kbPlatform').value = '';
                    }, 100);
                    break;
                case 'ragflow':
                    ragflowConfig.style.display = 'block';
                    // 检查全局配置状态
                    checkRagflowGlobalConfig();
                    break;
            }
        }

        // 检查RagFlow全局配置状态
        async function checkRagflowGlobalConfig() {
            try {
                const response = await fetch('/api/knowledge-bases/ragflow-auth/', {
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`
                    }
                });

                const globalConfigAlert = document.getElementById('ragflowGlobalConfigAlert');
                const globalConfigMissing = document.getElementById('ragflowGlobalConfigMissing');

                if (response.ok) {
                    const data = await response.json();
                    if (data && data.url) {
                        // 全局配置存在
                        globalConfigAlert.style.display = 'block';
                        globalConfigMissing.style.display = 'none';
                    } else {
                        // 全局配置不存在
                        globalConfigAlert.style.display = 'none';
                        globalConfigMissing.style.display = 'block';
                    }
                } else {
                    // 全局配置不存在或获取失败
                    globalConfigAlert.style.display = 'none';
                    globalConfigMissing.style.display = 'block';
                }
            } catch (error) {
                console.error('检查RagFlow全局配置失败:', error);
                // 出错时显示缺失状态
                document.getElementById('ragflowGlobalConfigAlert').style.display = 'none';
                document.getElementById('ragflowGlobalConfigMissing').style.display = 'block';
            }
        }

        // 处理独立配置切换
        function handleIndependentConfigToggle() {
            const useIndependent = document.getElementById('useIndependentConfig').checked;
            const independentFields = document.getElementById('independentConfigFields');

            if (useIndependent) {
                independentFields.style.display = 'block';
            } else {
                independentFields.style.display = 'none';
                // 清空独立配置字段
                document.getElementById('ragflowIndependentEndpoint').value = '';
                document.getElementById('ragflowIndependentUsername').value = '';
                document.getElementById('ragflowIndependentPassword').value = '';
            }
        }

        // 获取CSRF Token
        function getCsrfToken() {
            return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
        }

        // 从API加载知识库数据
        async function loadKnowledgeBasesFromAPI() {
            try {
                const response = await fetch('/api/knowledge-bases/knowledge-bases/', {
                    headers: {
                        'Authorization': `Token ${localStorage.getItem('token') || ''}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    knowledgeBases = data.results || data;
                    renderKnowledgeBases();
                } else {
                    console.error('Failed to load knowledge bases from API');
                    // 显示空状态
                    renderKnowledgeBases();
                }
            } catch (error) {
                console.error('Error loading knowledge bases:', error);
                // 显示空状态
                renderKnowledgeBases();
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadKnowledgeBasesFromAPI();

            // 平台选择变化事件
            document.getElementById('kbPlatform').addEventListener('change', handleKbPlatformChange);

            // 独立配置切换事件
            document.getElementById('useIndependentConfig').addEventListener('change', handleIndependentConfigToggle);

            // 搜索和筛选事件
            document.getElementById('searchInput').addEventListener('input', filterKnowledgeBases);
            document.getElementById('platformFilter').addEventListener('change', filterKnowledgeBases);
            document.getElementById('statusFilter').addEventListener('change', filterKnowledgeBases);

            // 初始化时显示默认配置
            handleKbPlatformChange();
        });

        // RagFlow认证设置相关函数
        function showRagflowAuthModal() {
            // 加载现有的RagFlow认证设置
            loadRagflowAuthSettings();

            const modal = new bootstrap.Modal(document.getElementById('ragflowAuthModal'));
            modal.show();
        }

        async function loadRagflowAuthSettings() {
            try {
                const response = await fetch('/api/knowledge-bases/ragflow-auth/', {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data) {
                        document.getElementById('ragflowAuthUrl').value = data.url || '';
                        document.getElementById('ragflowAuthUsername').value = data.username || '';
                        // 显示保存的密码
                        document.getElementById('ragflowAuthPassword').value = data.password || '';
                        document.getElementById('ragflowAuthRefreshInterval').value = data.refresh_interval || 12;
                    }
                }
            } catch (error) {
                console.error('加载RagFlow认证设置失败:', error);
            }
        }

        async function saveRagflowAuth() {
            const url = document.getElementById('ragflowAuthUrl').value.trim();
            const username = document.getElementById('ragflowAuthUsername').value.trim();
            const password = document.getElementById('ragflowAuthPassword').value.trim();
            const refreshInterval = parseInt(document.getElementById('ragflowAuthRefreshInterval').value) || 12;

            // 验证必填字段
            if (!url) {
                showToast('请输入服务器地址', 'danger');
                return;
            }
            if (!username) {
                showToast('请输入用户名', 'danger');
                return;
            }
            if (!password) {
                showToast('请输入密码', 'danger');
                return;
            }

            try {
                const response = await fetch('/api/knowledge-bases/ragflow-auth/', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        url: url,
                        username: username,
                        password: password,
                        refresh_interval: refreshInterval
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    showToast('RagFlow认证设置保存成功', 'success');

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('ragflowAuthModal'));
                    modal.hide();
                } else {
                    showToast(result.message || 'RagFlow认证设置保存失败', 'danger');
                }

            } catch (error) {
                console.error('保存RagFlow认证设置失败:', error);
                showToast('保存失败: 网络错误', 'danger');
            }
        }

        async function testRagflowConnection() {
            const url = document.getElementById('ragflowAuthUrl').value.trim();
            const username = document.getElementById('ragflowAuthUsername').value.trim();
            const password = document.getElementById('ragflowAuthPassword').value.trim();

            // 验证必填字段
            if (!url || !username || !password) {
                showToast('请填写完整的认证信息', 'danger');
                return;
            }

            // 显示测试中状态
            const testBtn = document.querySelector('button[onclick="testRagflowConnection()"]');
            const originalText = testBtn.innerHTML;
            testBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>测试中...';
            testBtn.disabled = true;

            try {
                const response = await fetch('/api/knowledge-bases/ragflow-auth/test/', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        url: url,
                        username: username,
                        password: password
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showToast('RagFlow连接测试成功', 'success');
                } else {
                    showToast(result.message || 'RagFlow连接测试失败', 'danger');
                }

            } catch (error) {
                console.error('RagFlow连接测试失败:', error);
                showToast('连接测试失败: 网络错误', 'danger');
            } finally {
                // 恢复按钮状态
                testBtn.innerHTML = originalText;
                testBtn.disabled = false;
            }
        }
    </script>
{% endblock %}
