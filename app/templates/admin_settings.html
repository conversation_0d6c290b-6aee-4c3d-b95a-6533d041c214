{% extends 'base.html' %}
{% load static %}

{% block title %}管理员设置 - Agent Portal{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-border: rgba(255, 255, 255, 0.2);
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    .navbar-glass {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid var(--glass-border);
    }

    main {
        padding-top: 0 !important;
    }

    .page-header {
        background: var(--primary-gradient);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        margin-top: 0;
        padding-top: calc(3rem + 76px);
    }

    .glass-card {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .settings-header {
        background: var(--primary-gradient);
        color: white;
        padding: 1.5rem;
        border-radius: 16px 16px 0 0;
        margin-bottom: 0;
    }

    .form-section {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 0.5rem;
        color: #667eea;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-gradient {
        background: var(--primary-gradient);
        border: none;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        padding: 0.75rem 2rem;
        border-radius: 8px;
    }

    .btn-gradient:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        color: white;
    }

    .btn-gradient:disabled {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
    }

    .alert-custom {
        border: none;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .loading-spinner {
        display: none;
    }

    .loading-spinner.show {
        display: inline-block;
    }

    .stats-card {
        background: var(--secondary-gradient);
        color: white;
        border-radius: 12px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 页面头部 -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-5 fw-bold mb-3">管理员设置</h1>
                <p class="lead mb-0">配置系统参数和限制，管理平台运行状态</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="stats-card p-3 text-center">
                    <h3 class="mb-1"><i class="bi bi-gear-fill"></i></h3>
                    <small>系统配置</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container mb-5">

        <!-- 提示信息 -->
        <div id="alertContainer"></div>

        <!-- 设置表单 -->
        <div class="glass-card">
            <form id="settingsForm">
                {% csrf_token %}
                <!-- 平台标题设置 -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="bi bi-palette"></i>平台标题设置
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="siteName" class="form-label">平台名称</label>
                            <input type="text" class="form-control" id="siteName"
                                   maxlength="100" placeholder="Agent Portal" required>
                            <div class="form-text">显示在导航栏和页面标题中的平台名称</div>
                        </div>
                        <div class="col-md-6">
                            <label for="siteTitle" class="form-label">首页主标题</label>
                            <input type="text" class="form-control" id="siteTitle"
                                   maxlength="200" placeholder="AI智能体门户" required>
                            <div class="form-text">首页Hero区域显示的主标题</div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label for="siteDescription" class="form-label">首页描述</label>
                        <textarea class="form-control" id="siteDescription" rows="3"
                                  maxlength="500" placeholder="连接多个AI平台，一站式智能对话体验。支持RagFlow、Dify、OpenAI等主流平台，让AI助手触手可及。" required></textarea>
                        <div class="form-text">首页Hero区域显示的描述文字</div>
                    </div>
                </div>

                <!-- 会话管理设置 -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="bi bi-chat-dots"></i>会话管理
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="maxConversations" class="form-label">每用户最大会话数</label>
                            <input type="number" class="form-control" id="maxConversations"
                                   min="1" max="100" required>
                            <div class="form-text">用户最多可以保存的会话数量，超出时会自动删除最旧的会话</div>
                        </div>
                        <div class="col-md-6">
                            <label for="maxMessages" class="form-label">每会话最大消息数</label>
                            <input type="number" class="form-control" id="maxMessages"
                                   min="10" max="1000" required>
                            <div class="form-text">每个会话最多可以保存的消息数量</div>
                        </div>
                    </div>
                </div>

                <!-- 文件和API限制 -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="bi bi-cloud-upload"></i>文件和API限制
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="maxFileSize" class="form-label">最大文件大小 (MB)</label>
                            <input type="number" class="form-control" id="maxFileSize" 
                                   min="1" max="100" required>
                            <div class="form-text">用户上传文件的最大大小限制</div>
                        </div>
                        <div class="col-md-6">
                            <label for="apiRateLimit" class="form-label">API每分钟限制</label>
                            <input type="number" class="form-control" id="apiRateLimit" 
                                   min="10" max="1000" required>
                            <div class="form-text">每个用户每分钟最多可以调用的API次数</div>
                        </div>
                    </div>
                </div>

                <!-- 系统维护 -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="bi bi-tools"></i>系统维护
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                <label class="form-check-label" for="maintenanceMode">
                                    维护模式
                                </label>
                            </div>
                            <div class="form-text">开启后，普通用户将无法访问系统</div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="allowRegistration">
                                <label class="form-check-label" for="allowRegistration">
                                    允许用户注册
                                </label>
                            </div>
                            <div class="form-text">是否允许新用户注册账号</div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enableSearch">
                                <label class="form-check-label" for="enableSearch">
                                    启用搜索功能
                                </label>
                            </div>
                            <div class="form-text">关闭后，首页搜索入口和知识库管理菜单将隐藏</div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label for="maintenanceMessage" class="form-label">维护提示信息</label>
                        <textarea class="form-control" id="maintenanceMessage" rows="3" 
                                  maxlength="500" placeholder="维护模式下显示给用户的提示信息"></textarea>
                    </div>
                </div>

                <!-- 保存按钮 -->
                <div class="form-section">
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-gradient" id="saveBtn">
                            <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"></span>
                            <i class="bi bi-check-lg me-2"></i>保存设置
                        </button>
                    </div>
                </div>
            </form>
        </div>


</div>
{% endblock %}

{% block extra_js %}
<script>
    let isLoading = false;

    // 显示提示信息
    function showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer');
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-custom alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alertDiv);

        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }

    // 加载系统设置
    async function loadSettings() {
        try {
            const response = await fetch('/api/system-settings/', {
                headers: {
                    'Authorization': `Token ${localStorage.getItem('token') || ''}`
                }
            });

            if (response.ok) {
                const settings = await response.json();
                
                // 填充表单
                document.getElementById('siteName').value = settings.site_name || 'Agent Portal';
                document.getElementById('siteTitle').value = settings.site_title || 'AI智能体门户';
                document.getElementById('siteDescription').value = settings.site_description || '连接多个AI平台，一站式智能对话体验。支持RagFlow、Dify、OpenAI等主流平台，让AI助手触手可及。';
                document.getElementById('maxConversations').value = settings.max_conversations_per_user;
                document.getElementById('maxMessages').value = settings.max_messages_per_conversation;
                document.getElementById('maxFileSize').value = settings.max_file_size_mb;
                document.getElementById('apiRateLimit').value = settings.api_rate_limit_per_minute;
                document.getElementById('maintenanceMode').checked = settings.maintenance_mode;
                document.getElementById('allowRegistration').checked = settings.allow_registration;
                document.getElementById('enableSearch').checked = settings.enable_search;
                document.getElementById('maintenanceMessage').value = settings.maintenance_message || '';

                console.log('设置加载成功:', settings);
            } else {
                throw new Error('加载设置失败');
            }
        } catch (error) {
            console.error('加载设置失败:', error);
            showAlert('加载设置失败，请刷新页面重试', 'danger');
        }
    }

    // 保存设置
    async function saveSettings(formData) {
        try {
            const response = await fetch('/api/system-settings/update/', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${localStorage.getItem('token') || ''}`,
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                const result = await response.json();
                showAlert('设置保存成功！', 'success');
                console.log('设置保存成功:', result);
            } else {
                const error = await response.json();
                throw new Error(error.error || '保存失败');
            }
        } catch (error) {
            console.error('保存设置失败:', error);
            showAlert(`保存失败: ${error.message}`, 'danger');
        }
    }

    // 获取CSRF Token
    function getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }

    // 表单提交处理
    document.getElementById('settingsForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (isLoading) return;
        
        isLoading = true;
        const saveBtn = document.getElementById('saveBtn');
        const spinner = saveBtn.querySelector('.loading-spinner');
        
        // 显示加载状态
        saveBtn.disabled = true;
        spinner.classList.add('show');
        
        try {
            // 收集表单数据
            const formData = {
                site_name: document.getElementById('siteName').value.trim(),
                site_title: document.getElementById('siteTitle').value.trim(),
                site_description: document.getElementById('siteDescription').value.trim(),
                max_conversations_per_user: parseInt(document.getElementById('maxConversations').value),
                max_messages_per_conversation: parseInt(document.getElementById('maxMessages').value),
                max_file_size_mb: parseInt(document.getElementById('maxFileSize').value),
                api_rate_limit_per_minute: parseInt(document.getElementById('apiRateLimit').value),
                maintenance_mode: document.getElementById('maintenanceMode').checked,
                allow_registration: document.getElementById('allowRegistration').checked,
                enable_search: document.getElementById('enableSearch').checked,
                maintenance_message: document.getElementById('maintenanceMessage').value
            };
            
            await saveSettings(formData);
        } finally {
            // 恢复按钮状态
            isLoading = false;
            saveBtn.disabled = false;
            spinner.classList.remove('show');
        }
    });

    // 页面加载时获取设置
    document.addEventListener('DOMContentLoaded', function() {
        loadSettings();
    });



</script>
{% endblock %}
