{% extends 'base.html' %}
{% load static %}

{% block title %}搜索 - AI智能体门户{% endblock %}

{% block extra_css %}
<style>
    /* Ant Design X 风格的搜索页面 */
    .search-header {
        background: var(--antdx-gradient-primary);
        color: white;
        padding: 48px 0;
        margin-bottom: 48px;
        position: relative;
        overflow: hidden;
    }

    .search-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
                    linear-gradient(-45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.05) 75%),
                    linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.05) 75%);
        background-size: 20px 20px;
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        opacity: 0.3;
    }

    .search-input-large {
        height: 56px;
        font-size: 16px;
        border-radius: var(--antdx-radius-lg);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 0 24px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: var(--antdx-shadow-md);
        color: var(--antdx-text-primary);
        transition: all 0.2s ease;
    }

    .search-input-large:focus {
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow: var(--antdx-shadow-lg), 0 0 0 2px rgba(255, 255, 255, 0.2);
        outline: none;
    }

    .search-btn-large {
        height: 56px;
        padding: 0 24px;
        border-radius: 0 var(--antdx-radius-lg) var(--antdx-radius-lg) 0;
        background: var(--antdx-primary);
        border: 1px solid var(--antdx-primary);
        color: white;
        font-weight: 500;
        font-size: 16px;
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
    }

    .search-btn-large:hover {
        background: var(--antdx-primary-hover);
        border-color: var(--antdx-primary-hover);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--antdx-shadow-md);
    }

    /* Ant Design X 风格的智能回答卡片 */
    .answer-card {
        background: var(--antdx-bg-elevated);
        border: 1px solid var(--antdx-border-light);
        border-left: 4px solid var(--antdx-primary);
        padding: 24px;
        margin-top: 32px;
        margin-bottom: 32px;
        border-radius: var(--antdx-radius-xl);
        box-shadow: var(--antdx-shadow-sm);
    }

    .answer-card h5 {
        color: var(--antdx-text-primary);
        font-weight: 600;
        margin-bottom: 16px;
    }

    /* Ant Design X 风格的搜索结果卡片 */
    .result-card {
        border: 1px solid var(--antdx-border-light);
        border-radius: var(--antdx-radius-xl);
        padding: 20px;
        margin-bottom: 16px;
        transition: all 0.2s ease;
        background: var(--antdx-bg-elevated);
        box-shadow: var(--antdx-shadow-sm);
    }

    .result-card:hover {
        box-shadow: var(--antdx-shadow-md);
        transform: translateY(-2px);
        border-color: var(--antdx-border);
    }

    .result-title {
        color: var(--antdx-primary);
        text-decoration: none;
        font-weight: 600;
        font-size: 16px;
        line-height: 1.5;
    }

    .result-title:hover {
        color: var(--antdx-primary-hover);
        text-decoration: none;
    }

    .result-source {
        color: var(--antdx-text-secondary);
        font-size: 12px;
        margin: 8px 0;
    }

    .result-content {
        margin: 12px 0;
        line-height: 1.6;
        color: var(--antdx-text-primary);
        font-size: 14px;
    }

    /* Ant Design X 风格的高亮显示 */
    .highlight {
        background-color: rgba(22, 119, 255, 0.1);
        color: var(--antdx-primary);
        padding: 2px 4px;
        border-radius: var(--antdx-radius-sm);
        font-weight: 500;
    }

    /* Ant Design X 风格的相关问题区域 */
    .related-questions {
        background: var(--antdx-bg-elevated);
        border: 1px solid var(--antdx-border-light);
        border-radius: var(--antdx-radius-xl);
        padding: 24px;
        margin-top: 32px;
        box-shadow: var(--antdx-shadow-sm);
    }

    .related-questions h6 {
        color: var(--antdx-text-primary);
        font-weight: 600;
        margin-bottom: 16px;
    }

    .related-question {
        color: var(--antdx-primary);
        text-decoration: none;
        display: block;
        padding: 12px 16px;
        margin: 4px 0;
        border-radius: var(--antdx-radius-md);
        transition: all 0.2s ease;
        border: 1px solid transparent;
    }

    .related-question:hover {
        background-color: var(--antdx-primary-bg);
        border-color: var(--antdx-border);
        color: var(--antdx-primary);
        text-decoration: none;
        transform: translateX(4px);
    }

    /* 加载状态 */
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 48px;
    }

    .loading-spinner .spinner-border {
        color: var(--antdx-primary);
    }

    /* 统计信息 */
    .stats-info {
        color: var(--antdx-text-secondary);
        font-size: 14px;
        margin-top: 24px;
        margin-bottom: 16px;
        padding: 12px 0;
        border-bottom: 1px solid var(--antdx-border-light);
    }

    /* 分页容器 */
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 32px;
    }

    /* Markdown内容样式 */
    .answer-card #answerContent {
        line-height: 1.7;
    }

    .answer-card #answerContent h1,
    .answer-card #answerContent h2,
    .answer-card #answerContent h3,
    .answer-card #answerContent h4,
    .answer-card #answerContent h5,
    .answer-card #answerContent h6 {
        margin-top: 24px;
        margin-bottom: 12px;
        font-weight: 600;
        color: var(--antdx-text-primary);
    }

    .answer-card #answerContent h1 { font-size: 20px; }
    .answer-card #answerContent h2 { font-size: 18px; }
    .answer-card #answerContent h3 { font-size: 16px; }
    .answer-card #answerContent h4 { font-size: 15px; }
    .answer-card #answerContent h5 { font-size: 14px; }
    .answer-card #answerContent h6 { font-size: 14px; }

    .answer-card #answerContent p {
        margin-bottom: 1rem;
    }

    .answer-card #answerContent ul,
    .answer-card #answerContent ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }

    .answer-card #answerContent li {
        margin-bottom: 0.3rem;
    }

    .answer-card #answerContent blockquote {
        border-left: 4px solid var(--antdx-primary);
        background-color: var(--antdx-bg-layout);
        padding: 16px;
        margin: 16px 0;
        border-radius: var(--antdx-radius-md);
        font-style: italic;
        color: var(--antdx-text-secondary);
    }

    .answer-card #answerContent code {
        background-color: var(--antdx-bg-layout);
        padding: 2px 6px;
        border-radius: var(--antdx-radius-sm);
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
        font-size: 13px;
        color: var(--antdx-primary);
        border: 1px solid var(--antdx-border-light);
    }

    .answer-card #answerContent pre {
        background-color: var(--antdx-bg-layout);
        border: 1px solid var(--antdx-border-light);
        border-radius: var(--antdx-radius-md);
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;
    }

    .answer-card #answerContent pre code {
        background-color: transparent;
        padding: 0;
        color: var(--antdx-text-primary);
        border: none;
        font-size: 13px;
    }

    .answer-card #answerContent table {
        width: 100%;
        border-collapse: collapse;
        margin: 16px 0;
        border: 1px solid var(--antdx-border-light);
        border-radius: var(--antdx-radius-md);
        overflow: hidden;
    }

    .answer-card #answerContent th,
    .answer-card #answerContent td {
        border: 1px solid var(--antdx-border-light);
        padding: 12px;
        text-align: left;
    }

    .answer-card #answerContent th {
        background-color: var(--antdx-bg-layout);
        font-weight: 600;
        color: var(--antdx-text-primary);
    }

    .answer-card #answerContent a {
        color: var(--antdx-primary);
        text-decoration: none;
    }

    .answer-card #answerContent a:hover {
        color: var(--antdx-primary-hover);
        text-decoration: underline;
    }

    .answer-card #answerContent strong {
        font-weight: 600;
        color: var(--antdx-text-primary);
    }

    .answer-card #answerContent em {
        font-style: italic;
        color: var(--antdx-text-secondary);
    }

    .answer-card #answerContent hr {
        border: none;
        border-top: 1px solid var(--antdx-border-light);
        margin: 24px 0;
    }

    /* 分页组件样式 */
    .pagination .page-link {
        color: var(--antdx-text-primary);
        background-color: var(--antdx-bg-elevated);
        border: 1px solid var(--antdx-border);
        border-radius: var(--antdx-radius-md);
        margin: 0 2px;
        padding: 8px 12px;
        transition: all 0.2s ease;
    }

    .pagination .page-link:hover {
        color: var(--antdx-primary);
        background-color: var(--antdx-primary-bg);
        border-color: var(--antdx-primary);
    }

    .pagination .page-item.active .page-link {
        background-color: var(--antdx-primary);
        border-color: var(--antdx-primary);
        color: white;
    }

    /* 空状态样式 */
    #emptyState {
        color: var(--antdx-text-secondary);
    }

    #emptyState .bi-search {
        color: var(--antdx-text-tertiary);
    }
</style>
{% endblock %}

{% block content %}
<!-- 搜索头部 -->
<div class="search-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h1 class="text-center mb-4">知识搜索</h1>
                <div class="input-group">
                    <input type="text" class="form-control search-input-large" 
                           placeholder="输入您的问题，搜索知识库..." 
                           id="searchInput" value="{{ query }}">
                    <button class="btn search-btn-large" type="button" onclick="performSearch()">
                        <i class="bi bi-search me-2"></i>搜索
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- 加载状态 -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">搜索中...</span>
        </div>
        <p class="mt-2">正在搜索知识库...</p>
    </div>

    <!-- 搜索结果容器 -->
    <div id="searchResults" style="display: none;">
        <!-- 智能回答区域 -->
        <div id="intelligentAnswer" style="display: none;">
            <div class="answer-card">
                <h5><i class="bi bi-lightbulb me-2"></i>智能回答</h5>
                <div id="answerContent"></div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="bi bi-database me-1"></i>来源: <span id="answerSources"></span>
                    </small>
                </div>
            </div>
        </div>

        <!-- 搜索统计信息 -->
        <div class="stats-info" id="searchStats"></div>

        <!-- 搜索结果列表 -->
        <div id="resultsList"></div>

        <!-- 分页导航 -->
        <div class="pagination-container" id="paginationContainer"></div>

        <!-- 相关问题推荐 -->
        <div class="related-questions" id="relatedQuestions" style="display: none;">
            <h6><i class="bi bi-question-circle me-2"></i>相关问题</h6>
            <div id="relatedQuestionsList"></div>
        </div>
    </div>

    <!-- 空状态 -->
    <div id="emptyState" style="display: none;">
        <div class="text-center py-5">
            <i class="bi bi-search" style="font-size: 4rem; color: #6c757d;"></i>
            <h4 class="mt-3 text-muted">没有找到相关结果</h4>
            <p class="text-muted">请尝试使用不同的关键词进行搜索</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let currentQuery = '';
    let allSearchResults = []; // 存储所有搜索结果
    let searchMetadata = {}; // 存储搜索元数据
    const pageSize = 10;

    // 页面加载时执行搜索
    document.addEventListener('DOMContentLoaded', function() {
        const query = '{{ query }}';
        if (query.trim()) {
            performInternalSearch();
        }
    });

    // 回车搜索
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 执行搜索 - 刷新页面重新搜索
    function performSearch() {
        const query = document.getElementById('searchInput').value.trim();
        if (!query) {
            alert('请输入搜索关键词');
            return;
        }

        // 刷新页面重新搜索
        window.location.href = `/search/?q=${encodeURIComponent(query)}`;
    }

    // 内部搜索函数（页面加载时使用）
    async function performInternalSearch(page = 1) {
        const query = '{{ query }}';
        if (!query.trim()) {
            return;
        }

        currentPage = page;
        currentQuery = query;

        // 显示加载状态
        showLoading();

        try {
            // 执行搜索，获取所有结果
            const searchResponse = await searchKnowledge(query, 1, 100); // 获取更多结果

            // 存储所有结果
            allSearchResults = searchResponse.results || [];
            searchMetadata = {
                total: searchResponse.total,
                response_time: searchResponse.response_time,
                active_kb_count: searchResponse.active_kb_count
            };

            // 并行执行智能问答（不等待完成）
            getIntelligentAnswer(query);

            // 获取相关问题
            getRelatedQuestions(query);

        } catch (error) {
            console.error('搜索失败:', error);
            showError('搜索失败，请稍后重试');
            return;
        } finally {
            hideLoading();
        }

        // 显示当前页的搜索结果
        displaySearchResults();
    }

    // 搜索知识库
    async function searchKnowledge(query, page = 1, size = pageSize) {
        const response = await fetch(`/search/api/search/?q=${encodeURIComponent(query)}&page=${page}&size=${size}`);
        if (!response.ok) {
            throw new Error('搜索请求失败');
        }
        return await response.json();
    }

    // 获取智能回答（SSE流式）
    async function getIntelligentAnswer(question) {
        try {
            const response = await fetch('/search/api/ask/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({ question })
            });

            if (response.ok) {
                // 处理SSE流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                let answerContent = '';
                let kbSources = [];

                // 显示智能回答区域
                const answerContainer = document.getElementById('intelligentAnswer');
                const contentContainer = document.getElementById('answerContent');
                const sourcesContainer = document.getElementById('answerSources');

                answerContainer.style.display = 'block';
                contentContainer.innerHTML = '<div class="text-muted"><i class="bi bi-hourglass-split me-2"></i>AI正在思考...</div>';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                if (data.type === 'start') {
                                    kbSources = data.kb_sources || [];
                                    sourcesContainer.textContent = kbSources.map(kb => kb.name).join(', ');
                                } else if (data.type === 'answer') {
                                    answerContent = data.content;
                                    // 使用marked.js解析Markdown并渲染
                                    contentContainer.innerHTML = parseMarkdown(answerContent);
                                } else if (data.type === 'error') {
                                    contentContainer.innerHTML = `<div class="text-danger"><i class="bi bi-exclamation-triangle me-2"></i>${data.message}</div>`;
                                } else if (data.type === 'end') {
                                    // 流式响应结束
                                }
                            } catch (e) {
                                console.error('解析SSE数据失败:', e);
                            }
                        }
                    }
                }

                return { success: true };
            }
        } catch (error) {
            console.error('获取智能回答失败:', error);
            // 显示错误信息
            const answerContainer = document.getElementById('intelligentAnswer');
            const contentContainer = document.getElementById('answerContent');
            answerContainer.style.display = 'block';
            contentContainer.innerHTML = `<div class="text-danger"><i class="bi bi-exclamation-triangle me-2"></i>获取智能回答失败: ${error.message}</div>`;
        }
        return null;
    }

    // 获取相关问题
    async function getRelatedQuestions(question) {
        try {
            const response = await fetch('/search/api/related-questions/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({ question })
            });
            
            if (response.ok) {
                const data = await response.json();
                displayRelatedQuestions(data.questions);
            }
        } catch (error) {
            console.error('获取相关问题失败:', error);
        }
    }

    // 显示搜索结果
    function displaySearchResults() {
        const resultsContainer = document.getElementById('searchResults');
        const statsContainer = document.getElementById('searchStats');
        const listContainer = document.getElementById('resultsList');
        const emptyState = document.getElementById('emptyState');

        if (allSearchResults.length === 0) {
            resultsContainer.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        resultsContainer.style.display = 'block';

        // 计算当前页的结果
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const currentPageResults = allSearchResults.slice(startIndex, endIndex);

        // 显示统计信息
        statsContainer.innerHTML = `
            找到约 <strong>${searchMetadata.total}</strong> 条结果 (用时 ${searchMetadata.response_time.toFixed(2)} 秒)
            <span class="ms-3">搜索了 ${searchMetadata.active_kb_count} 个知识库</span>
            <span class="ms-3">第 ${currentPage} 页，共 ${Math.ceil(allSearchResults.length / pageSize)} 页</span>
        `;

        // 显示结果列表
        listContainer.innerHTML = currentPageResults.map(result => `
            <div class="result-card">
                <h6 class="result-title">${highlightText(result.title, currentQuery)}</h6>
                <div class="result-source">
                    <i class="bi bi-database me-1"></i>${result.source_name}
                    <span class="ms-2"><i class="bi bi-star me-1"></i>相关性: ${(result.relevance_score * 100).toFixed(1)}%</span>
                </div>
                <div class="result-content">
                    ${highlightText(result.summary || result.content.substring(0, 200) + '...', currentQuery)}
                </div>
                ${result.metadata && result.metadata.created_at ?
                    `<small class="text-muted"><i class="bi bi-calendar me-1"></i>${formatDate(result.metadata.created_at)}</small>` :
                    ''
                }
            </div>
        `).join('');

        // 显示分页
        displayPagination();
    }



    // 显示相关问题
    function displayRelatedQuestions(questions) {
        if (!questions || questions.length === 0) return;

        const container = document.getElementById('relatedQuestions');
        const listContainer = document.getElementById('relatedQuestionsList');

        listContainer.innerHTML = questions.map(question => `
            <a href="#" class="related-question" onclick="searchRelatedQuestion('${question.replace(/'/g, "\\'")}')">
                <i class="bi bi-arrow-right me-2"></i>${question}
            </a>
        `).join('');

        container.style.display = 'block';
    }

    // 搜索相关问题 - 刷新页面重新搜索
    function searchRelatedQuestion(question) {
        // 直接跳转到新的搜索页面
        window.location.href = `/search/?q=${encodeURIComponent(question)}`;
    }

    // 显示分页
    function displayPagination() {
        const container = document.getElementById('paginationContainer');
        const totalPages = Math.ceil(allSearchResults.length / pageSize);

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<nav><ul class="pagination">';

        // 上一页
        if (currentPage > 1) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a></li>`;
        }

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `<li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>`;
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a></li>`;
        }

        paginationHTML += '</ul></nav>';
        container.innerHTML = paginationHTML;
    }

    // 切换页面（不重新搜索）
    function changePage(page) {
        currentPage = page;
        displaySearchResults();

        // 滚动到搜索结果顶部
        document.getElementById('searchResults').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // 高亮文本
    function highlightText(text, query) {
        if (!query) return text;
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    }

    // 格式化日期
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    // 显示加载状态
    function showLoading() {
        document.getElementById('loadingSpinner').style.display = 'block';
        document.getElementById('searchResults').style.display = 'none';
        document.getElementById('emptyState').style.display = 'none';
    }

    // 隐藏加载状态
    function hideLoading() {
        document.getElementById('loadingSpinner').style.display = 'none';
    }

    // 显示错误
    function showError(message) {
        alert(message);
    }

    // 获取CSRF Token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Markdown解析函数
    function parseMarkdown(content) {
        if (typeof marked === 'undefined') {
            console.warn('Marked.js未加载，使用纯文本显示');
            return content.replace(/\n/g, '<br>');
        }

        try {
            // 配置marked选项
            marked.setOptions({
                breaks: true,        // 支持换行符转换为<br>
                gfm: true,          // 启用GitHub风格的Markdown
                sanitize: false,    // 不清理HTML（因为我们信任AI回答的内容）
                smartLists: true,   // 智能列表
                smartypants: true   // 智能标点符号
            });

            // 解析Markdown
            return marked.parse(content);
        } catch (error) {
            console.error('Markdown解析失败:', error);
            // 降级处理：简单的换行转换
            return content.replace(/\n/g, '<br>');
        }
    }
</script>
{% endblock %}
