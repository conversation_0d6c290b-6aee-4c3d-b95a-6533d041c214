{% extends 'base.html' %}
{% load static %}

{% block title %}大模型设置 - Agent Portal{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-border: rgba(255, 255, 255, 0.2);
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    .navbar-glass {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid var(--glass-border);
    }

    main {
        padding-top: 0 !important;
    }

    .page-header {
        background: var(--primary-gradient);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        margin-top: 0;
        padding-top: calc(3rem + 76px);
    }

    .glass-card {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .settings-header {
        background: var(--primary-gradient);
        color: white;
        padding: 1.5rem;
        border-radius: 16px 16px 0 0;
        margin-bottom: 0;
    }

    .form-section {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 0.5rem;
        color: #667eea;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-gradient {
        background: var(--primary-gradient);
        border: none;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        padding: 0.75rem 2rem;
        border-radius: 8px;
    }

    .btn-gradient:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        color: white;
    }

    .alert-custom {
        border: none;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .loading-spinner {
        display: none;
    }

    .loading-spinner.show {
        display: inline-block;
    }

    .card {
        border: 1px solid rgba(0, 0, 0, 0.125);
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .btn-group .btn {
        border-radius: 6px;
        margin: 0 2px;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .stats-card {
        background: var(--secondary-gradient);
        color: white;
        border-radius: 12px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 页面头部 -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-5 fw-bold mb-3">大模型设置</h1>
                <p class="lead mb-0">配置和管理大模型提供商，监控使用状态</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="stats-card p-3 text-center">
                    <h3 class="mb-1" id="totalLLMProviders">0</h3>
                    <small>已配置模型</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container mb-5">

    <!-- 提示信息 -->
    <div id="alertContainer"></div>

    <!-- CSRF Token -->
    {% csrf_token %}

    <!-- 大模型配置 -->
    <div class="glass-card">
        <div class="form-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="section-title mb-0">
                    <i class="bi bi-robot"></i>已配置的大模型
                </h5>
                <button type="button" class="btn btn-sm btn-gradient" onclick="showAddLLMModal()">
                    <i class="bi bi-plus-lg me-1"></i>添加大模型
                </button>
            </div>

            <!-- 大模型列表 -->
            <div id="llmProvidersList">
                <div class="text-center text-muted py-4">
                    <i class="bi bi-robot fs-1 opacity-50"></i>
                    <p class="mt-2">暂无配置的大模型</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑大模型模态框 -->
    <div class="modal fade" id="llmProviderModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="llmProviderModalTitle">添加大模型</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="llmProviderForm">
                        <input type="hidden" id="llmProviderId">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label for="llmName" class="form-label">显示名称 *</label>
                                <input type="text" class="form-control" id="llmName" required>
                            </div>
                            <div class="col-md-6">
                                <label for="llmProvider" class="form-label">提供商 *</label>
                                <select class="form-select" id="llmProvider" required>
                                    <option value="">请选择提供商</option>
                                    <option value="qwen">阿里云千问</option>
                                    <option value="deepseek">DeepSeek</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label for="llmModelName" class="form-label">模型名称 *</label>
                                <input type="text" class="form-control" id="llmModelName" required>
                                <div class="form-text">例如：qwen-turbo, deepseek-chat, gpt-4等</div>
                            </div>
                            <div class="col-md-6">
                                <label for="llmApiUrl" class="form-label">API端点地址 *</label>
                                <input type="url" class="form-control" id="llmApiUrl" required>
                            </div>
                        </div>

                        <div class="mt-3">
                            <label for="llmApiKey" class="form-label">API密钥 *</label>
                            <input type="password" class="form-control" id="llmApiKey" required>
                            <div class="form-text">API密钥将被安全存储</div>
                        </div>

                        <div class="mt-3">
                            <label for="llmSystemPrompt" class="form-label">系统提示词</label>
                            <textarea class="form-control" id="llmSystemPrompt" rows="4"
                                      placeholder="请输入系统提示词，用于指导AI助手的行为和回答风格..."></textarea>
                            <div class="form-text">可选，用于指导AI助手的行为和回答风格</div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label for="llmTemperature" class="form-label">温度参数</label>
                                <input type="number" class="form-control" id="llmTemperature" 
                                       min="0" max="2" step="0.1" value="0.7">
                                <div class="form-text">0.0-2.0</div>
                            </div>
                            <div class="col-md-4">
                                <label for="llmMaxTokens" class="form-label">最大令牌数</label>
                                <input type="number" class="form-control" id="llmMaxTokens" 
                                       min="1" max="32000" value="2000">
                            </div>
                            <div class="col-md-4">
                                <label for="llmTopP" class="form-label">Top-p参数</label>
                                <input type="number" class="form-control" id="llmTopP" 
                                       min="0" max="1" step="0.1" value="0.9">
                                <div class="form-text">0.0-1.0</div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="llmIsEnabled" checked>
                                    <label class="form-check-label" for="llmIsEnabled">启用此模型</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="llmIsDefault">
                                    <label class="form-check-label" for="llmIsDefault">设为默认模型</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="llmIsPublic">
                                    <label class="form-check-label" for="llmIsPublic">
                                        公开访问
                                        <i class="bi bi-info-circle ms-1" data-bs-toggle="tooltip"
                                           title="允许匿名用户使用此大模型"></i>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <label for="llmExtraConfig" class="form-label">额外配置 (JSON格式)</label>
                            <textarea class="form-control" id="llmExtraConfig" rows="3" 
                                      placeholder='{"stream": true, "frequency_penalty": 0}'></textarea>
                            <div class="form-text">可选的额外配置参数，JSON格式</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-gradient" onclick="saveLLMProvider()">
                        <span class="spinner-border spinner-border-sm me-1 d-none" id="llmSaveSpinner"></span>
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 大模型管理相关函数
    let llmProviders = [];

    // 显示提示信息
    function showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer');
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-custom alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alertDiv);

        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }

    // 获取CSRF Token
    function getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
        if (!token) {
            console.warn('CSRF token not found');
            return '';
        }
        return token;
    }

    // 页面加载时获取大模型列表
    document.addEventListener('DOMContentLoaded', function() {
        loadLLMProviders();
    });



    // 加载大模型提供商列表
    async function loadLLMProviders() {
        try {
            console.log('开始加载大模型提供商列表...');

            // 获取认证token
            const token = localStorage.getItem('token');
            console.log('Token存在:', !!token);

            const headers = {};
            if (token) {
                headers['Authorization'] = `Token ${token}`;
            }

            console.log('发送请求到:', '/api/llm-providers/');
            const response = await fetch('/api/llm-providers/', {
                headers: headers
            });

            console.log('响应状态码:', response.status);

            if (response.ok) {
                llmProviders = await response.json();
                console.log('大模型提供商加载成功:', llmProviders);
                console.log('数组长度:', llmProviders.length);
                console.log('开始渲染...');
                renderLLMProviders();
                console.log('渲染完成');
            } else {
                // 显示详细错误信息用于调试
                const errorText = await response.text();
                console.error('加载大模型提供商失败，状态码:', response.status, '错误:', errorText);

                // 如果是401错误，可能是认证问题
                if (response.status === 401) {
                    console.error('认证失败，可能需要重新登录');
                }

                llmProviders = [];
                renderLLMProviders();
            }
        } catch (error) {
            // 显示详细错误信息用于调试
            console.error('加载大模型提供商网络错误:', error);
            llmProviders = [];
            renderLLMProviders();
        }
    }

    // 渲染大模型提供商列表
    function renderLLMProviders() {
        console.log('renderLLMProviders 被调用，数据:', llmProviders);
        const container = document.getElementById('llmProvidersList');
        console.log('容器元素:', container);

        if (llmProviders.length === 0) {
            console.log('没有数据，显示空状态');
            container.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="bi bi-robot fs-1 opacity-50 mb-3"></i>
                    <h5 class="mb-2">暂无配置的大模型</h5>
                    <p class="mb-0">点击上方"添加大模型"按钮开始配置</p>
                </div>
            `;
            return;
        }

        const providersHtml = llmProviders.map(provider => `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="card-title mb-0 me-2">${provider.name}</h6>
                                ${provider.is_default ? '<span class="badge bg-primary me-2">默认</span>' : ''}
                                <span class="badge ${provider.is_enabled ? 'bg-success' : 'bg-secondary'} me-2">
                                    ${provider.is_enabled ? '启用' : '禁用'}
                                </span>
                                ${provider.is_public ? '<span class="badge bg-info me-2"><i class="bi bi-globe me-1"></i>公开</span>' : ''}
                            </div>
                            <p class="card-text text-muted mb-2">
                                <small>
                                    <strong>提供商:</strong> ${getProviderDisplayName(provider.provider)} |
                                    <strong>模型:</strong> ${provider.model_name}
                                </small>
                            </p>
                            <p class="card-text text-muted mb-0">
                                <small>
                                    <strong>使用次数:</strong> ${provider.usage_count || 0} |
                                    <strong>总令牌:</strong> ${provider.total_tokens || 0} |
                                    <strong>总费用:</strong> ¥${provider.total_cost || '0.00'}
                                </small>
                            </p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="editLLMProvider('${provider.id}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-${provider.is_enabled ? 'warning' : 'success'}"
                                    onclick="toggleLLMProvider('${provider.id}')">
                                <i class="bi bi-${provider.is_enabled ? 'pause' : 'play'}"></i>
                            </button>
                            ${!provider.is_default ? `
                                <button class="btn btn-sm btn-outline-info" onclick="setDefaultLLMProvider('${provider.id}')">
                                    <i class="bi bi-star"></i>
                                </button>
                            ` : ''}
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteLLMProvider('${provider.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = providersHtml;
        
        // 更新统计
        document.getElementById('totalLLMProviders').textContent = llmProviders.length;
    }

    // 获取提供商显示名称
    function getProviderDisplayName(provider) {
        const providerMap = {
            'qwen': '阿里云千问',
            'deepseek': 'DeepSeek',
            'custom': '自定义'
        };
        return providerMap[provider] || provider;
    }

    // 显示添加大模型模态框
    function showAddLLMModal() {
        document.getElementById('llmProviderModalTitle').textContent = '添加大模型';
        document.getElementById('llmProviderForm').reset();
        document.getElementById('llmProviderId').value = '';
        document.getElementById('llmIsEnabled').checked = true;
        document.getElementById('llmIsDefault').checked = false;
        document.getElementById('llmIsPublic').checked = false;

        const modal = new bootstrap.Modal(document.getElementById('llmProviderModal'));
        modal.show();

        // 初始化tooltip
        setTimeout(() => {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }, 100);
    }

    // 编辑大模型
    function editLLMProvider(providerId) {
        const provider = llmProviders.find(p => p.id === providerId);
        if (!provider) return;

        document.getElementById('llmProviderModalTitle').textContent = '编辑大模型';
        document.getElementById('llmProviderId').value = provider.id;
        document.getElementById('llmName').value = provider.name;
        document.getElementById('llmProvider').value = provider.provider;
        document.getElementById('llmModelName').value = provider.model_name;
        document.getElementById('llmApiUrl').value = provider.api_url;
        document.getElementById('llmApiKey').value = provider.api_key;
        document.getElementById('llmSystemPrompt').value = provider.system_prompt || '';
        document.getElementById('llmTemperature').value = provider.temperature;
        document.getElementById('llmMaxTokens').value = provider.max_tokens;
        document.getElementById('llmTopP').value = provider.top_p;
        document.getElementById('llmIsEnabled').checked = provider.is_enabled;
        document.getElementById('llmIsDefault').checked = provider.is_default;
        document.getElementById('llmIsPublic').checked = provider.is_public || false;
        document.getElementById('llmExtraConfig').value = JSON.stringify(provider.extra_config || {}, null, 2);

        const modal = new bootstrap.Modal(document.getElementById('llmProviderModal'));
        modal.show();

        // 初始化tooltip
        setTimeout(() => {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }, 100);
    }

    // 保存大模型提供商
    async function saveLLMProvider() {
        const spinner = document.getElementById('llmSaveSpinner');
        const saveBtn = spinner.parentElement;

        try {
            // 显示加载状态
            spinner.classList.remove('d-none');
            saveBtn.disabled = true;

            // 收集表单数据
            const formData = {
                name: document.getElementById('llmName').value.trim(),
                provider: document.getElementById('llmProvider').value,
                model_name: document.getElementById('llmModelName').value.trim(),
                api_url: document.getElementById('llmApiUrl').value.trim(),
                api_key: document.getElementById('llmApiKey').value.trim(),
                system_prompt: document.getElementById('llmSystemPrompt').value.trim(),
                temperature: parseFloat(document.getElementById('llmTemperature').value),
                max_tokens: parseInt(document.getElementById('llmMaxTokens').value),
                top_p: parseFloat(document.getElementById('llmTopP').value),
                is_enabled: document.getElementById('llmIsEnabled').checked,
                is_default: document.getElementById('llmIsDefault').checked,
                is_public: document.getElementById('llmIsPublic').checked
            };

            // 处理额外配置
            const extraConfigText = document.getElementById('llmExtraConfig').value.trim();
            if (extraConfigText) {
                try {
                    formData.extra_config = JSON.parse(extraConfigText);
                } catch (e) {
                    throw new Error('额外配置JSON格式错误');
                }
            } else {
                formData.extra_config = {};
            }

            // 验证必填字段
            if (!formData.name || !formData.provider || !formData.model_name ||
                !formData.api_url || !formData.api_key) {
                throw new Error('请填写所有必填字段');
            }

            const providerId = document.getElementById('llmProviderId').value;
            const isEdit = !!providerId;

            const url = isEdit ? `/api/llm-providers/${providerId}/` : '/api/llm-providers/';
            const method = isEdit ? 'PUT' : 'POST';

            console.log('发送数据:', formData);
            console.log('请求URL:', url);
            console.log('请求方法:', method);

            const headers = {
                'Content-Type': 'application/json'
            };

            const token = localStorage.getItem('token');
            if (token) {
                headers['Authorization'] = `Token ${token}`;
                console.log('使用Token认证');
            } else {
                console.warn('没有找到认证Token');
            }

            const response = await fetch(url, {
                method: method,
                headers: headers,
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('保存成功，返回数据:', result);
                showAlert(`${isEdit ? '更新' : '添加'}大模型成功！`, 'success');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('llmProviderModal'));
                modal.hide();

                // 立即重新加载列表
                console.log('重新加载大模型列表...');
                await loadLLMProviders();
            } else {
                console.error('保存失败，状态码:', response.status);
                const error = await response.json().catch(() => ({}));
                console.error('错误详情:', error);
                throw new Error(error.error || error.detail || `${isEdit ? '更新' : '添加'}失败`);
            }
        } catch (error) {
            console.error('保存大模型失败:', error);
            showAlert(`保存失败: ${error.message}`, 'danger');
        } finally {
            // 恢复按钮状态
            spinner.classList.add('d-none');
            saveBtn.disabled = false;
        }
    }

    // 切换大模型启用状态
    async function toggleLLMProvider(providerId) {
        try {
            const headers = {};
            const token = localStorage.getItem('token');
            if (token) {
                headers['Authorization'] = `Token ${token}`;
            }

            const response = await fetch(`/api/llm-providers/${providerId}/toggle_enabled/`, {
                method: 'POST',
                headers: headers
            });

            if (response.ok) {
                showAlert('状态切换成功！', 'success');
                await loadLLMProviders();
            } else {
                if (response.status === 403) {
                    showAlert('权限不足，无法执行此操作', 'warning');
                } else {
                    const error = await response.json().catch(() => ({}));
                    showAlert(error.error || '状态切换失败，请稍后重试', 'danger');
                }
            }
        } catch (error) {
            console.error('切换状态失败:', error);
            showAlert('网络错误，请检查连接后重试', 'danger');
        }
    }

    // 设置默认大模型
    async function setDefaultLLMProvider(providerId) {
        try {
            const headers = {};
            const token = localStorage.getItem('token');
            if (token) {
                headers['Authorization'] = `Token ${token}`;
            }

            const response = await fetch(`/api/llm-providers/${providerId}/set_default/`, {
                method: 'POST',
                headers: headers
            });

            if (response.ok) {
                showAlert('设置默认模型成功！', 'success');
                await loadLLMProviders();
            } else {
                if (response.status === 403) {
                    showAlert('权限不足，无法执行此操作', 'warning');
                } else {
                    const error = await response.json().catch(() => ({}));
                    showAlert(error.error || '设置默认模型失败，请稍后重试', 'danger');
                }
            }
        } catch (error) {
            console.error('设置默认模型失败:', error);
            showAlert('网络错误，请检查连接后重试', 'danger');
        }
    }

    // 删除大模型
    async function deleteLLMProvider(providerId) {
        const provider = llmProviders.find(p => p.id === providerId);
        if (!provider) return;

        if (!confirm(`确定要删除大模型 "${provider.name}" 吗？此操作不可撤销。`)) {
            return;
        }

        try {
            const headers = {};
            const token = localStorage.getItem('token');
            if (token) {
                headers['Authorization'] = `Token ${token}`;
            }

            const response = await fetch(`/api/llm-providers/${providerId}/`, {
                method: 'DELETE',
                headers: headers
            });

            if (response.ok) {
                showAlert('删除大模型成功！', 'success');
                await loadLLMProviders();
            } else {
                if (response.status === 403) {
                    showAlert('权限不足，无法执行此操作', 'warning');
                } else if (response.status === 404) {
                    showAlert('大模型不存在，可能已被删除', 'warning');
                    await loadLLMProviders(); // 刷新列表
                } else {
                    const error = await response.json().catch(() => ({}));
                    showAlert(error.error || '删除失败，请稍后重试', 'danger');
                }
            }
        } catch (error) {
            console.error('删除大模型失败:', error);
            showAlert('网络错误，请检查连接后重试', 'danger');
        }
    }
</script>
{% endblock %}
