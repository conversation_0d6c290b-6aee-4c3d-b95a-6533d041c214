import uuid
from django.db import models
from django.conf import settings


class KnowledgeBase(models.Model):
    """知识库模型"""

    PLATFORM_CHOICES = [
        ('dify', 'Dify'),
        ('ragflow', 'RagFlow'),
    ]

    STATUS_CHOICES = [
        ('active', '激活'),
        ('inactive', '未激活'),
        ('error', '错误'),
    ]

    # 主键
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name='ID')

    # 基础信息
    name = models.CharField(max_length=100, verbose_name='知识库名称')
    description = models.TextField(max_length=500, blank=True, verbose_name='描述')

    # 平台信息
    platform = models.CharField(max_length=20, choices=PLATFORM_CHOICES, verbose_name='平台')
    platform_kb_id = models.CharField(max_length=200, verbose_name='平台知识库ID')

    # 配置信息
    api_endpoint = models.URLField(blank=True, verbose_name='API端点')
    api_key = models.CharField(max_length=200, blank=True, verbose_name='API密钥')

    # RagFlow用户名密码认证（新增）
    username = models.CharField(max_length=100, blank=True, verbose_name='用户名')
    password = models.CharField(max_length=200, blank=True, verbose_name='密码')

    # 全局配置使用标识
    uses_global_config = models.BooleanField(default=False, verbose_name='使用全局配置')

    # 认证令牌刷新配置
    auth_token = models.TextField(blank=True, verbose_name='认证令牌')
    token_refresh_interval = models.PositiveIntegerField(default=12, verbose_name='令牌刷新间隔(小时)')
    last_token_refresh = models.DateTimeField(null=True, blank=True, verbose_name='最后令牌刷新时间')

    config_data = models.JSONField(default=dict, blank=True, verbose_name='配置数据')

    # 状态信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='状态')
    is_public = models.BooleanField(default=False, verbose_name='公开')

    # 统计信息
    document_count = models.PositiveIntegerField(default=0, verbose_name='文档数量')
    usage_count = models.PositiveIntegerField(default=0, verbose_name='使用次数')
    last_sync_at = models.DateTimeField(null=True, blank=True, verbose_name='最后同步时间')

    # 关联信息
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='knowledge_bases', verbose_name='所有者')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '知识库'
        verbose_name_plural = '知识库'
        ordering = ['-created_at']
        unique_together = ['platform', 'platform_kb_id', 'owner']

    def __str__(self):
        return f'{self.name} ({self.platform})'

    def get_config(self):
        """获取配置数据"""
        return self.config_data

    def set_config(self, config):
        """设置配置数据"""
        self.config_data = config
        self.save()

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])

    def is_ragflow_platform(self):
        """判断是否为RagFlow平台"""
        return self.platform == 'ragflow'

    def uses_username_auth(self):
        """判断是否使用用户名密码认证"""
        return self.is_ragflow_platform() and self.username and self.password

    def uses_global_auth(self):
        """判断是否使用全局认证配置"""
        return self.is_ragflow_platform() and self.uses_global_config

    def needs_token_refresh(self):
        """判断是否需要刷新令牌"""
        if not self.is_ragflow_platform() or not self.uses_username_auth():
            return False

        if not self.last_token_refresh:
            return True

        from django.utils import timezone
        from datetime import timedelta

        refresh_threshold = timezone.now() - timedelta(hours=self.token_refresh_interval)
        return self.last_token_refresh < refresh_threshold

    def get_auth_header(self):
        """获取认证头"""
        if self.is_ragflow_platform() and self.auth_token:
            return self.auth_token
        elif self.api_key:
            return f"Bearer {self.api_key}"
        return None

    def update_auth_token(self, token):
        """更新认证令牌"""
        from django.utils import timezone

        self.auth_token = token
        self.last_token_refresh = timezone.now()
        self.save(update_fields=['auth_token', 'last_token_refresh'])


class RagFlowGlobalAuth(models.Model):
    """RagFlow全局认证设置模型"""

    # 使用单例模式，只允许一条记录
    id = models.AutoField(primary_key=True)

    # 认证信息
    url = models.URLField(verbose_name='RagFlow服务地址')
    username = models.CharField(max_length=100, verbose_name='用户名')
    password = models.CharField(max_length=200, verbose_name='密码')
    refresh_interval = models.PositiveIntegerField(default=12, verbose_name='令牌刷新间隔(小时)')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = 'RagFlow全局认证设置'
        verbose_name_plural = 'RagFlow全局认证设置'

    def __str__(self):
        return f"RagFlow认证设置 ({self.username}@{self.url})"

    @classmethod
    def get_settings(cls):
        """获取全局认证设置"""
        try:
            return cls.objects.first()
        except cls.DoesNotExist:
            return None

    @classmethod
    def save_settings(cls, url, username, password, refresh_interval=12):
        """保存全局认证设置"""
        # 删除现有设置（单例模式）
        cls.objects.all().delete()

        # 创建新设置
        return cls.objects.create(
            url=url,
            username=username,
            password=password,
            refresh_interval=refresh_interval
        )


class KnowledgeTag(models.Model):
    """知识标签模型"""

    name = models.CharField(max_length=50, unique=True, verbose_name='标签名称')
    description = models.TextField(max_length=200, blank=True, verbose_name='描述')
    color = models.CharField(max_length=7, default='#007bff', verbose_name='颜色')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '知识标签'
        verbose_name_plural = '知识标签'
        ordering = ['name']

    def __str__(self):
        return self.name


class Knowledge(models.Model):
    """知识条目模型"""

    TYPE_CHOICES = [
        ('document', '文档'),
        ('qa', '问答对'),
        ('text', '文本片段'),
    ]

    # 主键
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name='ID')

    # 基础信息
    title = models.CharField(max_length=200, verbose_name='标题')
    content = models.TextField(verbose_name='内容')
    summary = models.TextField(max_length=500, blank=True, verbose_name='摘要')
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='document', verbose_name='类型')

    # 关联信息
    knowledge_base = models.ForeignKey(KnowledgeBase, on_delete=models.CASCADE, related_name='knowledge_items', verbose_name='知识库')

    # 平台信息
    platform_item_id = models.CharField(max_length=200, blank=True, verbose_name='平台条目ID')

    # 元数据
    metadata = models.JSONField(default=dict, blank=True, verbose_name='元数据')
    tags = models.ManyToManyField(KnowledgeTag, blank=True, verbose_name='标签')

    # 统计信息
    view_count = models.PositiveIntegerField(default=0, verbose_name='查看次数')
    usage_count = models.PositiveIntegerField(default=0, verbose_name='使用次数')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '知识条目'
        verbose_name_plural = '知识条目'
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])
