"""
管理命令：将RagFlow认证设置从缓存迁移到数据库
"""

from django.core.management.base import BaseCommand
from django.core.cache import cache
from knowledge_bases.models import RagFlowGlobalAuth


class Command(BaseCommand):
    help = '将RagFlow认证设置从缓存迁移到数据库'

    def handle(self, *args, **options):
        # 缓存键
        RAGFLOW_AUTH_CACHE_KEY = 'ragflow_global_auth_settings'
        
        try:
            # 从缓存获取现有设置
            auth_settings = cache.get(RAGFLOW_AUTH_CACHE_KEY)
            
            if auth_settings:
                # 检查数据库中是否已有设置
                existing_settings = RagFlowGlobalAuth.get_settings()
                
                if existing_settings:
                    self.stdout.write(
                        self.style.WARNING('数据库中已存在RagFlow认证设置，跳过迁移')
                    )
                    return
                
                # 保存到数据库
                RagFlowGlobalAuth.save_settings(
                    url=auth_settings.get('url', ''),
                    username=auth_settings.get('username', ''),
                    password=auth_settings.get('password', ''),
                    refresh_interval=auth_settings.get('refresh_interval', 12)
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'成功将RagFlow认证设置迁移到数据库: '
                        f'{auth_settings.get("username")}@{auth_settings.get("url")}'
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING('缓存中没有找到RagFlow认证设置')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'迁移失败: {str(e)}')
            )
