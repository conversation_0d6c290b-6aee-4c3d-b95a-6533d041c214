from django.contrib import admin
from .models import KnowledgeBase, Knowledge, KnowledgeTag


@admin.register(KnowledgeBase)
class KnowledgeBaseAdmin(admin.ModelAdmin):
    """知识库管理"""

    list_display = ('name', 'platform', 'owner', 'status', 'is_public',
                   'document_count', 'usage_count', 'created_at')
    list_filter = ('platform', 'status', 'is_public', 'created_at')
    search_fields = ('name', 'description', 'owner__username')
    ordering = ('-created_at',)

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'owner')
        }),
        ('平台配置', {
            'fields': ('platform', 'platform_kb_id', 'api_endpoint', 'api_key', 'config_data')
        }),
        ('状态信息', {
            'fields': ('status', 'is_public')
        }),
        ('统计信息', {
            'fields': ('document_count', 'usage_count', 'last_sync_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')


@admin.register(Knowledge)
class KnowledgeAdmin(admin.ModelAdmin):
    """知识条目管理"""

    list_display = ('title', 'knowledge_base', 'type', 'view_count', 'usage_count', 'created_at')
    list_filter = ('type', 'knowledge_base__platform', 'created_at')
    search_fields = ('title', 'content', 'summary')
    ordering = ('-created_at',)

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'content', 'summary', 'type')
        }),
        ('关联信息', {
            'fields': ('knowledge_base', 'platform_item_id')
        }),
        ('元数据', {
            'fields': ('metadata', 'tags'),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': ('view_count', 'usage_count'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')
    filter_horizontal = ('tags',)


@admin.register(KnowledgeTag)
class KnowledgeTagAdmin(admin.ModelAdmin):
    """知识标签管理"""

    list_display = ('name', 'description', 'color', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('name',)
