"""
RagFlow认证设置API视图
"""

import json
import logging
from django.conf import settings
from django.core.cache import cache
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import ensure_csrf_cookie
from .ragflow_auth_service import RagFlowAuthService
from .models import RagFlowGlobalAuth

logger = logging.getLogger(__name__)

# RagFlow认证设置的缓存键
RAGFLOW_AUTH_CACHE_KEY = 'ragflow_global_auth_settings'


class RagFlowAuthView(View):
    """RagFlow认证设置视图"""
    
    @method_decorator(login_required)
    def get(self, request):
        """获取RagFlow认证设置"""
        try:
            # 优先从缓存获取设置
            auth_settings = cache.get(RAGFLOW_AUTH_CACHE_KEY)

            if not auth_settings:
                # 缓存中没有，从数据库获取
                db_settings = RagFlowGlobalAuth.get_settings()
                if db_settings:
                    auth_settings = {
                        'url': db_settings.url,
                        'username': db_settings.username,
                        'password': db_settings.password,
                        'refresh_interval': db_settings.refresh_interval
                    }
                    # 重新缓存到内存中
                    cache.set(RAGFLOW_AUTH_CACHE_KEY, auth_settings, 24 * 3600)

            if auth_settings:
                # 返回包括密码在内的所有设置信息
                settings = {
                    'url': auth_settings.get('url', ''),
                    'username': auth_settings.get('username', ''),
                    'password': auth_settings.get('password', ''),
                    'refresh_interval': auth_settings.get('refresh_interval', 12)
                }
                return JsonResponse(settings)
            else:
                return JsonResponse({})

        except Exception as e:
            logger.error(f"获取RagFlow认证设置失败: {str(e)}")
            return JsonResponse({'error': '获取设置失败'}, status=500)
    
    @method_decorator(login_required)
    def post(self, request):
        """保存RagFlow认证设置"""
        try:
            data = json.loads(request.body)
            
            url = data.get('url', '').strip()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()
            refresh_interval = int(data.get('refresh_interval', 12))
            
            # 验证必填字段
            if not url or not username or not password:
                return JsonResponse({
                    'success': False,
                    'message': '请填写完整的认证信息'
                }, status=400)
            
            # 验证刷新间隔
            if refresh_interval < 1 or refresh_interval > 48:
                return JsonResponse({
                    'success': False,
                    'message': '刷新间隔必须在1-48小时之间'
                }, status=400)
            
            # 保存到数据库（持久化存储）
            RagFlowGlobalAuth.save_settings(url, username, password, refresh_interval)

            # 同时保存到缓存（提高访问速度）
            auth_settings = {
                'url': url,
                'username': username,
                'password': password,
                'refresh_interval': refresh_interval
            }

            # 缓存24小时
            cache.set(RAGFLOW_AUTH_CACHE_KEY, auth_settings, 24 * 3600)

            logger.info(f"RagFlow认证设置已保存到数据库和缓存: {username}@{url}")

            return JsonResponse({
                'success': True,
                'message': 'RagFlow认证设置保存成功'
            })
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'message': '请求数据格式错误'
            }, status=400)
        except Exception as e:
            logger.error(f"保存RagFlow认证设置失败: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': '保存设置失败'
            }, status=500)


class RagFlowAuthTestView(View):
    """RagFlow认证测试视图"""
    
    @method_decorator(login_required)
    def post(self, request):
        """测试RagFlow连接"""
        try:
            data = json.loads(request.body)
            
            url = data.get('url', '').strip()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()
            
            # 验证必填字段
            if not url or not username or not password:
                return JsonResponse({
                    'success': False,
                    'message': '请填写完整的认证信息'
                }, status=400)
            
            # 创建临时的知识库对象用于测试
            class TempKB:
                def __init__(self, url, username, password):
                    self.api_endpoint = url
                    self.username = username
                    self.password = password
                    self.name = 'Test Connection'
                    self.token_refresh_interval = 12  # 默认12小时
                    self.auth_token = None
                    self.last_token_refresh = None

                def needs_token_refresh(self):
                    return True

                def update_auth_token(self, token):
                    from django.utils import timezone
                    self.auth_token = token
                    self.last_token_refresh = timezone.now()

                def is_ragflow_platform(self):
                    return True

                def uses_username_auth(self):
                    return True
            
            # 创建临时知识库对象
            temp_kb = TempKB(url, username, password)
            
            # 测试连接
            auth_service = RagFlowAuthService(temp_kb)
            success, message = auth_service.test_connection()
            
            if success:
                return JsonResponse({
                    'success': True,
                    'message': message
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': message
                }, status=400)
                
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'message': '请求数据格式错误'
            }, status=400)
        except Exception as e:
            logger.error(f"RagFlow连接测试失败: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': f'连接测试失败: {str(e)}'
            }, status=500)


def get_ragflow_global_auth():
    """
    获取全局RagFlow认证设置

    Returns:
        dict or None: 认证设置字典，如果没有设置则返回None
    """
    # 优先从缓存获取
    auth_settings = cache.get(RAGFLOW_AUTH_CACHE_KEY)

    if not auth_settings:
        # 缓存中没有，从数据库获取
        db_settings = RagFlowGlobalAuth.get_settings()
        if db_settings:
            auth_settings = {
                'url': db_settings.url,
                'username': db_settings.username,
                'password': db_settings.password,
                'refresh_interval': db_settings.refresh_interval
            }
            # 重新缓存到内存中
            cache.set(RAGFLOW_AUTH_CACHE_KEY, auth_settings, 24 * 3600)

    return auth_settings


def create_ragflow_kb_with_global_auth(kb_id):
    """
    使用全局认证设置创建RagFlow知识库对象
    
    Args:
        kb_id: 知识库ID
        
    Returns:
        临时知识库对象或None
    """
    auth_settings = get_ragflow_global_auth()
    if not auth_settings:
        return None
    
    class GlobalAuthKB:
        def __init__(self, auth_settings, kb_id):
            self.api_endpoint = auth_settings['url']
            self.username = auth_settings['username']
            self.password = auth_settings['password']
            self.platform_kb_id = kb_id
            self.name = f'RagFlow KB {kb_id}'
            self.token_refresh_interval = auth_settings.get('refresh_interval', 12)
            self._auth_token = None
            self._last_token_refresh = None
        
        def needs_token_refresh(self):
            if not self._auth_token or not self._last_token_refresh:
                return True
            
            from django.utils import timezone
            from datetime import timedelta
            
            refresh_threshold = timezone.now() - timedelta(hours=self.token_refresh_interval)
            return self._last_token_refresh < refresh_threshold
        
        def update_auth_token(self, token):
            from django.utils import timezone
            
            self._auth_token = token
            self._last_token_refresh = timezone.now()
            
            # 更新缓存中的令牌
            cache_key = f'ragflow_kb_token_{self.platform_kb_id}'
            cache.set(cache_key, {
                'token': token,
                'refresh_time': self._last_token_refresh.isoformat()
            }, self.token_refresh_interval * 3600)
        
        @property
        def auth_token(self):
            if not self._auth_token:
                # 尝试从缓存加载令牌
                cache_key = f'ragflow_kb_token_{self.platform_kb_id}'
                cached_token = cache.get(cache_key)
                if cached_token:
                    self._auth_token = cached_token['token']
                    from django.utils import timezone
                    self._last_token_refresh = timezone.datetime.fromisoformat(cached_token['refresh_time'])
            
            return self._auth_token
        
        def is_ragflow_platform(self):
            return True
        
        def uses_username_auth(self):
            return True
    
    return GlobalAuthKB(auth_settings, kb_id)
