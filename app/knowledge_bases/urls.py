from django.urls import path, include
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>
from . import views
from .ragflow_auth_views import Rag<PERSON><PERSON>AuthView, RagFlowAuthTestView

# API路由
router = DefaultRouter()
router.register(r'knowledge-bases', views.KnowledgeBaseViewSet, basename='knowledge-base')
router.register(r'knowledge', views.KnowledgeViewSet, basename='knowledge')
router.register(r'knowledge-tags', views.KnowledgeTagViewSet, basename='knowledge-tag')

urlpatterns = [
    # DRF路由
    path('', include(router.urls)),

    # 知识库统计API
    path('stats/', views.knowledge_base_stats_view, name='knowledge-base-stats'),

    # RagFlow认证设置API
    path('ragflow-auth/', RagFlowAuthView.as_view(), name='ragflow-auth'),
    path('ragflow-auth/test/', RagFlowAuthTestView.as_view(), name='ragflow-auth-test'),
]
