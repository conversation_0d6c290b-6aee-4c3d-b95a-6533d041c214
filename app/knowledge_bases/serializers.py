from rest_framework import serializers
from .models import KnowledgeBase, Knowledge, KnowledgeTag


class KnowledgeTagSerializer(serializers.ModelSerializer):
    """知识标签序列化器"""

    class Meta:
        model = KnowledgeTag
        fields = '__all__'


class KnowledgeBaseSerializer(serializers.ModelSerializer):
    """知识库序列化器"""

    owner_username = serializers.CharField(source='owner.username', read_only=True)

    class Meta:
        model = KnowledgeBase
        fields = '__all__'
        read_only_fields = ('owner', 'usage_count', 'last_sync_at', 'created_at', 'updated_at')


class KnowledgeBaseCreateSerializer(serializers.ModelSerializer):
    """知识库创建序列化器"""

    class Meta:
        model = KnowledgeBase
        fields = ('id', 'name', 'description', 'platform', 'platform_kb_id',
                 'api_endpoint', 'api_key', 'username', 'password', 'uses_global_config',
                 'token_refresh_interval', 'config_data', 'is_public', 'status',
                 'document_count', 'usage_count', 'created_at', 'updated_at')
        read_only_fields = ('id', 'status', 'document_count', 'usage_count',
                           'created_at', 'updated_at', 'auth_token', 'last_token_refresh')
        extra_kwargs = {
            'password': {'write_only': True},  # 密码只写不读
        }

    def validate(self, data):
        """验证数据"""
        # 检查唯一约束：同一用户在同一平台上不能有相同的platform_kb_id
        user = self.context['request'].user
        platform = data.get('platform')
        platform_kb_id = data.get('platform_kb_id')

        if platform and platform_kb_id:
            existing = KnowledgeBase.objects.filter(
                owner=user,
                platform=platform,
                platform_kb_id=platform_kb_id
            ).exists()

            if existing:
                raise serializers.ValidationError({
                    'platform_kb_id': f'您已经在{platform}平台上添加了ID为"{platform_kb_id}"的知识库'
                })

        # 验证RagFlow平台的认证字段
        if platform == 'ragflow':
            # 检查是否使用全局认证设置
            uses_global_config = data.get('uses_global_config', True)  # 默认使用全局配置

            if uses_global_config:
                # 使用全局认证设置，从全局设置中获取认证信息
                from .ragflow_auth_views import get_ragflow_global_auth

                global_auth = get_ragflow_global_auth()
                if not global_auth:
                    raise serializers.ValidationError({
                        'platform': 'RagFlow平台需要先配置全局认证设置'
                    })

                # 自动设置api_endpoint和认证信息
                data['api_endpoint'] = global_auth['url']
                data['username'] = global_auth['username']
                data['password'] = global_auth['password']
                data['token_refresh_interval'] = global_auth.get('refresh_interval', 12)
                data['uses_global_config'] = True
            else:
                # 使用独立认证配置，验证必要字段
                username = data.get('username')
                password = data.get('password')
                api_endpoint = data.get('api_endpoint')

                if not username or not password:
                    raise serializers.ValidationError({
                        'username': 'RagFlow平台使用独立配置时需要提供用户名和密码'
                    })
                if not api_endpoint:
                    raise serializers.ValidationError({
                        'api_endpoint': 'RagFlow平台使用独立配置时需要提供API端点'
                    })
                data['uses_global_config'] = False

        elif platform == 'dify':
            api_key = data.get('api_key')
            api_endpoint = data.get('api_endpoint')
            if not api_key:
                raise serializers.ValidationError({
                    'api_key': 'Dify平台需要提供API密钥'
                })
            if not api_endpoint:
                raise serializers.ValidationError({
                    'api_endpoint': 'Dify平台需要提供API端点'
                })

        return data

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return KnowledgeBase.objects.create(**validated_data)


class KnowledgeBaseListSerializer(serializers.ModelSerializer):
    """知识库列表序列化器"""

    owner_username = serializers.CharField(source='owner.username', read_only=True)
    config_status = serializers.SerializerMethodField()

    class Meta:
        model = KnowledgeBase
        fields = ('id', 'name', 'description', 'platform', 'platform_kb_id', 'status', 'is_public',
                 'uses_global_config', 'config_status', 'document_count', 'usage_count',
                 'owner_username', 'created_at', 'updated_at')

    def get_config_status(self, obj):
        """获取配置状态信息"""
        if obj.platform == 'ragflow':
            if obj.uses_global_config:
                # 检查全局配置是否存在
                from .ragflow_auth_views import get_ragflow_global_auth
                global_auth = get_ragflow_global_auth()
                return {
                    'type': 'global',
                    'status': 'configured' if global_auth else 'missing',
                    'description': '使用全局认证配置' if global_auth else '全局认证配置缺失'
                }
            else:
                # 使用独立配置
                has_auth = bool(obj.username and obj.password and obj.api_endpoint)
                return {
                    'type': 'independent',
                    'status': 'configured' if has_auth else 'incomplete',
                    'description': '使用独立认证配置' if has_auth else '独立认证配置不完整'
                }
        elif obj.platform == 'dify':
            has_auth = bool(obj.api_key and obj.api_endpoint)
            return {
                'type': 'independent',
                'status': 'configured' if has_auth else 'incomplete',
                'description': 'Dify独立配置' if has_auth else 'Dify配置不完整'
            }
        else:
            return {
                'type': 'unknown',
                'status': 'unknown',
                'description': '未知平台'
            }


class KnowledgeSerializer(serializers.ModelSerializer):
    """知识条目序列化器"""

    tags = KnowledgeTagSerializer(many=True, read_only=True)
    tag_names = serializers.ListField(
        child=serializers.CharField(max_length=50),
        write_only=True,
        required=False,
        help_text="标签名称列表"
    )
    knowledge_base_name = serializers.CharField(source='knowledge_base.name', read_only=True)

    class Meta:
        model = Knowledge
        fields = '__all__'
        read_only_fields = ('view_count', 'usage_count', 'created_at', 'updated_at')

    def create(self, validated_data):
        tag_names = validated_data.pop('tag_names', [])
        knowledge = Knowledge.objects.create(**validated_data)

        # 处理标签
        for tag_name in tag_names:
            tag, created = KnowledgeTag.objects.get_or_create(name=tag_name)
            knowledge.tags.add(tag)

        return knowledge

    def update(self, instance, validated_data):
        tag_names = validated_data.pop('tag_names', None)
        
        # 更新基本字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 处理标签
        if tag_names is not None:
            instance.tags.clear()
            for tag_name in tag_names:
                tag, created = KnowledgeTag.objects.get_or_create(name=tag_name)
                instance.tags.add(tag)

        return instance


class KnowledgeListSerializer(serializers.ModelSerializer):
    """知识条目列表序列化器"""

    knowledge_base_name = serializers.CharField(source='knowledge_base.name', read_only=True)
    tag_count = serializers.SerializerMethodField()

    class Meta:
        model = Knowledge
        fields = ('id', 'title', 'summary', 'type', 'knowledge_base_name', 
                 'tag_count', 'view_count', 'usage_count', 'created_at', 'updated_at')

    def get_tag_count(self, obj):
        return obj.tags.count()


class KnowledgeSearchSerializer(serializers.Serializer):
    """知识搜索序列化器"""

    query = serializers.CharField(help_text="搜索关键词")
    knowledge_base_id = serializers.UUIDField(required=False, help_text="知识库ID")
    type = serializers.ChoiceField(
        choices=Knowledge.TYPE_CHOICES,
        required=False,
        help_text="知识类型"
    )
    limit = serializers.IntegerField(default=10, min_value=1, max_value=100, help_text="返回结果数量")


class KnowledgeBaseStatsSerializer(serializers.Serializer):
    """知识库统计序列化器"""

    total_knowledge_bases = serializers.IntegerField()
    active_knowledge_bases = serializers.IntegerField()
    public_knowledge_bases = serializers.IntegerField()
    total_documents = serializers.IntegerField()
    total_usage = serializers.IntegerField()
    platform_distribution = serializers.DictField()
    recent_activity = KnowledgeBaseListSerializer(many=True, read_only=True)
