"""
RagFlow认证服务

处理RagFlow平台的用户名密码认证和令牌管理
"""

import base64
import json
import logging
import requests
from datetime import datetime, timedelta
from django.utils import timezone
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5

logger = logging.getLogger(__name__)


class RagFlowAuthService:
    """RagFlow认证服务"""
    
    # RagFlow的公钥（来自前端代码）
    PUBLIC_KEY_PEM = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArq9XTUSeYr2+N1h3Afl/z8Dse/2yD0ZGrKwx+EEEcdsBLca9Ynmx3nIB5obmLlSfmskLpBo0UACBmB5rEjBp2Q2f3AG3Hjd4B+gNCG6BDaawuDlgANIhGnaTLrIqWrrcm4EMzJOnAOI1fgzJRsOOUEfaS318Eq9OVO3apEyCCt0lOQK6PuksduOjVxtltDav+guVAA068NrPYmRNabVKRNLJpL8w4D44sfth5RvZ3q9t+6RTArpEtc5sh5ChzvqPOzKGMXW83C95TxmXqpbK6olN4RevSfVjEAgCydH6HN6OhtOQEcnrU97r9H0iZOWwbw3pVrZiUkuRD1R56Wzs2wIDAQAB
-----END PUBLIC KEY-----"""
    
    def __init__(self, knowledge_base):
        """
        初始化认证服务
        
        Args:
            knowledge_base: KnowledgeBase实例
        """
        self.knowledge_base = knowledge_base
        self.base_url = knowledge_base.api_endpoint.rstrip('/')
        self.username = knowledge_base.username
        self.password = knowledge_base.password
        self.timeout = 30
        
        # 设置请求会话
        self.session = requests.Session()
        self.session.timeout = self.timeout
    
    def encrypt_password(self, password):
        """
        使用RagFlow的RSA加密方法加密密码
        
        Args:
            password: 明文密码
            
        Returns:
            str: 加密后的密码
        """
        try:
            # 导入公钥
            rsa_key = RSA.importKey(self.PUBLIC_KEY_PEM)
            cipher = PKCS1_v1_5.new(rsa_key)
            
            # 先Base64编码密码
            password_b64 = base64.b64encode(password.encode('utf-8')).decode('utf-8')
            
            # 加密Base64编码后的密码
            encrypted = cipher.encrypt(password_b64.encode('utf-8'))
            
            # Base64编码加密结果
            return base64.b64encode(encrypted).decode('utf-8')
            
        except Exception as e:
            logger.error(f"密码加密失败: {str(e)}")
            raise
    
    def login(self):
        """
        登录RagFlow获取Authorization令牌
        
        Returns:
            tuple: (success, auth_token)
        """
        try:
            login_url = f"{self.base_url}/v1/user/login"
            
            # 加密密码
            encrypted_password = self.encrypt_password(self.password)
            
            login_data = {
                "email": self.username,
                "password": encrypted_password
            }
            
            logger.info(f"尝试登录RagFlow: {self.username}")
            
            response = self.session.post(
                login_url,
                json=login_data,
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                logger.error(f"登录失败，状态码: {response.status_code}")
                return False, None
            
            result = response.json()
            
            if result.get("code") != 0:
                logger.error(f"登录失败: {result.get('message')}")
                return False, None
            
            # 获取Authorization令牌
            auth_token = response.headers.get("Authorization")
            if not auth_token:
                logger.error("响应中没有Authorization头")
                return False, None
            
            logger.info("RagFlow登录成功")
            return True, auth_token
            
        except Exception as e:
            logger.error(f"登录异常: {str(e)}")
            return False, None
    
    def refresh_token(self):
        """
        刷新认证令牌
        
        Returns:
            bool: 是否成功刷新
        """
        try:
            success, auth_token = self.login()
            if success and auth_token:
                # 更新知识库的认证令牌
                self.knowledge_base.update_auth_token(auth_token)
                logger.info(f"知识库 {self.knowledge_base.name} 的认证令牌已刷新")
                return True
            else:
                logger.error(f"知识库 {self.knowledge_base.name} 的认证令牌刷新失败")
                return False
                
        except Exception as e:
            logger.error(f"刷新令牌异常: {str(e)}")
            return False
    
    def get_valid_token(self):
        """
        获取有效的认证令牌，如果需要则自动刷新

        Returns:
            str or None: 有效的认证令牌
        """
        # 检查是否需要刷新令牌
        if self.knowledge_base.needs_token_refresh():
            logger.info(f"知识库 {self.knowledge_base.name} 需要刷新令牌 (间隔: {self.knowledge_base.token_refresh_interval}小时)")
            if not self.refresh_token():
                logger.error(f"知识库 {self.knowledge_base.name} 令牌刷新失败")
                return None

        return self.knowledge_base.auth_token
    
    def test_connection(self):
        """
        测试连接是否正常
        
        Returns:
            tuple: (success, message)
        """
        try:
            auth_token = self.get_valid_token()
            if not auth_token:
                return False, "无法获取有效的认证令牌"
            
            # 测试API调用
            test_url = f"{self.base_url}/api/v1/datasets"
            headers = {
                "Authorization": auth_token,
                "Content-Type": "application/json"
            }
            
            response = self.session.get(test_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    return True, "连接测试成功"
                else:
                    return False, f"API返回错误: {result.get('message')}"
            else:
                return False, f"连接测试失败，状态码: {response.status_code}"
                
        except Exception as e:
            logger.error(f"连接测试异常: {str(e)}")
            return False, f"连接测试异常: {str(e)}"





def get_ragflow_auth_header(knowledge_base):
    """
    获取RagFlow知识库的认证头

    Args:
        knowledge_base: KnowledgeBase实例或包含platform_kb_id的对象

    Returns:
        str or None: 认证头
    """
    # 检查是否为RagFlow平台
    if hasattr(knowledge_base, 'platform') and knowledge_base.platform != 'ragflow':
        return None

    # 检查是否使用全局认证配置
    if hasattr(knowledge_base, 'uses_global_auth') and knowledge_base.uses_global_auth():
        # 使用全局认证设置
        from .ragflow_auth_views import create_ragflow_kb_with_global_auth

        kb_id = getattr(knowledge_base, 'platform_kb_id', None)
        if kb_id:
            global_auth_kb = create_ragflow_kb_with_global_auth(kb_id)
            if global_auth_kb:
                auth_service = RagFlowAuthService(global_auth_kb)
                return auth_service.get_valid_token()

    # 使用知识库自身的认证信息
    elif hasattr(knowledge_base, 'uses_username_auth') and knowledge_base.uses_username_auth():
        auth_service = RagFlowAuthService(knowledge_base)
        return auth_service.get_valid_token()

    # 最后尝试使用API密钥（向后兼容）
    elif hasattr(knowledge_base, 'api_key') and knowledge_base.api_key:
        return f"Bearer {knowledge_base.api_key}"

    # 如果没有明确配置，尝试使用全局认证设置作为后备
    else:
        from .ragflow_auth_views import create_ragflow_kb_with_global_auth

        kb_id = getattr(knowledge_base, 'platform_kb_id', None)
        if kb_id:
            global_auth_kb = create_ragflow_kb_with_global_auth(kb_id)
            if global_auth_kb:
                auth_service = RagFlowAuthService(global_auth_kb)
                return auth_service.get_valid_token()

    return None
