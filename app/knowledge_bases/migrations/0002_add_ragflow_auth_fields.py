# Generated by Django 5.2.3 on 2025-06-27 12:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("knowledge_bases", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="knowledgebase",
            name="auth_token",
            field=models.TextField(blank=True, verbose_name="认证令牌"),
        ),
        migrations.AddField(
            model_name="knowledgebase",
            name="last_token_refresh",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="最后令牌刷新时间"
            ),
        ),
        migrations.AddField(
            model_name="knowledgebase",
            name="password",
            field=models.CharField(blank=True, max_length=200, verbose_name="密码"),
        ),
        migrations.AddField(
            model_name="knowledgebase",
            name="token_refresh_interval",
            field=models.PositiveIntegerField(
                default=12, verbose_name="令牌刷新间隔(小时)"
            ),
        ),
        migrations.AddField(
            model_name="knowledgebase",
            name="username",
            field=models.CharField(blank=True, max_length=100, verbose_name="用户名"),
        ),
        migrations.AlterField(
            model_name="knowledgebase",
            name="api_key",
            field=models.CharField(blank=True, max_length=200, verbose_name="API密钥"),
        ),
    ]
