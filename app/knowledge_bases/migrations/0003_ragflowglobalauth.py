# Generated by Django 5.2.3 on 2025-06-28 07:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("knowledge_bases", "0002_add_ragflow_auth_fields"),
    ]

    operations = [
        migrations.CreateModel(
            name="RagFlowGlobalAuth",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("url", models.URLField(verbose_name="RagFlow服务地址")),
                ("username", models.CharField(max_length=100, verbose_name="用户名")),
                ("password", models.CharField(max_length=200, verbose_name="密码")),
                (
                    "refresh_interval",
                    models.PositiveIntegerField(
                        default=12, verbose_name="令牌刷新间隔(小时)"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "RagFlow全局认证设置",
                "verbose_name_plural": "RagFlow全局认证设置",
            },
        ),
    ]
