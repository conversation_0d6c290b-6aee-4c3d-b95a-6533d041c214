# Generated by Django 5.2.3 on 2025-06-22 10:29

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='KnowledgeTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='标签名称')),
                ('description', models.TextField(blank=True, max_length=200, verbose_name='描述')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='颜色')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '知识标签',
                'verbose_name_plural': '知识标签',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='KnowledgeBase',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='知识库名称')),
                ('description', models.TextField(blank=True, max_length=500, verbose_name='描述')),
                ('platform', models.CharField(choices=[('dify', 'Dify'), ('ragflow', 'RagFlow')], max_length=20, verbose_name='平台')),
                ('platform_kb_id', models.CharField(max_length=200, verbose_name='平台知识库ID')),
                ('api_endpoint', models.URLField(verbose_name='API端点')),
                ('api_key', models.CharField(max_length=200, verbose_name='API密钥')),
                ('config_data', models.JSONField(blank=True, default=dict, verbose_name='配置数据')),
                ('status', models.CharField(choices=[('active', '激活'), ('inactive', '未激活'), ('error', '错误')], default='active', max_length=20, verbose_name='状态')),
                ('is_public', models.BooleanField(default=False, verbose_name='公开')),
                ('document_count', models.PositiveIntegerField(default=0, verbose_name='文档数量')),
                ('usage_count', models.PositiveIntegerField(default=0, verbose_name='使用次数')),
                ('last_sync_at', models.DateTimeField(blank=True, null=True, verbose_name='最后同步时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='knowledge_bases', to=settings.AUTH_USER_MODEL, verbose_name='所有者')),
            ],
            options={
                'verbose_name': '知识库',
                'verbose_name_plural': '知识库',
                'ordering': ['-created_at'],
                'unique_together': {('platform', 'platform_kb_id', 'owner')},
            },
        ),
        migrations.CreateModel(
            name='Knowledge',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('content', models.TextField(verbose_name='内容')),
                ('summary', models.TextField(blank=True, max_length=500, verbose_name='摘要')),
                ('type', models.CharField(choices=[('document', '文档'), ('qa', '问答对'), ('text', '文本片段')], default='document', max_length=20, verbose_name='类型')),
                ('platform_item_id', models.CharField(blank=True, max_length=200, verbose_name='平台条目ID')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='查看次数')),
                ('usage_count', models.PositiveIntegerField(default=0, verbose_name='使用次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('knowledge_base', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='knowledge_items', to='knowledge_bases.knowledgebase', verbose_name='知识库')),
                ('tags', models.ManyToManyField(blank=True, to='knowledge_bases.knowledgetag', verbose_name='标签')),
            ],
            options={
                'verbose_name': '知识条目',
                'verbose_name_plural': '知识条目',
                'ordering': ['-created_at'],
            },
        ),
    ]
