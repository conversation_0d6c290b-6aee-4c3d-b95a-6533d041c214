import requests
from django.db import models
from django.shortcuts import render
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend

from .models import KnowledgeBase, Knowledge, KnowledgeTag
from .serializers import (
    KnowledgeBaseSerializer,
    KnowledgeBaseCreateSerializer,
    KnowledgeBaseListSerializer,
    KnowledgeSerializer,
    KnowledgeListSerializer,
    KnowledgeTagSerializer,
    KnowledgeSearchSerializer,
    KnowledgeBaseStatsSerializer
)


class KnowledgeBaseViewSet(viewsets.ModelViewSet):
    """知识库视图集"""

    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['platform', 'status', 'is_public']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'updated_at', 'usage_count', 'name']
    ordering = ['-created_at']

    def get_queryset(self):
        """获取查询集"""
        user = self.request.user
        if user.is_superuser:
            return KnowledgeBase.objects.all()
        else:
            # 用户只能看到自己的知识库和公开的知识库
            return KnowledgeBase.objects.filter(
                models.Q(owner=user) | models.Q(is_public=True)
            ).distinct()

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return KnowledgeBaseCreateSerializer
        elif self.action == 'list':
            return KnowledgeBaseListSerializer
        return KnowledgeBaseSerializer

    def perform_create(self, serializer):
        """创建知识库时设置所有者"""
        serializer.save(owner=self.request.user)

    def perform_update(self, serializer):
        """更新知识库"""
        # 只有所有者或管理员可以更新
        kb = self.get_object()
        if kb.owner != self.request.user and not self.request.user.is_superuser:
            raise permissions.PermissionDenied("您没有权限修改此知识库")
        serializer.save()

    def perform_destroy(self, instance):
        """删除知识库"""
        # 只有所有者或管理员可以删除
        if instance.owner != self.request.user and not self.request.user.is_superuser:
            raise permissions.PermissionDenied("您没有权限删除此知识库")
        instance.delete()

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """测试知识库连接"""
        kb = self.get_object()

        try:
            import time
            start_time = time.time()
            success = self._test_knowledge_base_connection(kb)
            end_time = time.time()
            response_time = end_time - start_time

            if success:
                return Response({
                    'success': True,
                    'message': '连接测试成功',
                    'response_time': response_time
                })
            else:
                return Response({
                    'success': False,
                    'message': '连接测试失败',
                    'response_time': response_time
                }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'连接测试异常: {str(e)}',
                'response_time': 0
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _test_knowledge_base_connection(self, kb):
        """测试知识库连接的具体实现"""
        try:
            # 根据平台进行简单的连接测试
            if kb.platform == 'dify':
                # 测试Dify知识库连接
                url = f"{kb.api_endpoint.rstrip('/')}/v1/datasets"
                headers = {
                    'Authorization': f'Bearer {kb.api_key}',
                    'Content-Type': 'application/json'
                }
                response = requests.get(url, headers=headers, timeout=10)
                return response.status_code == 200

            elif kb.platform == 'ragflow':
                # 测试RagFlow知识库连接
                url = f"{kb.api_endpoint.rstrip('/')}/v1/datasets"
                headers = {
                    'Authorization': f'Bearer {kb.api_key}',
                    'Content-Type': 'application/json'
                }
                response = requests.get(url, headers=headers, timeout=10)
                return response.status_code == 200

            else:
                # 其他平台的测试逻辑
                return True

        except Exception as e:
            print(f"测试知识库连接失败: {str(e)}")
            return False

    @action(detail=True, methods=['post'])
    def sync(self, request, pk=None):
        """同步知识库"""
        kb = self.get_object()

        try:
            # 这里可以添加具体的同步逻辑
            # 根据不同平台调用相应的API来同步知识库内容

            from django.utils import timezone
            kb.last_sync_at = timezone.now()
            kb.save(update_fields=['last_sync_at'])

            return Response({
                'success': True,
                'message': '知识库同步成功',
                'last_sync_at': kb.last_sync_at
            })
        except Exception as e:
            return Response({
                'success': False,
                'message': f'同步失败: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索知识库"""
        serializer = KnowledgeSearchSerializer(data=request.query_params)
        if serializer.is_valid():
            query = serializer.validated_data['query']
            kb_id = serializer.validated_data.get('knowledge_base_id')
            limit = serializer.validated_data.get('limit', 10)

            # 构建搜索查询
            queryset = self.get_queryset()
            if kb_id:
                queryset = queryset.filter(id=kb_id)

            # 搜索知识库名称和描述
            queryset = queryset.filter(
                models.Q(name__icontains=query) |
                models.Q(description__icontains=query)
            )[:limit]

            serializer = KnowledgeBaseListSerializer(queryset, many=True)
            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class KnowledgeViewSet(viewsets.ModelViewSet):
    """知识条目视图集"""

    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type', 'knowledge_base', 'knowledge_base__platform']
    search_fields = ['title', 'content', 'summary']
    ordering_fields = ['created_at', 'updated_at', 'usage_count', 'view_count']
    ordering = ['-created_at']

    def get_queryset(self):
        """获取查询集"""
        user = self.request.user
        if user.is_superuser:
            return Knowledge.objects.all()
        else:
            # 用户只能看到自己知识库中的知识条目和公开知识库的条目
            return Knowledge.objects.filter(
                models.Q(knowledge_base__owner=user) |
                models.Q(knowledge_base__is_public=True)
            ).distinct()

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return KnowledgeListSerializer
        return KnowledgeSerializer

    def perform_create(self, serializer):
        """创建知识条目时检查权限"""
        kb = serializer.validated_data['knowledge_base']
        if kb.owner != self.request.user and not self.request.user.is_superuser:
            raise permissions.PermissionDenied("您没有权限在此知识库中添加内容")
        serializer.save()

    def perform_update(self, serializer):
        """更新知识条目"""
        knowledge = self.get_object()
        if knowledge.knowledge_base.owner != self.request.user and not self.request.user.is_superuser:
            raise permissions.PermissionDenied("您没有权限修改此知识条目")
        serializer.save()

    def perform_destroy(self, instance):
        """删除知识条目"""
        if instance.knowledge_base.owner != self.request.user and not self.request.user.is_superuser:
            raise permissions.PermissionDenied("您没有权限删除此知识条目")
        instance.delete()

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索知识条目"""
        serializer = KnowledgeSearchSerializer(data=request.query_params)
        if serializer.is_valid():
            query = serializer.validated_data['query']
            kb_id = serializer.validated_data.get('knowledge_base_id')
            type_filter = serializer.validated_data.get('type')
            limit = serializer.validated_data.get('limit', 10)

            # 构建搜索查询
            queryset = self.get_queryset()
            if kb_id:
                queryset = queryset.filter(knowledge_base_id=kb_id)
            if type_filter:
                queryset = queryset.filter(type=type_filter)

            # 搜索标题、内容和摘要
            queryset = queryset.filter(
                models.Q(title__icontains=query) |
                models.Q(content__icontains=query) |
                models.Q(summary__icontains=query)
            )[:limit]

            serializer = KnowledgeListSerializer(queryset, many=True)
            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class KnowledgeTagViewSet(viewsets.ModelViewSet):
    """知识标签视图集"""

    queryset = KnowledgeTag.objects.all()
    serializer_class = KnowledgeTagSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def knowledge_base_stats_view(request):
    """知识库统计视图"""

    user = request.user

    # 用户的知识库统计
    user_kbs = KnowledgeBase.objects.filter(owner=user)

    stats = {
        'total_knowledge_bases': user_kbs.count(),
        'active_knowledge_bases': user_kbs.filter(status='active').count(),
        'public_knowledge_bases': user_kbs.filter(is_public=True).count(),
        'total_documents': Knowledge.objects.filter(knowledge_base__owner=user).count(),
        'total_usage': sum(kb.usage_count for kb in user_kbs),
        'platform_distribution': {},
        'recent_activity': []
    }

    # 平台分布统计
    for platform_code, platform_name in KnowledgeBase.PLATFORM_CHOICES:
        count = user_kbs.filter(platform=platform_code).count()
        if count > 0:
            stats['platform_distribution'][platform_name] = count

    # 最近活动（最近更新的知识库）
    recent_kbs = user_kbs.order_by('-updated_at')[:5]
    stats['recent_activity'] = KnowledgeBaseListSerializer(recent_kbs, many=True).data

    return Response(stats)
