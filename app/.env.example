# Django设置
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库设置
DATABASE_URL=sqlite:///db.sqlite3
# 生产环境使用PostgreSQL
# DATABASE_URL=postgresql://user:password@localhost:5432/agent_portal

# Redis设置
REDIS_URL=redis://localhost:6379/0

# AI平台API密钥
OPENAI_API_KEY=your-openai-api-key
DIFY_API_KEY=your-dify-api-key
RAGFLOW_API_KEY=your-ragflow-api-key

# 邮件设置（可选）
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=True
