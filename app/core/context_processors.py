from .models import SystemSettings


def system_settings(request):
    """
    系统设置上下文处理器
    在所有模板中提供系统设置数据
    """
    try:
        settings = SystemSettings.get_settings()
        return {
            'system_settings': {
                'site_name': settings.site_name,
                'site_title': settings.site_title,
                'site_description': settings.site_description,
                'maintenance_mode': settings.maintenance_mode,
                'maintenance_message': settings.maintenance_message,
                'allow_registration': settings.allow_registration,
                'enable_search': settings.enable_search,
            }
        }
    except Exception as e:
        # 如果获取设置失败，返回默认值
        return {
            'system_settings': {
                'site_name': 'Agent Portal',
                'site_title': 'AI智能体门户',
                'site_description': '连接多个AI平台，一站式智能对话体验。支持RagFlow、Dify、OpenAI等主流平台，让AI助手触手可及。',
                'maintenance_mode': False,
                'maintenance_message': '系统正在维护中，请稍后再试。',
                'allow_registration': True,
                'enable_search': True,
            }
        }
