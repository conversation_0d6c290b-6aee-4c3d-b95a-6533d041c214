# Generated by Django 5.2.3 on 2025-07-19 10:05

import django.core.validators
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_systemsettings_site_description_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="<PERSON><PERSON>rovider",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="显示名称")),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("qwen", "阿里云千问"),
                            ("deepseek", "DeepSeek"),
                            ("openai", "OpenAI"),
                            ("claude", "<PERSON>"),
                            ("custom", "自定义"),
                        ],
                        max_length=20,
                        verbose_name="提供商",
                    ),
                ),
                (
                    "model_name",
                    models.CharField(max_length=100, verbose_name="模型名称"),
                ),
                ("api_url", models.URLField(verbose_name="API端点地址")),
                ("api_key", models.CharField(max_length=500, verbose_name="API密钥")),
                (
                    "temperature",
                    models.FloatField(
                        default=0.7,
                        help_text="控制回答的随机性，0.0-2.0之间",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(2.0),
                        ],
                        verbose_name="温度参数",
                    ),
                ),
                (
                    "max_tokens",
                    models.PositiveIntegerField(
                        default=2000,
                        help_text="单次回答的最大令牌数量",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(32000),
                        ],
                        verbose_name="最大令牌数",
                    ),
                ),
                (
                    "top_p",
                    models.FloatField(
                        default=0.9,
                        help_text="核采样参数，0.0-1.0之间",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(1.0),
                        ],
                        verbose_name="Top-p参数",
                    ),
                ),
                (
                    "extra_config",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="其他模型特定的配置参数",
                        verbose_name="额外配置",
                    ),
                ),
                (
                    "is_enabled",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="是否为默认模型"),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(default=0, verbose_name="使用次数"),
                ),
                (
                    "total_tokens",
                    models.PositiveBigIntegerField(default=0, verbose_name="总令牌数"),
                ),
                (
                    "total_cost",
                    models.DecimalField(
                        decimal_places=4,
                        default=0.0,
                        max_digits=10,
                        verbose_name="总费用",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "last_used_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="最后使用时间"
                    ),
                ),
            ],
            options={
                "verbose_name": "大模型提供商",
                "verbose_name_plural": "大模型提供商",
                "ordering": ["-is_default", "-is_enabled", "name"],
            },
        ),
    ]
