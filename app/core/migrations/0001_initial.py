# Generated by Django 5.2.3 on 2025-06-22 05:13

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SystemSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "max_conversations_per_user",
                    models.PositiveIntegerField(
                        default=5,
                        help_text="每个用户最多可以保存的会话数量，超出时会自动删除最旧的会话",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="每用户最大会话数",
                    ),
                ),
                (
                    "max_messages_per_conversation",
                    models.PositiveIntegerField(
                        default=100,
                        help_text="每个会话最多可以保存的消息数量",
                        validators=[
                            django.core.validators.MinValueValidator(10),
                            django.core.validators.MaxValueValidator(1000),
                        ],
                        verbose_name="每会话最大消息数",
                    ),
                ),
                (
                    "max_file_size_mb",
                    models.PositiveIntegerField(
                        default=10,
                        help_text="用户上传文件的最大大小限制",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="最大文件大小(MB)",
                    ),
                ),
                (
                    "api_rate_limit_per_minute",
                    models.PositiveIntegerField(
                        default=60,
                        help_text="每个用户每分钟最多可以调用的API次数",
                        validators=[
                            django.core.validators.MinValueValidator(10),
                            django.core.validators.MaxValueValidator(1000),
                        ],
                        verbose_name="API每分钟限制",
                    ),
                ),
                (
                    "maintenance_mode",
                    models.BooleanField(
                        default=False,
                        help_text="开启后，普通用户将无法访问系统",
                        verbose_name="维护模式",
                    ),
                ),
                (
                    "maintenance_message",
                    models.TextField(
                        blank=True,
                        help_text="维护模式下显示给用户的提示信息",
                        max_length=500,
                        verbose_name="维护提示信息",
                    ),
                ),
                (
                    "allow_registration",
                    models.BooleanField(
                        default=True,
                        help_text="是否允许新用户注册账号",
                        verbose_name="允许用户注册",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "系统设置",
                "verbose_name_plural": "系统设置",
            },
        ),
    ]
