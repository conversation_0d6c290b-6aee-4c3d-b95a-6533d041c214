# Generated by Django 5.2.3 on 2025-06-22 17:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='systemsettings',
            name='site_description',
            field=models.TextField(default='连接多个AI平台，一站式智能对话体验。支持RagFlow、Dify、OpenAI等主流平台，让AI助手触手可及。', help_text='首页Hero区域显示的描述文字', max_length=500, verbose_name='首页描述'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='site_name',
            field=models.CharField(default='Agent Portal', help_text='显示在导航栏和页面标题中的平台名称', max_length=100, verbose_name='平台名称'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='site_title',
            field=models.CharField(default='AI智能体门户', help_text='首页Hero区域显示的主标题', max_length=200, verbose_name='首页主标题'),
        ),
    ]
