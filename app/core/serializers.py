from rest_framework import serializers
from .models import SystemSettings, LLMProvider


class SystemSettingsSerializer(serializers.ModelSerializer):
    """系统设置序列化器"""

    class Meta:
        model = SystemSettings
        fields = [
            'max_conversations_per_user',
            'max_messages_per_conversation',
            'max_file_size_mb',
            'api_rate_limit_per_minute',
            'maintenance_mode',
            'maintenance_message',
            'allow_registration',
            'enable_search',
            'site_name',
            'site_title',
            'site_description',
            'updated_at'
        ]
        read_only_fields = ['updated_at']


class SystemSettingsUpdateSerializer(serializers.ModelSerializer):
    """系统设置更新序列化器"""

    class Meta:
        model = SystemSettings
        fields = [
            'max_conversations_per_user',
            'max_messages_per_conversation',
            'max_file_size_mb',
            'api_rate_limit_per_minute',
            'maintenance_mode',
            'maintenance_message',
            'allow_registration',
            'enable_search',
            'site_name',
            'site_title',
            'site_description'
        ]
        
    def validate_max_conversations_per_user(self, value):
        """验证最大会话数"""
        if value < 1 or value > 100:
            raise serializers.ValidationError("每用户最大会话数必须在1-100之间")
        return value
        
    def validate_max_messages_per_conversation(self, value):
        """验证每会话最大消息数"""
        if value < 10 or value > 1000:
            raise serializers.ValidationError("每会话最大消息数必须在10-1000之间")
        return value

    def validate_site_name(self, value):
        """验证平台名称"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("平台名称不能为空")
        if len(value) > 100:
            raise serializers.ValidationError("平台名称不能超过100个字符")
        return value.strip()

    def validate_site_title(self, value):
        """验证首页主标题"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("首页主标题不能为空")
        if len(value) > 200:
            raise serializers.ValidationError("首页主标题不能超过200个字符")
        return value.strip()

    def validate_site_description(self, value):
        """验证首页描述"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("首页描述不能为空")
        if len(value) > 500:
            raise serializers.ValidationError("首页描述不能超过500个字符")
        return value.strip()
        
    def validate_max_file_size_mb(self, value):
        """验证最大文件大小"""
        if value < 1 or value > 100:
            raise serializers.ValidationError("最大文件大小必须在1-100MB之间")
        return value
        
    def validate_api_rate_limit_per_minute(self, value):
        """验证API限制"""
        if value < 10 or value > 1000:
            raise serializers.ValidationError("API每分钟限制必须在10-1000之间")
        return value


class LLMProviderSerializer(serializers.ModelSerializer):
    """大模型提供商序列化器"""

    class Meta:
        model = LLMProvider
        fields = [
            'id',
            'name',
            'provider',
            'model_name',
            'api_url',
            'api_key',
            'temperature',
            'max_tokens',
            'top_p',
            'system_prompt',
            'extra_config',
            'is_enabled',
            'is_default',
            'is_public',
            'usage_count',
            'total_tokens',
            'total_cost',
            'created_at',
            'updated_at',
            'last_used_at'
        ]
        read_only_fields = ['id', 'usage_count', 'total_tokens', 'total_cost', 'created_at', 'updated_at', 'last_used_at']


class LLMProviderCreateSerializer(serializers.ModelSerializer):
    """大模型提供商创建序列化器"""

    class Meta:
        model = LLMProvider
        fields = [
            'name',
            'provider',
            'model_name',
            'api_url',
            'api_key',
            'temperature',
            'max_tokens',
            'top_p',
            'system_prompt',
            'extra_config',
            'is_enabled',
            'is_default',
            'is_public'
        ]

    def validate_name(self, value):
        """验证显示名称"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("显示名称不能为空")
        if len(value) > 100:
            raise serializers.ValidationError("显示名称不能超过100个字符")
        return value.strip()

    def validate_model_name(self, value):
        """验证模型名称"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("模型名称不能为空")
        return value.strip()

    def validate_api_url(self, value):
        """验证API地址"""
        if not value:
            raise serializers.ValidationError("API端点地址不能为空")
        return value

    def validate_api_key(self, value):
        """验证API密钥"""
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError("API密钥不能为空")
        return value.strip()

    def validate_temperature(self, value):
        """验证温度参数"""
        if value < 0.0 or value > 2.0:
            raise serializers.ValidationError("温度参数必须在0.0-2.0之间")
        return value

    def validate_max_tokens(self, value):
        """验证最大令牌数"""
        if value < 1 or value > 32000:
            raise serializers.ValidationError("最大令牌数必须在1-32000之间")
        return value

    def validate_top_p(self, value):
        """验证Top-p参数"""
        if value < 0.0 or value > 1.0:
            raise serializers.ValidationError("Top-p参数必须在0.0-1.0之间")
        return value


class LLMProviderListSerializer(serializers.ModelSerializer):
    """大模型提供商列表序列化器（用于前端选择）"""

    class Meta:
        model = LLMProvider
        fields = [
            'id',
            'name',
            'provider',
            'model_name',
            'is_enabled',
            'is_default',
            'is_public'
        ]
