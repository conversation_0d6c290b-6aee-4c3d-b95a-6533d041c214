"""
大模型服务集成
支持阿里云千问、DeepSeek等大模型的API调用和SSE流式响应
"""

import json
import requests
import time
from typing import Dict, List, Generator, Any, Optional
from django.utils import timezone
from .models import LLMProvider


class LLMService:
    """大模型服务基类"""
    
    def __init__(self, provider: LLMProvider):
        self.provider = provider
        self.api_url = provider.api_url
        self.api_key = provider.api_key
        self.model_name = provider.model_name
        self.temperature = provider.temperature
        self.max_tokens = provider.max_tokens
        self.top_p = provider.top_p
        self.extra_config = provider.extra_config or {}
    
    def send_message_stream(self, message: str, conversation_history: List[Dict], conversation=None) -> Generator[Dict, None, None]:
        """发送消息并返回流式响应"""
        raise NotImplementedError("子类必须实现此方法")
    
    def _format_messages(self, message: str, conversation_history: List[Dict]) -> List[Dict]:
        """格式化消息历史"""
        messages = []

        # 添加系统提示词（如果存在）
        if self.provider.system_prompt and self.provider.system_prompt.strip():
            messages.append({
                "role": "system",
                "content": self.provider.system_prompt.strip()
            })

        # 添加历史消息
        for msg in conversation_history:
            messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })

        # 添加当前消息
        messages.append({
            "role": "user",
            "content": message
        })

        return messages
    
    def _update_usage_stats(self, tokens_used: int = 0, cost: float = 0.0):
        """更新使用统计"""
        self.provider.increment_usage(tokens_used, cost)


class QwenLLMService(LLMService):
    """阿里云千问大模型服务"""

    def _get_api_endpoint(self):
        """获取千问API的完整端点URL"""
        base_url = self.api_url.rstrip('/')
        if not base_url.endswith('/chat/completions'):
            base_url += '/chat/completions'
        return base_url

    def send_message_stream(self, message: str, conversation_history: List[Dict], conversation=None) -> Generator[Dict, None, None]:
        """发送消息并返回流式响应"""
        try:
            # 发送状态消息
            yield {"type": "status", "message": "千问正在思考中..."}
            
            # 格式化消息
            messages = self._format_messages(message, conversation_history)
            
            # 构建请求数据
            request_data = {
                "model": self.model_name,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "top_p": self.top_p,
                "stream": True,
                **self.extra_config
            }
            
            # 发送请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "Accept": "text/event-stream"
            }
            
            # 使用正确的API端点
            api_endpoint = self._get_api_endpoint()

            response = requests.post(
                api_endpoint,
                headers=headers,
                json=request_data,
                stream=True,
                timeout=60
            )
            
            if response.status_code != 200:
                error_msg = f"千问API调用失败: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', '')}"
                except:
                    pass
                yield {"type": "error", "message": error_msg}
                return
            
            # 处理流式响应
            full_content = ""
            total_tokens = 0
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        
                        if data_str.strip() == '[DONE]':
                            break
                        
                        try:
                            data = json.loads(data_str)

                            if 'choices' in data and len(data['choices']) > 0:
                                choice = data['choices'][0]

                                # 确保choice不为None
                                if choice is None:
                                    continue

                                # 安全地检查delta和content
                                if 'delta' in choice and choice['delta'] is not None:
                                    delta = choice['delta']
                                    if 'content' in delta and delta['content']:
                                        content = delta['content']
                                        full_content += content
                                        yield {
                                            "type": "chunk",
                                            "content": content
                                        }

                                # 检查是否完成
                                if choice and choice.get('finish_reason'):
                                    break

                            # 获取token使用信息
                            if 'usage' in data and data['usage'] is not None:
                                total_tokens = data['usage'].get('total_tokens', 0)
                        
                        except json.JSONDecodeError as e:
                            print(f"千问API JSON解析错误: {e}, 数据: {data_str[:100]}")
                            continue
                        except Exception as e:
                            print(f"千问API处理错误: {e}, 数据: {data_str[:100]}")
                            continue
            
            # 发送完成信号
            yield {
                "type": "complete",
                "content": full_content,
                "tokens_used": total_tokens
            }
            
            # 更新使用统计
            self._update_usage_stats(total_tokens, total_tokens * 0.0001)  # 假设费用
            
        except Exception as e:
            yield {"type": "error", "message": f"千问服务错误: {str(e)}"}


class DeepSeekLLMService(LLMService):
    """DeepSeek大模型服务"""

    def _get_api_endpoint(self):
        """获取DeepSeek API的完整端点URL"""
        base_url = self.api_url.rstrip('/')
        if not base_url.endswith('/chat/completions'):
            base_url += '/chat/completions'
        return base_url

    def send_message_stream(self, message: str, conversation_history: List[Dict], conversation=None) -> Generator[Dict, None, None]:
        """发送消息并返回流式响应"""
        try:
            # 发送状态消息
            yield {"type": "status", "message": "DeepSeek正在思考中..."}
            
            # 格式化消息
            messages = self._format_messages(message, conversation_history)
            
            # 构建请求数据
            request_data = {
                "model": self.model_name,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "top_p": self.top_p,
                "stream": True,
                **self.extra_config
            }
            
            # 发送请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "Accept": "text/event-stream"
            }
            
            # 使用正确的API端点
            api_endpoint = self._get_api_endpoint()

            response = requests.post(
                api_endpoint,
                headers=headers,
                json=request_data,
                stream=True,
                timeout=60
            )
            
            if response.status_code != 200:
                error_msg = f"DeepSeek API调用失败: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', '')}"
                except:
                    pass
                yield {"type": "error", "message": error_msg}
                return
            
            # 处理流式响应
            full_content = ""
            total_tokens = 0
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        
                        if data_str.strip() == '[DONE]':
                            break
                        
                        try:
                            data = json.loads(data_str)
                            
                            if 'choices' in data and len(data['choices']) > 0:
                                choice = data['choices'][0]

                                # 安全地检查delta和content
                                if 'delta' in choice and choice['delta'] is not None:
                                    delta = choice['delta']
                                    if 'content' in delta and delta['content']:
                                        content = delta['content']
                                        full_content += content
                                        yield {
                                            "type": "chunk",
                                            "content": content
                                        }

                                # 检查是否完成
                                if choice.get('finish_reason'):
                                    break
                            
                            # 获取token使用信息
                            if 'usage' in data and data['usage'] is not None:
                                total_tokens = data['usage'].get('total_tokens', 0)
                        
                        except json.JSONDecodeError:
                            continue
            
            # 发送完成信号
            yield {
                "type": "complete",
                "content": full_content,
                "tokens_used": total_tokens
            }
            
            # 更新使用统计
            self._update_usage_stats(total_tokens, total_tokens * 0.00014)  # DeepSeek费用
            
        except Exception as e:
            yield {"type": "error", "message": f"DeepSeek服务错误: {str(e)}"}


class OpenAILLMService(LLMService):
    """OpenAI大模型服务"""

    def _get_api_endpoint(self):
        """获取OpenAI API的完整端点URL"""
        base_url = self.api_url.rstrip('/')
        if not base_url.endswith('/chat/completions'):
            base_url += '/chat/completions'
        return base_url

    def send_message_stream(self, message: str, conversation_history: List[Dict], conversation=None) -> Generator[Dict, None, None]:
        """发送消息并返回流式响应"""
        try:
            # 发送状态消息
            yield {"type": "status", "message": "OpenAI正在思考中..."}
            
            # 格式化消息
            messages = self._format_messages(message, conversation_history)
            
            # 构建请求数据
            request_data = {
                "model": self.model_name,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "top_p": self.top_p,
                "stream": True,
                **self.extra_config
            }
            
            # 发送请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "Accept": "text/event-stream"
            }
            
            # 使用正确的API端点
            api_endpoint = self._get_api_endpoint()

            response = requests.post(
                api_endpoint,
                headers=headers,
                json=request_data,
                stream=True,
                timeout=60
            )
            
            if response.status_code != 200:
                error_msg = f"OpenAI API调用失败: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', '')}"
                except:
                    pass
                yield {"type": "error", "message": error_msg}
                return
            
            # 处理流式响应
            full_content = ""
            total_tokens = 0
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        
                        if data_str.strip() == '[DONE]':
                            break
                        
                        try:
                            data = json.loads(data_str)
                            
                            if 'choices' in data and len(data['choices']) > 0:
                                choice = data['choices'][0]

                                # 安全地检查delta和content
                                if 'delta' in choice and choice['delta'] is not None:
                                    delta = choice['delta']
                                    if 'content' in delta and delta['content']:
                                        content = delta['content']
                                        full_content += content
                                        yield {
                                            "type": "chunk",
                                            "content": content
                                        }

                                # 检查是否完成
                                if choice.get('finish_reason'):
                                    break
                            
                            # 获取token使用信息
                            if 'usage' in data and data['usage'] is not None:
                                total_tokens = data['usage'].get('total_tokens', 0)
                        
                        except json.JSONDecodeError:
                            continue
            
            # 发送完成信号
            yield {
                "type": "complete",
                "content": full_content,
                "tokens_used": total_tokens
            }
            
            # 更新使用统计
            self._update_usage_stats(total_tokens, total_tokens * 0.002)  # OpenAI费用
            
        except Exception as e:
            yield {"type": "error", "message": f"OpenAI服务错误: {str(e)}"}


def get_llm_service(provider: LLMProvider) -> LLMService:
    """根据提供商类型获取对应的服务实例"""
    service_map = {
        'qwen': QwenLLMService,
        'deepseek': DeepSeekLLMService,
        'custom': OpenAILLMService,  # 自定义使用OpenAI格式作为通用格式
    }

    service_class = service_map.get(provider.provider, OpenAILLMService)
    return service_class(provider)
