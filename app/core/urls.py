from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

# 创建路由器
router = DefaultRouter()
router.register(r'llm-providers', views.LLMProviderViewSet)

urlpatterns = [
    path('', views.home, name='home'),
    path('chat/', views.chat, name='chat'),
    path('search/', views.search, name='search'),
    path('agents/', views.agents, name='agents'),
    path('knowledge-bases/', views.knowledge_bases, name='knowledge-bases'),
    path('admin-settings/', views.admin_settings, name='admin-settings'),
    path('llm-settings/', views.llm_settings, name='llm-settings'),

    path('demo/', views.demo, name='demo'),
    path('api/status/', views.api_status, name='api-status'),
    path('api/system-settings/', views.get_system_settings, name='get-system-settings'),
    path('api/system-settings/update/', views.update_system_settings, name='update-system-settings'),

    # 包含API路由
    path('api/', include(router.urls)),
]
