from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid
import json


class SystemSettings(models.Model):
    """系统设置模型"""
    
    # 会话管理设置
    max_conversations_per_user = models.PositiveIntegerField(
        default=5,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        verbose_name='每用户最大会话数',
        help_text='每个用户最多可以保存的会话数量，超出时会自动删除最旧的会话'
    )
    
    # 消息管理设置
    max_messages_per_conversation = models.PositiveIntegerField(
        default=100,
        validators=[MinValueValidator(10), MaxValueValidator(1000)],
        verbose_name='每会话最大消息数',
        help_text='每个会话最多可以保存的消息数量'
    )
    
    # 文件上传设置
    max_file_size_mb = models.PositiveIntegerField(
        default=10,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        verbose_name='最大文件大小(MB)',
        help_text='用户上传文件的最大大小限制'
    )
    
    # API限制设置
    api_rate_limit_per_minute = models.PositiveIntegerField(
        default=60,
        validators=[MinValueValidator(10), MaxValueValidator(1000)],
        verbose_name='API每分钟限制',
        help_text='每个用户每分钟最多可以调用的API次数'
    )
    
    # 系统维护设置
    maintenance_mode = models.BooleanField(
        default=False,
        verbose_name='维护模式',
        help_text='开启后，普通用户将无法访问系统'
    )
    
    maintenance_message = models.TextField(
        blank=True,
        max_length=500,
        verbose_name='维护提示信息',
        help_text='维护模式下显示给用户的提示信息'
    )
    
    # 注册设置
    allow_registration = models.BooleanField(
        default=True,
        verbose_name='允许用户注册',
        help_text='是否允许新用户注册账号'
    )

    # 搜索功能设置
    enable_search = models.BooleanField(
        default=True,
        verbose_name='启用搜索功能',
        help_text='是否启用首页搜索入口和知识库管理功能'
    )

    # 平台标题设置
    site_name = models.CharField(
        max_length=100,
        default='Agent Portal',
        verbose_name='平台名称',
        help_text='显示在导航栏和页面标题中的平台名称'
    )

    site_title = models.CharField(
        max_length=200,
        default='AI智能体门户',
        verbose_name='首页主标题',
        help_text='首页Hero区域显示的主标题'
    )

    site_description = models.TextField(
        max_length=500,
        default='连接多个AI平台，一站式智能对话体验。支持RagFlow、Dify、OpenAI等主流平台，让AI助手触手可及。',
        verbose_name='首页描述',
        help_text='首页Hero区域显示的描述文字'
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '系统设置'
        verbose_name_plural = '系统设置'
        
    def __str__(self):
        return f'系统设置 (更新于 {self.updated_at.strftime("%Y-%m-%d %H:%M")})'
    
    @classmethod
    def get_settings(cls):
        """获取系统设置，如果不存在则创建默认设置"""
        settings, created = cls.objects.get_or_create(
            id=1,  # 确保只有一个设置记录
            defaults={
                'max_conversations_per_user': 5,
                'max_messages_per_conversation': 100,
                'max_file_size_mb': 10,
                'api_rate_limit_per_minute': 60,
                'maintenance_mode': False,
                'maintenance_message': '系统正在维护中，请稍后再试。',
                'allow_registration': True,
                'enable_search': True,
                'site_name': 'Agent Portal',
                'site_title': 'AI智能体门户',
                'site_description': '连接多个AI平台，一站式智能对话体验。支持RagFlow、Dify、OpenAI等主流平台，让AI助手触手可及。',
            }
        )
        return settings
    
    def save(self, *args, **kwargs):
        # 确保只有一个设置记录
        self.id = 1
        super().save(*args, **kwargs)
        
    def delete(self, *args, **kwargs):
        # 不允许删除设置记录
        pass


class LLMProvider(models.Model):
    """大模型提供商配置"""

    PROVIDER_CHOICES = [
        ('qwen', '阿里云千问'),
        ('deepseek', 'DeepSeek'),
        ('custom', '自定义'),
    ]

    # 主键
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name='ID')

    # 基础信息
    name = models.CharField(max_length=100, verbose_name='显示名称')
    provider = models.CharField(max_length=20, choices=PROVIDER_CHOICES, verbose_name='提供商')
    model_name = models.CharField(max_length=100, verbose_name='模型名称')

    # API配置
    api_url = models.URLField(verbose_name='API端点地址')
    api_key = models.CharField(max_length=500, verbose_name='API密钥')

    # 模型参数
    temperature = models.FloatField(
        default=0.7,
        validators=[MinValueValidator(0.0), MaxValueValidator(2.0)],
        verbose_name='温度参数',
        help_text='控制回答的随机性，0.0-2.0之间'
    )
    max_tokens = models.PositiveIntegerField(
        default=2000,
        validators=[MinValueValidator(1), MaxValueValidator(32000)],
        verbose_name='最大令牌数',
        help_text='单次回答的最大令牌数量'
    )
    top_p = models.FloatField(
        default=0.9,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name='Top-p参数',
        help_text='核采样参数，0.0-1.0之间'
    )

    # 系统提示词
    system_prompt = models.TextField(
        blank=True,
        default='',
        verbose_name='系统提示词',
        help_text='用于指导AI助手行为的系统级提示词'
    )

    # 额外配置
    extra_config = models.JSONField(
        default=dict,
        blank=True,
        verbose_name='额外配置',
        help_text='其他模型特定的配置参数'
    )

    # 状态信息
    is_enabled = models.BooleanField(default=True, verbose_name='是否启用')
    is_default = models.BooleanField(default=False, verbose_name='是否为默认模型')
    is_public = models.BooleanField(default=False, verbose_name='是否公开',
                                   help_text='公开的大模型允许匿名用户使用')

    # 统计信息
    usage_count = models.PositiveIntegerField(default=0, verbose_name='使用次数')
    total_tokens = models.PositiveBigIntegerField(default=0, verbose_name='总令牌数')
    total_cost = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        default=0.0000,
        verbose_name='总费用'
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    last_used_at = models.DateTimeField(null=True, blank=True, verbose_name='最后使用时间')

    class Meta:
        verbose_name = '大模型提供商'
        verbose_name_plural = '大模型提供商'
        ordering = ['-is_default', '-is_enabled', 'name']

    def __str__(self):
        return f'{self.name} ({self.provider})'

    def save(self, *args, **kwargs):
        # 如果设置为默认模型，取消其他模型的默认状态
        if self.is_default:
            LLMProvider.objects.filter(is_default=True).exclude(id=self.id).update(is_default=False)
        super().save(*args, **kwargs)

    def increment_usage(self, tokens_used=0, cost=0.0):
        """增加使用统计"""
        from decimal import Decimal
        from django.utils import timezone

        self.usage_count += 1
        self.total_tokens += tokens_used
        # 确保cost是Decimal类型
        if isinstance(cost, float):
            cost = Decimal(str(cost))
        self.total_cost += cost
        self.last_used_at = timezone.now()
        self.save(update_fields=['usage_count', 'total_tokens', 'total_cost', 'last_used_at'])

    @classmethod
    def get_enabled_providers(cls, user=None):
        """获取启用的提供商列表"""
        queryset = cls.objects.filter(is_enabled=True)

        # 如果用户未登录，只返回公开的提供商
        if user is None or not user.is_authenticated:
            queryset = queryset.filter(is_public=True)

        return queryset.order_by('-is_default', 'name')

    @classmethod
    def get_public_providers(cls):
        """获取公开的提供商列表（供匿名用户使用）"""
        return cls.objects.filter(is_enabled=True, is_public=True).order_by('-is_default', 'name')

    @classmethod
    def get_default_provider(cls):
        """获取默认提供商"""
        return cls.objects.filter(is_enabled=True, is_default=True).first()
