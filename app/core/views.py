from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework import status, permissions, viewsets
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from agents.models import Agent
from conversations.models import Conversation
from .models import SystemSettings, LLMProvider
from .serializers import (
    SystemSettingsSerializer,
    SystemSettingsUpdateSerializer,
    LLMProviderSerializer,
    LLMProviderCreateSerializer,
    LLMProviderListSerializer
)
import json


def home(request):
    """首页视图"""
    return render(request, 'index.html')


def chat(request):
    """聊天页面视图"""
    agent_id = request.GET.get('agent')
    model_id = request.GET.get('model')
    query = request.GET.get('q')

    context = {
        'agent_id': agent_id,
        'model_id': model_id,
        'query': query,
        'chat_mode': None,
        'current_item': None,
        'current_item_name': None,
        'current_item_platform': None,
    }

    # 如果是大模型模式
    if model_id:
        try:
            from .models import LLMProvider
            provider = LLMProvider.objects.get(id=model_id, is_enabled=True)

            # 检查权限：如果用户未登录，只能使用公开的大模型
            if not request.user.is_authenticated and not provider.is_public:
                # 重定向到登录页面或显示错误
                context.update({
                    'error': '此大模型需要登录后才能使用',
                    'current_item_name': '访问受限',
                    'current_item_platform': '需要登录',
                })
            else:
                provider_display_name = {
                    'qwen': '阿里云千问',
                    'deepseek': 'DeepSeek',
                    'custom': '自定义'
                }.get(provider.provider, provider.provider)

                context.update({
                    'chat_mode': 'model',
                    'current_item': provider,
                    'current_item_name': provider.name,
                    'current_item_platform': provider_display_name,
                })
        except LLMProvider.DoesNotExist:
            context.update({
                'error': '指定的大模型不存在或未启用',
                'current_item_name': '大模型不存在',
                'current_item_platform': '错误',
            })

    # 如果是智能体模式
    elif agent_id:
        try:
            from agents.models import Agent
            agent = Agent.objects.get(id=agent_id)

            # 检查权限：如果智能体不公开且用户未登录
            if not agent.is_public and not request.user.is_authenticated:
                context.update({
                    'error': '此智能体需要登录后才能使用',
                    'current_item_name': '访问受限',
                    'current_item_platform': '需要登录',
                })
            else:
                context.update({
                    'chat_mode': 'agent',
                    'current_item': agent,
                    'current_item_name': agent.name,
                    'current_item_platform': agent.platform,
                })
        except Agent.DoesNotExist:
            context.update({
                'error': '指定的智能体不存在',
                'current_item_name': '智能体不存在',
                'current_item_platform': '错误',
            })

    return render(request, 'chat.html', context)


@login_required
def agents(request):
    """智能体管理页面视图"""
    return render(request, 'agents.html')


@login_required
def knowledge_bases(request):
    """知识库管理页面视图"""
    return render(request, 'knowledge_bases.html')


def search(request):
    """搜索页面视图"""
    query = request.GET.get('q', '')
    page = request.GET.get('page', 1)

    context = {
        'query': query,
        'page': page
    }

    return render(request, 'search.html', context)


def demo(request):
    """用户头像下拉菜单演示页面"""
    return render(request, 'demo.html')


def api_status(request):
    """API状态检查"""
    return JsonResponse({
        'status': 'ok',
        'message': 'Agent Portal API is running'
    })


@login_required
def admin_settings(request):
    """管理员设置页面视图"""
    if not request.user.is_superuser:
        return redirect('home')
    return render(request, 'admin_settings.html')


@login_required
def llm_settings(request):
    """大模型设置页面视图"""
    if not request.user.is_superuser:
        return redirect('home')
    return render(request, 'llm_settings.html')








@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_system_settings(request):
    """获取系统设置"""
    if not request.user.is_superuser:
        return Response({'error': '权限不足'}, status=status.HTTP_403_FORBIDDEN)

    settings = SystemSettings.get_settings()
    serializer = SystemSettingsSerializer(settings)
    return Response(serializer.data)


@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def update_system_settings(request):
    """更新系统设置"""
    if not request.user.is_superuser:
        return Response({'error': '权限不足'}, status=status.HTTP_403_FORBIDDEN)

    settings = SystemSettings.get_settings()
    serializer = SystemSettingsUpdateSerializer(settings, data=request.data, partial=True)

    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LLMProviderViewSet(viewsets.ModelViewSet):
    """大模型提供商视图集"""

    queryset = LLMProvider.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return LLMProviderCreateSerializer
        # 对于列表和详情都使用完整的序列化器
        return LLMProviderSerializer

    def get_permissions(self):
        """权限控制：查看需要认证，修改需要管理员权限"""
        if self.action in ['list', 'retrieve']:
            return [permissions.IsAuthenticated()]
        elif self.action in ['enabled', 'chat']:
            # enabled和chat端点允许匿名访问，以便匿名用户可以使用公开的大模型
            return [permissions.AllowAny()]
        else:
            return [permissions.IsAuthenticated()]

    def check_admin_permission(self, request):
        """检查管理员权限"""
        if not request.user.is_superuser:
            return Response({'error': '权限不足，只有管理员可以执行此操作'},
                          status=status.HTTP_403_FORBIDDEN)
        return None

    def create(self, request, *args, **kwargs):
        """创建大模型提供商"""
        print(f"CREATE REQUEST - User: {request.user}, Is superuser: {request.user.is_superuser}")
        print(f"CREATE REQUEST - Data: {request.data}")

        error_response = self.check_admin_permission(request)
        if error_response:
            print(f"Permission denied for user: {request.user}")
            return error_response

        # 使用创建序列化器进行验证和创建
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            print(f"Validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        instance = serializer.save()
        print(f"Created instance: {instance}")

        # 使用完整序列化器返回创建的对象
        response_serializer = LLMProviderSerializer(instance)
        print(f"Response data: {response_serializer.data}")
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def list(self, request, *args, **kwargs):
        """获取大模型提供商列表"""
        print(f"LIST REQUEST - User: {request.user}")
        queryset = self.get_queryset()
        print(f"Queryset count: {queryset.count()}")
        serializer = self.get_serializer(queryset, many=True)
        print(f"Serialized data: {serializer.data}")
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def chat(self, request):
        """直接与大模型聊天"""
        try:
            model_id = request.data.get('model_id')
            message = request.data.get('message')
            history = request.data.get('history', [])  # 获取历史聊天记录

            if not model_id or not message:
                return Response({'error': '缺少必要参数'}, status=status.HTTP_400_BAD_REQUEST)

            # 获取大模型提供商
            try:
                provider = LLMProvider.objects.get(id=model_id, is_enabled=True)
            except LLMProvider.DoesNotExist:
                return Response({'error': '指定的大模型不存在或未启用'}, status=status.HTTP_404_NOT_FOUND)

            # 检查权限：如果用户未登录，只能使用公开的大模型
            if not request.user.is_authenticated and not provider.is_public:
                return Response({'error': '此大模型需要登录后才能使用'}, status=status.HTTP_401_UNAUTHORIZED)

            # 构建流式响应
            def generate_response():
                try:
                    import requests
                    import json

                    # 构建消息列表，包含历史记录和当前消息
                    messages = []

                    # 添加系统提示词（如果存在）
                    if provider.system_prompt and provider.system_prompt.strip():
                        messages.append({
                            "role": "system",
                            "content": provider.system_prompt.strip()
                        })

                    # 添加历史消息（确保格式正确）
                    if history and isinstance(history, list):
                        for msg in history:
                            if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                                # 确保role是有效的值
                                if msg['role'] in ['user', 'assistant', 'system']:
                                    messages.append({
                                        "role": msg['role'],
                                        "content": str(msg['content'])
                                    })

                    # 添加当前用户消息
                    messages.append({
                        "role": "user",
                        "content": message
                    })

                    # 构建请求数据
                    chat_data = {
                        "model": provider.model_name,
                        "messages": messages,
                        "stream": True,
                        "temperature": provider.temperature,
                        "max_tokens": provider.max_tokens,
                        "top_p": provider.top_p
                    }

                    # 添加额外配置
                    if provider.extra_config:
                        chat_data.update(provider.extra_config)

                    # 发送请求到大模型API
                    headers = {
                        'Authorization': f'Bearer {provider.api_key}',
                        'Content-Type': 'application/json'
                    }

                    response = requests.post(
                        f"{provider.api_url}/chat/completions",
                        headers=headers,
                        json=chat_data,
                        stream=True
                    )

                    if response.status_code != 200:
                        yield f"data: {json.dumps({'type': 'error', 'message': f'API请求失败: {response.status_code}'})}\n\n"
                        return

                    # 处理流式响应
                    for line in response.iter_lines():
                        if line:
                            line_str = line.decode('utf-8')
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]
                                if data_str.strip() == '[DONE]':
                                    yield f"data: {json.dumps({'type': 'complete'})}\n\n"
                                    break

                                try:
                                    data = json.loads(data_str)
                                    if 'choices' in data and len(data['choices']) > 0:
                                        delta = data['choices'][0].get('delta', {})
                                        if 'content' in delta:
                                            yield f"data: {json.dumps({'type': 'chunk', 'content': delta['content']})}\n\n"
                                except json.JSONDecodeError:
                                    continue

                except Exception as e:
                    print(f"大模型聊天错误: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

            from django.http import StreamingHttpResponse
            response = StreamingHttpResponse(generate_response(), content_type='text/event-stream')
            response['Cache-Control'] = 'no-cache'
            response['X-Accel-Buffering'] = 'no'  # 禁用nginx缓冲
            # 不要设置Connection头，让WSGI服务器处理
            return response

        except Exception as e:
            print(f"聊天API错误: {e}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def update(self, request, *args, **kwargs):
        """更新大模型提供商"""
        error_response = self.check_admin_permission(request)
        if error_response:
            return error_response

        # 获取实例
        instance = self.get_object()

        # 使用创建序列化器进行验证和更新
        serializer = LLMProviderCreateSerializer(instance, data=request.data, partial=kwargs.get('partial', False))
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()

        # 使用完整序列化器返回更新的对象
        response_serializer = LLMProviderSerializer(instance)
        return Response(response_serializer.data)

    def partial_update(self, request, *args, **kwargs):
        """部分更新大模型提供商"""
        error_response = self.check_admin_permission(request)
        if error_response:
            return error_response
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        """删除大模型提供商"""
        error_response = self.check_admin_permission(request)
        if error_response:
            return error_response
        return super().destroy(request, *args, **kwargs)

    @action(detail=False, methods=['get'])
    def enabled(self, request):
        """获取启用的大模型提供商列表"""
        # 传递用户信息，以便根据登录状态过滤
        providers = LLMProvider.get_enabled_providers(user=request.user)
        serializer = LLMProviderListSerializer(providers, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def default(self, request):
        """获取默认大模型提供商"""
        provider = LLMProvider.get_default_provider()
        if provider:
            serializer = LLMProviderListSerializer(provider)
            return Response(serializer.data)
        return Response({'error': '未找到默认大模型提供商'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """设置为默认大模型提供商"""
        error_response = self.check_admin_permission(request)
        if error_response:
            return error_response

        provider = self.get_object()
        if not provider.is_enabled:
            return Response({'error': '只能将启用的提供商设置为默认'},
                          status=status.HTTP_400_BAD_REQUEST)

        # 取消其他提供商的默认状态
        LLMProvider.objects.filter(is_default=True).update(is_default=False)
        # 设置当前提供商为默认
        provider.is_default = True
        provider.save()

        serializer = LLMProviderSerializer(provider)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def toggle_enabled(self, request, pk=None):
        """切换启用状态"""
        error_response = self.check_admin_permission(request)
        if error_response:
            return error_response

        provider = self.get_object()
        provider.is_enabled = not provider.is_enabled

        # 如果禁用的是默认提供商，需要取消默认状态
        if not provider.is_enabled and provider.is_default:
            provider.is_default = False

        provider.save()

        serializer = LLMProviderSerializer(provider)
        return Response(serializer.data)
