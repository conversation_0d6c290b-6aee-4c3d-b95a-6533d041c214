"""
自定义中间件
"""
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.http import HttpResponse
from django.template import Template, Context
from django.shortcuts import render


class SecurityHeadersMiddleware:
    """
    安全头中间件 - 规避各种安全策略错误
    根据用户偏好，在所有环境中规避安全性限制
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # 规避Cross-Origin-Opener-Policy错误
        response['Cross-Origin-Opener-Policy'] = 'unsafe-none'
        
        # 规避Cross-Origin-Embedder-Policy错误
        response['Cross-Origin-Embedder-Policy'] = 'unsafe-none'
        
        # 允许所有来源的跨域请求
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH'
        response['Access-Control-Allow-Headers'] = '*'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'
        
        # 规避X-Frame-Options限制
        response['X-Frame-Options'] = 'ALLOWALL'
        
        # 规避Content-Security-Policy限制
        response['Content-Security-Policy'] = "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data: blob:; font-src * data:; connect-src * blob:; media-src * blob:; object-src *; child-src * blob:; worker-src * blob:; frame-src *; form-action *; base-uri *;"
        
        # 规避Referrer-Policy限制
        response['Referrer-Policy'] = 'unsafe-url'
        
        # 规避Permissions-Policy限制 - 允许常用功能
        response['Permissions-Policy'] = 'camera=*, microphone=*, geolocation=*, payment=*, usb=*, accelerometer=*, gyroscope=*, magnetometer=*, fullscreen=*, picture-in-picture=*, display-capture=*, clipboard-read=*, clipboard-write=*'
        
        # 移除可能导致问题的安全头
        headers_to_remove = [
            'Strict-Transport-Security',
            'X-Content-Type-Options',
            'X-XSS-Protection',
        ]
        
        for header in headers_to_remove:
            if header in response:
                del response[header]
        
        return response


class MaintenanceModeMiddleware:
    """
    维护模式中间件
    当系统开启维护模式时，拦截所有请求并显示维护页面
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 检查维护模式状态
        if self.is_maintenance_mode_enabled():
            # 允许管理员访问
            if request.user.is_authenticated and request.user.is_superuser:
                return self.get_response(request)

            # 允许访问管理后台
            if request.path.startswith('/admin/'):
                return self.get_response(request)

            # 允许访问API登录接口
            if request.path.startswith('/api/auth/'):
                return self.get_response(request)

            # 其他请求显示维护页面
            return self.render_maintenance_page(request)

        return self.get_response(request)

    def is_maintenance_mode_enabled(self):
        """检查维护模式是否启用"""
        try:
            from .models import SystemSettings
            settings = SystemSettings.get_settings()
            return settings.maintenance_mode
        except Exception:
            return False

    def render_maintenance_page(self, request):
        """渲染维护页面"""
        try:
            from .models import SystemSettings
            settings = SystemSettings.get_settings()
            maintenance_message = settings.maintenance_message or '系统正在维护中，请稍后再试。'
            site_name = settings.site_name or 'Agent Portal'
        except Exception:
            maintenance_message = '系统正在维护中，请稍后再试。'
            site_name = 'Agent Portal'

        # 简单的维护页面HTML
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>系统维护中 - {site_name}</title>
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0;
                    padding: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }}
                .maintenance-container {{
                    background: white;
                    border-radius: 10px;
                    padding: 40px;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    max-width: 500px;
                    margin: 20px;
                }}
                .maintenance-icon {{
                    font-size: 64px;
                    margin-bottom: 20px;
                }}
                .maintenance-title {{
                    color: #333;
                    font-size: 28px;
                    margin-bottom: 20px;
                    font-weight: 600;
                }}
                .maintenance-message {{
                    color: #666;
                    font-size: 16px;
                    line-height: 1.6;
                    margin-bottom: 30px;
                }}
                .maintenance-footer {{
                    color: #999;
                    font-size: 14px;
                }}
            </style>
        </head>
        <body>
            <div class="maintenance-container">
                <div class="maintenance-icon">🔧</div>
                <h1 class="maintenance-title">系统维护中</h1>
                <p class="maintenance-message">{maintenance_message}</p>
                <div class="maintenance-footer">
                    感谢您的耐心等待
                </div>
            </div>
        </body>
        </html>
        """

        return HttpResponse(html_content, status=503)


class CORSMiddleware:
    """
    自定义CORS中间件 - 处理所有跨域请求
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 处理预检请求
        if request.method == 'OPTIONS':
            response = self.create_cors_response()
        else:
            response = self.get_response(request)
            self.add_cors_headers(response)
        
        return response
    
    def create_cors_response(self):
        """创建CORS预检响应"""
        from django.http import HttpResponse
        
        response = HttpResponse()
        response.status_code = 200
        self.add_cors_headers(response)
        return response
    
    def add_cors_headers(self, response):
        """添加CORS头"""
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD'
        response['Access-Control-Allow-Headers'] = 'Accept, Accept-Language, Content-Language, Content-Type, Authorization, X-Requested-With, X-CSRFToken, Origin, Referer, User-Agent'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'
        response['Access-Control-Expose-Headers'] = 'Content-Type, Authorization, X-CSRFToken'


class DisableCSRFMiddleware:
    """
    完全禁用CSRF验证的中间件
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 设置多个CSRF豁免标记
        setattr(request, '_dont_enforce_csrf_checks', True)
        setattr(request, 'csrf_processing_done', True)

        # 为所有POST请求添加CSRF token（如果不存在）
        if request.method == 'POST' and 'csrfmiddlewaretoken' not in request.POST:
            request.POST = request.POST.copy()
            request.POST['csrfmiddlewaretoken'] = 'dummy-csrf-token'

        response = self.get_response(request)

        # 确保响应中包含CSRF相关头
        if hasattr(request, 'META'):
            response['X-CSRFToken'] = 'dummy-csrf-token'

        return response
