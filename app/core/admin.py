from django.contrib import admin
from .models import SystemSettings


@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    """系统设置管理"""
    
    list_display = ('__str__', 'max_conversations_per_user', 'maintenance_mode', 'allow_registration', 'updated_at')
    
    fieldsets = (
        ('平台标题设置', {
            'fields': ('site_name', 'site_title', 'site_description'),
            'description': '自定义平台名称、标题和描述'
        }),
        ('会话管理', {
            'fields': ('max_conversations_per_user', 'max_messages_per_conversation'),
            'description': '控制用户会话和消息的数量限制'
        }),
        ('文件和API限制', {
            'fields': ('max_file_size_mb', 'api_rate_limit_per_minute'),
            'description': '控制文件上传和API调用的限制'
        }),
        ('系统维护', {
            'fields': ('maintenance_mode', 'maintenance_message'),
            'description': '系统维护模式设置'
        }),
        ('用户管理', {
            'fields': ('allow_registration',),
            'description': '用户注册和访问控制'
        }),
        ('功能开关', {
            'fields': ('enable_search',),
            'description': '控制系统功能的启用和禁用'
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def has_add_permission(self, request):
        # 只允许有一个设置记录
        return not SystemSettings.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        # 不允许删除设置记录
        return False
    
    def changelist_view(self, request, extra_context=None):
        # 如果没有设置记录，自动创建一个
        if not SystemSettings.objects.exists():
            SystemSettings.get_settings()
        return super().changelist_view(request, extra_context)
