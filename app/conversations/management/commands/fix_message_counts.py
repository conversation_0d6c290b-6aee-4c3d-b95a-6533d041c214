from django.core.management.base import BaseCommand
from django.db import transaction
from conversations.models import Conversation


class Command(BaseCommand):
    help = '修复会话的消息数量统计'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要修复的会话，不实际修复'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # 获取所有会话
        conversations = Conversation.objects.all()
        total_count = conversations.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.SUCCESS('没有找到需要修复的会话')
            )
            return
        
        fixed_count = 0
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'[DRY RUN] 将要检查 {total_count} 个会话的消息数量')
            )
        else:
            self.stdout.write(f'开始修复 {total_count} 个会话的消息数量...')
        
        with transaction.atomic():
            for conversation in conversations:
                # 计算实际消息数量
                actual_count = conversation.messages.count()
                stored_count = conversation.message_count
                
                if actual_count != stored_count:
                    if dry_run:
                        self.stdout.write(
                            f'  - {conversation.title} (ID: {conversation.id}): '
                            f'存储数量={stored_count}, 实际数量={actual_count}'
                        )
                    else:
                        # 更新消息数量
                        conversation.message_count = actual_count
                        
                        # 更新最后消息时间
                        last_message = conversation.messages.order_by('-created_at').first()
                        if last_message:
                            conversation.last_message_at = last_message.created_at
                        
                        conversation.save(update_fields=['message_count', 'last_message_at'])
                        
                        self.stdout.write(
                            f'  ✓ 修复 {conversation.title}: {stored_count} → {actual_count}'
                        )
                    
                    fixed_count += 1
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'[DRY RUN] 发现 {fixed_count} 个会话需要修复')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'成功修复 {fixed_count} 个会话的消息数量统计')
            )
        
        self.stdout.write(
            self.style.SUCCESS('修复任务完成')
        )
