from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from conversations.models import Conversation


class Command(BaseCommand):
    help = '清理没有用户消息的空会话'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=1,
            help='清理多少天前创建的空会话（默认1天）'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要删除的会话，不实际删除'
        )

    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        
        # 计算时间阈值
        threshold_date = timezone.now() - timedelta(days=days)
        
        # 查找没有用户消息的空会话
        empty_conversations = Conversation.objects.filter(
            created_at__lt=threshold_date
        ).exclude(
            messages__type='user'
        ).distinct()
        
        count = empty_conversations.count()
        
        if count == 0:
            self.stdout.write(
                self.style.SUCCESS(f'没有找到需要清理的空会话（{days}天前创建）')
            )
            return
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'[DRY RUN] 将要删除 {count} 个空会话：')
            )
            for conv in empty_conversations[:10]:  # 只显示前10个
                self.stdout.write(f'  - {conv.title} (用户: {conv.user.username}, 创建于: {conv.created_at})')
            if count > 10:
                self.stdout.write(f'  ... 还有 {count - 10} 个会话')
        else:
            # 实际删除
            deleted_conversations = []
            for conv in empty_conversations:
                deleted_conversations.append({
                    'title': conv.title,
                    'user': conv.user.username,
                    'created_at': conv.created_at
                })
            
            empty_conversations.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f'成功删除 {count} 个空会话')
            )
            
            # 显示删除的会话详情
            for conv_info in deleted_conversations[:5]:  # 只显示前5个
                self.stdout.write(
                    f'  - {conv_info["title"]} (用户: {conv_info["user"]}, 创建于: {conv_info["created_at"]})'
                )
            if count > 5:
                self.stdout.write(f'  ... 还有 {count - 5} 个会话被删除')
        
        self.stdout.write(
            self.style.SUCCESS(f'清理任务完成')
        )
