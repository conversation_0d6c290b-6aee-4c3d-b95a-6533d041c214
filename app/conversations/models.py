from django.db import models
from django.contrib.auth import get_user_model
from agents.models import Agent
import uuid

User = get_user_model()


class Conversation(models.Model):
    """对话模型"""

    STATUS_CHOICES = [
        ('active', '进行中'),
        ('archived', '已归档'),
        ('deleted', '已删除'),
    ]

    # 基础信息
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200, verbose_name='标题')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='状态')

    # 关联信息
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversations', verbose_name='用户')
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, related_name='conversations', verbose_name='智能体')

    # 统计信息
    message_count = models.PositiveIntegerField(default=0, verbose_name='消息数量')
    total_tokens = models.PositiveIntegerField(default=0, verbose_name='总Token数')

    # 外部服务会话ID（如RagFlow的session_id）
    external_session_id = models.CharField(max_length=255, blank=True, null=True, verbose_name='外部会话ID')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    last_message_at = models.DateTimeField(null=True, blank=True, verbose_name='最后消息时间')

    class Meta:
        verbose_name = '对话'
        verbose_name_plural = '对话'
        ordering = ['-updated_at']

    def __str__(self):
        return f'{self.title} - {self.user.username}'

    def get_short_title(self):
        """获取短标题"""
        if len(self.title) > 30:
            return self.title[:30] + '...'
        return self.title


class Message(models.Model):
    """消息模型"""

    TYPE_CHOICES = [
        ('user', '用户消息'),
        ('assistant', '助手回复'),
        ('system', '系统消息'),
    ]

    STATUS_CHOICES = [
        ('sending', '发送中'),
        ('sent', '已发送'),
        ('delivered', '已送达'),
        ('failed', '发送失败'),
    ]

    # 基础信息
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages', verbose_name='对话')
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, verbose_name='消息类型')
    content = models.TextField(verbose_name='消息内容')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='sent', verbose_name='状态')

    # 元数据
    metadata = models.JSONField(default=dict, blank=True, verbose_name='元数据')
    token_count = models.PositiveIntegerField(default=0, verbose_name='Token数量')
    response_time = models.FloatField(null=True, blank=True, verbose_name='响应时间(秒)')

    # 引用和来源
    sources = models.JSONField(default=list, blank=True, verbose_name='引用来源')
    parent_message = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父消息')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '消息'
        verbose_name_plural = '消息'
        ordering = ['created_at']

    def __str__(self):
        content_preview = self.content[:50] + '...' if len(self.content) > 50 else self.content
        return f'{self.type}: {content_preview}'

    def save(self, *args, **kwargs):
        """保存时更新对话的统计信息"""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            # 更新对话的消息数量和最后消息时间
            self.conversation.message_count += 1
            self.conversation.last_message_at = self.created_at
            self.conversation.save(update_fields=['message_count', 'last_message_at'])


class ConversationShare(models.Model):
    """对话分享模型"""

    conversation = models.OneToOneField(Conversation, on_delete=models.CASCADE, related_name='share', verbose_name='对话')
    share_token = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='分享令牌')
    is_public = models.BooleanField(default=False, verbose_name='是否公开')
    password = models.CharField(max_length=50, blank=True, verbose_name='访问密码')
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')
    view_count = models.PositiveIntegerField(default=0, verbose_name='查看次数')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '对话分享'
        verbose_name_plural = '对话分享'

    def __str__(self):
        return f'{self.conversation.title} 的分享'

    def increment_view_count(self):
        """增加查看次数"""
        self.view_count += 1
        self.save(update_fields=['view_count'])
