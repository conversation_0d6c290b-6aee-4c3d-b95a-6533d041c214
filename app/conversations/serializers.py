from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Conversation, Message, ConversationShare
from agents.models import Agent
from agents.serializers import AgentListSerializer

User = get_user_model()


class MessageSerializer(serializers.ModelSerializer):
    """消息序列化器"""
    
    class Meta:
        model = Message
        fields = '__all__'
        read_only_fields = ('id', 'conversation', 'created_at', 'updated_at')


class MessageCreateSerializer(serializers.ModelSerializer):
    """消息创建序列化器"""
    
    class Meta:
        model = Message
        fields = ('type', 'content', 'metadata', 'sources', 'parent_message')
    
    def create(self, validated_data):
        # conversation 将在视图中设置
        return Message.objects.create(**validated_data)


class ConversationSerializer(serializers.ModelSerializer):
    """对话序列化器"""
    
    agent = AgentListSerializer(read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)
    messages = MessageSerializer(many=True, read_only=True)
    
    class Meta:
        model = Conversation
        fields = '__all__'
        read_only_fields = ('id', 'user', 'message_count', 'total_tokens', 
                           'created_at', 'updated_at', 'last_message_at')


class ConversationListSerializer(serializers.ModelSerializer):
    """对话列表序列化器"""

    agent = AgentListSerializer(read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)
    last_message = serializers.SerializerMethodField()
    message_count = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = ('id', 'title', 'status', 'agent', 'user_username',
                 'message_count', 'created_at', 'updated_at', 'last_message_at', 'last_message')

    def get_message_count(self, obj):
        """动态计算消息数量"""
        return obj.messages.count()

    def get_last_message(self, obj):
        """获取最后一条消息"""
        last_message = obj.messages.order_by('-created_at').first()
        if last_message:
            return {
                'content': last_message.content[:100] + '...' if len(last_message.content) > 100 else last_message.content,
                'type': last_message.type,
                'created_at': last_message.created_at
            }
        return None


class ConversationCreateSerializer(serializers.ModelSerializer):
    """对话创建序列化器"""

    agent_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = Conversation
        fields = ('id', 'title', 'agent_id')
        read_only_fields = ('id',)
    
    def validate_agent_id(self, value):
        """验证智能体ID"""
        try:
            agent = Agent.objects.get(id=value)
            # 检查用户是否有权限使用此智能体
            user = self.context['request'].user
            if not agent.is_public and agent.owner != user:
                raise serializers.ValidationError("您没有权限使用此智能体")
            return value
        except Agent.DoesNotExist:
            raise serializers.ValidationError("智能体不存在")
    
    def create(self, validated_data):
        agent_id = validated_data.pop('agent_id')
        agent = Agent.objects.get(id=agent_id)
        validated_data['agent'] = agent
        # 不在这里设置user，让视图的perform_create处理
        return Conversation.objects.create(**validated_data)


class SendMessageSerializer(serializers.Serializer):
    """发送消息序列化器"""
    
    conversation_id = serializers.UUIDField()
    content = serializers.CharField(max_length=10000)
    type = serializers.ChoiceField(choices=Message.TYPE_CHOICES, default='user')
    metadata = serializers.JSONField(required=False, default=dict)
    
    def validate_conversation_id(self, value):
        """验证对话ID"""
        try:
            conversation = Conversation.objects.get(id=value)
            # 暂时不检查用户权限
            return value
        except Conversation.DoesNotExist:
            raise serializers.ValidationError("对话不存在")


class ConversationShareSerializer(serializers.ModelSerializer):
    """对话分享序列化器"""
    
    conversation_title = serializers.CharField(source='conversation.title', read_only=True)
    share_url = serializers.SerializerMethodField()
    
    class Meta:
        model = ConversationShare
        fields = '__all__'
        read_only_fields = ('conversation', 'share_token', 'view_count', 'created_at', 'updated_at')
    
    def get_share_url(self, obj):
        """获取分享链接"""
        request = self.context.get('request')
        if request:
            return request.build_absolute_uri(f'/share/{obj.share_token}/')
        return f'/share/{obj.share_token}/'


class ConversationStatsSerializer(serializers.Serializer):
    """对话统计序列化器"""
    
    total_conversations = serializers.IntegerField()
    active_conversations = serializers.IntegerField()
    total_messages = serializers.IntegerField()
    avg_messages_per_conversation = serializers.FloatField()
    most_used_agents = serializers.ListField()
    recent_activity = serializers.ListField()


class ConversationExportSerializer(serializers.Serializer):
    """对话导出序列化器"""
    
    format = serializers.ChoiceField(
        choices=[('json', 'JSON'), ('txt', 'Text'), ('md', 'Markdown')],
        default='json'
    )
    include_metadata = serializers.BooleanField(default=False)
    include_sources = serializers.BooleanField(default=True)
