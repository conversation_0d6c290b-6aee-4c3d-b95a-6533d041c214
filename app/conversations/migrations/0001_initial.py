# Generated by Django 5.2.3 on 2025-06-20 06:27

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('agents', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('status', models.CharField(choices=[('active', '进行中'), ('archived', '已归档'), ('deleted', '已删除')], default='active', max_length=20, verbose_name='状态')),
                ('message_count', models.PositiveIntegerField(default=0, verbose_name='消息数量')),
                ('total_tokens', models.PositiveIntegerField(default=0, verbose_name='总Token数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('last_message_at', models.DateTimeField(blank=True, null=True, verbose_name='最后消息时间')),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversations', to='agents.agent', verbose_name='智能体')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversations', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '对话',
                'verbose_name_plural': '对话',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='ConversationShare',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('share_token', models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='分享令牌')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('password', models.CharField(blank=True, max_length=50, verbose_name='访问密码')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='过期时间')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='查看次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('conversation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='share', to='conversations.conversation', verbose_name='对话')),
            ],
            options={
                'verbose_name': '对话分享',
                'verbose_name_plural': '对话分享',
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('user', '用户消息'), ('assistant', '助手回复'), ('system', '系统消息')], max_length=20, verbose_name='消息类型')),
                ('content', models.TextField(verbose_name='消息内容')),
                ('status', models.CharField(choices=[('sending', '发送中'), ('sent', '已发送'), ('delivered', '已送达'), ('failed', '发送失败')], default='sent', max_length=20, verbose_name='状态')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('token_count', models.PositiveIntegerField(default=0, verbose_name='Token数量')),
                ('response_time', models.FloatField(blank=True, null=True, verbose_name='响应时间(秒)')),
                ('sources', models.JSONField(blank=True, default=list, verbose_name='引用来源')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='conversations.conversation', verbose_name='对话')),
                ('parent_message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='conversations.message', verbose_name='父消息')),
            ],
            options={
                'verbose_name': '消息',
                'verbose_name_plural': '消息',
                'ordering': ['created_at'],
            },
        ),
    ]
