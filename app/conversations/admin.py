from django.contrib import admin
from .models import Conversation, Message, ConversationShare


class MessageInline(admin.TabularInline):
    """消息内联编辑"""
    model = Message
    extra = 0
    readonly_fields = ('id', 'type', 'created_at', 'updated_at')
    fields = ('type', 'content', 'status', 'token_count', 'response_time', 'created_at')

    def has_add_permission(self, request, obj=None):
        return False  # 不允许在这里添加消息


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    """对话管理"""

    list_display = ('title', 'user', 'agent', 'status', 'message_count',
                   'total_tokens', 'created_at', 'last_message_at')
    list_filter = ('status', 'agent__platform', 'created_at')
    search_fields = ('title', 'user__username', 'agent__name')
    ordering = ('-created_at',)
    inlines = [MessageInline]

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'user', 'agent', 'status')
        }),
        ('统计信息', {
            'fields': ('message_count', 'total_tokens'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'last_message_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('message_count', 'total_tokens', 'created_at',
                      'updated_at', 'last_message_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'agent')


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """消息管理"""

    list_display = ('conversation', 'type', 'content_preview', 'status',
                   'token_count', 'response_time', 'created_at')
    list_filter = ('type', 'status', 'created_at')
    search_fields = ('content', 'conversation__title', 'conversation__user__username')
    ordering = ('-created_at',)

    fieldsets = (
        ('基本信息', {
            'fields': ('conversation', 'type', 'content', 'status')
        }),
        ('元数据', {
            'fields': ('metadata', 'sources', 'parent_message'),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': ('token_count', 'response_time'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')

    def content_preview(self, obj):
        """内容预览"""
        return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
    content_preview.short_description = '内容预览'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('conversation', 'conversation__user')


@admin.register(ConversationShare)
class ConversationShareAdmin(admin.ModelAdmin):
    """对话分享管理"""

    list_display = ('conversation', 'is_public', 'view_count', 'expires_at', 'created_at')
    list_filter = ('is_public', 'created_at')
    search_fields = ('conversation__title', 'conversation__user__username')
    ordering = ('-created_at',)

    fieldsets = (
        ('基本信息', {
            'fields': ('conversation', 'share_token', 'is_public', 'password')
        }),
        ('统计信息', {
            'fields': ('view_count', 'expires_at')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('share_token', 'view_count', 'created_at', 'updated_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('conversation', 'conversation__user')
