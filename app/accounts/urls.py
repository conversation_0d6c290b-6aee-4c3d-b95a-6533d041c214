from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

# API路由
router = DefaultRouter()

urlpatterns = [
    # 认证相关API
    path('register/', views.RegisterView.as_view(), name='register'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('profile/', views.ProfileView.as_view(), name='profile'),
    path('profile/config/', views.ProfileConfigView.as_view(), name='profile-config'),
    path('change-password/', views.change_password_view, name='change-password'),
    path('reset-password/', views.reset_password_view, name='reset-password'),

    # DRF路由
    path('', include(router.urls)),
]
