from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from .models import User, UserProfile
from .serializers import (
    UserRegistrationSerializer,
    UserLoginSerializer,
    UserProfileSerializer,
    UserProfileConfigSerializer,
    ChangePasswordSerializer,
    ResetPasswordSerializer
)
from core.models import SystemSettings


class RegisterView(generics.CreateAPIView):
    """用户注册视图"""

    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        # 检查是否允许注册
        system_settings = SystemSettings.get_settings()
        if not system_settings.allow_registration:
            return Response({
                'error': '用户注册已被禁用',
                'message': '管理员已禁用新用户注册功能'
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # 创建Token
        token, created = Token.objects.get_or_create(user=user)

        return Response({
            'message': '注册成功',
            'user': UserProfileSerializer(user).data,
            'token': token.key
        }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """用户登录视图"""

    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']
        login(request, user)

        # 获取或创建Token
        token, created = Token.objects.get_or_create(user=user)

        return Response({
            'message': '登录成功',
            'user': UserProfileSerializer(user).data,
            'token': token.key
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    """用户登出视图"""

    try:
        # 删除Token
        request.user.auth_token.delete()
    except:
        pass

    logout(request)
    return Response({'message': '登出成功'})


class ProfileView(generics.RetrieveUpdateAPIView):
    """用户资料视图"""

    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class ProfileConfigView(generics.RetrieveUpdateAPIView):
    """用户配置视图"""

    serializer_class = UserProfileConfigSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def change_password_view(request):
    """修改密码视图"""

    serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()

        # 删除旧Token，强制重新登录
        try:
            user.auth_token.delete()
        except:
            pass

        return Response({'message': '密码修改成功，请重新登录'})

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def reset_password_view(request):
    """重置密码视图（不需要原密码）"""

    serializer = ResetPasswordSerializer(data=request.data)
    if serializer.is_valid():
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()

        # 删除旧Token，强制重新登录
        try:
            user.auth_token.delete()
        except:
            pass

        return Response({'message': '密码重置成功，请重新登录'})

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
