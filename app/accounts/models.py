from django.contrib.auth.models import AbstractUser
from django.db import models


class User(AbstractUser):
    """扩展用户模型"""

    # 基础信息
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, verbose_name='头像')
    bio = models.TextField(max_length=500, blank=True, verbose_name='个人简介')

    # 统计信息
    total_conversations = models.PositiveIntegerField(default=0, verbose_name='总对话数')
    total_messages = models.PositiveIntegerField(default=0, verbose_name='总消息数')

    # 设置
    preferred_language = models.CharField(
        max_length=10,
        default='zh-hans',
        choices=[
            ('zh-hans', '简体中文'),
            ('zh-hant', '繁体中文'),
            ('en', 'English'),
        ],
        verbose_name='首选语言'
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'

    def __str__(self):
        return self.username


class UserProfile(models.Model):
    """用户配置文件"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile', verbose_name='用户')

    # API配置
    openai_api_key = models.CharField(max_length=200, blank=True, verbose_name='OpenAI API Key')
    dify_api_key = models.CharField(max_length=200, blank=True, verbose_name='Dify API Key')
    ragflow_api_key = models.CharField(max_length=200, blank=True, verbose_name='RagFlow API Key')

    # 偏好设置
    default_agent_id = models.PositiveIntegerField(null=True, blank=True, verbose_name='默认智能体ID')
    auto_save_conversations = models.BooleanField(default=True, verbose_name='自动保存对话')
    enable_notifications = models.BooleanField(default=True, verbose_name='启用通知')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户配置'
        verbose_name_plural = '用户配置'

    def __str__(self):
        return f'{self.user.username} 的配置'
