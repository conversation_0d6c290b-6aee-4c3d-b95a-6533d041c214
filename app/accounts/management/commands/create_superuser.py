from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import IntegrityError

User = get_user_model()


class Command(BaseCommand):
    help = '创建默认超级用户'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='admin',
            help='超级用户用户名（默认：admin）'
        )
        parser.add_argument(
            '--password',
            type=str,
            default='admin123',
            help='超级用户密码（默认：admin123）'
        )
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='超级用户邮箱（默认：<EMAIL>）'
        )

    def handle(self, *args, **options):
        username = options['username']
        password = options['password']
        email = options['email']

        try:
            # 检查用户是否已存在
            if User.objects.filter(username=username).exists():
                self.stdout.write(
                    self.style.WARNING(f'用户 "{username}" 已存在')
                )
                return

            # 创建超级用户
            user = User.objects.create_superuser(
                username=username,
                email=email,
                password=password
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f'成功创建超级用户:\n'
                    f'用户名: {username}\n'
                    f'密码: {password}\n'
                    f'邮箱: {email}'
                )
            )

        except IntegrityError as e:
            self.stdout.write(
                self.style.ERROR(f'创建超级用户失败: {e}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'发生错误: {e}')
            )
