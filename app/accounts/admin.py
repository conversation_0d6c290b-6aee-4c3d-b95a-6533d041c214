from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, UserProfile


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """用户管理"""

    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'is_active', 'date_joined')
    list_filter = ('is_staff', 'is_active', 'preferred_language', 'date_joined')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    ordering = ('-date_joined',)

    fieldsets = BaseUserAdmin.fieldsets + (
        ('扩展信息', {
            'fields': ('avatar', 'bio', 'preferred_language', 'total_conversations', 'total_messages')
        }),
    )

    readonly_fields = ('total_conversations', 'total_messages', 'date_joined', 'last_login')


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户配置管理"""

    list_display = ('user', 'default_agent_id', 'auto_save_conversations', 'enable_notifications', 'created_at')
    list_filter = ('auto_save_conversations', 'enable_notifications', 'created_at')
    search_fields = ('user__username', 'user__email')
    ordering = ('-created_at',)

    fieldsets = (
        ('基本信息', {
            'fields': ('user',)
        }),
        ('API配置', {
            'fields': ('openai_api_key', 'dify_api_key', 'ragflow_api_key'),
            'classes': ('collapse',)
        }),
        ('偏好设置', {
            'fields': ('default_agent_id', 'auto_save_conversations', 'enable_notifications')
        }),
    )
