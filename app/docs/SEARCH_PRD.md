# 搜索页面产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品背景
Agent Portal 需要一个专门的知识搜索页面，用户可以通过搜索获取基于知识库的智能回答和相关文档片段，类似 RagFlow 的搜索体验。

### 1.2 产品目标
- 提供专业的知识搜索体验
- 整合多个 RagFlow 知识库的搜索能力
- 为用户提供准确、相关的知识检索结果
- 支持从搜索结果进入深度对话

### 1.3 目标用户
- 需要快速查找特定信息的用户
- 希望基于知识库获取准确答案的用户
- 需要浏览相关文档片段的用户

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 搜索输入
- **搜索框**：支持自然语言查询输入
- **搜索按钮**：触发搜索操作
- **回车搜索**：支持键盘回车快捷搜索
- **搜索历史**：保存用户最近的搜索记录（可选）

#### 2.1.2 智能回答区域
- **AI 生成回答**：基于知识库内容生成的智能回答
- **回答来源标注**：显示回答基于哪些知识库
- **回答质量指标**：显示置信度或相关性评分
- **复制功能**：支持一键复制回答内容

#### 2.1.3 搜索结果列表
- **文档片段展示**：显示相关的知识库文档片段
- **关键词高亮**：在结果中高亮显示搜索关键词
- **来源信息**：显示文档标题、知识库名称、创建时间
- **相关性排序**：按相关性对结果进行排序
- **片段预览**：显示文档片段的上下文内容

#### 2.1.4 分页功能
- **页码导航**：支持翻页浏览更多结果
- **每页结果数**：可配置每页显示的结果数量（默认10条）
- **总数显示**：显示搜索结果总数
- **加载状态**：分页加载时的状态提示

#### 2.1.5 相关问题推荐
- **智能推荐**：基于当前搜索生成相关问题
- **一键搜索**：点击推荐问题直接搜索
- **问题分类**：按主题对推荐问题进行分组

### 2.2 版本限制

#### 2.2.1 当前版本限制（V1.0）
- **知识库范围**：仅搜索状态为"激活"(active)的知识库
- **权限控制**：用户只能搜索自己拥有的或公开的激活知识库
- **平台支持**：当前版本主要支持 RagFlow 平台的知识库

#### 2.2.2 未来版本功能（V2.0+）
- **知识库筛选**：选择特定知识库进行搜索
- **时间筛选**：按文档创建时间筛选
- **文档类型筛选**：按文档类型（PDF、Word、文本等）筛选
- **相关性筛选**：设置最低相关性阈值

#### 2.2.2 结果操作
- **收藏功能**：收藏有用的搜索结果
- **分享功能**：分享搜索结果链接
- **导出功能**：导出搜索结果为文档
- **进入对话**：从搜索结果跳转到聊天页面

#### 2.2.3 搜索优化
- **搜索建议**：输入时提供搜索建议
- **拼写检查**：自动纠正拼写错误
- **同义词扩展**：支持同义词搜索
- **搜索统计**：记录搜索行为用于优化

## 3. 技术需求

### 3.1 后端 API

#### 3.1.1 知识库搜索 API
```
GET /api/knowledge/search/
参数：
- q: 搜索查询字符串
- page: 页码（默认1）
- size: 每页结果数（默认10）
- highlight: 是否高亮（默认true）

返回：
- results: 搜索结果列表
- total: 总结果数
- page: 当前页码
- has_next: 是否有下一页
- active_kb_count: 参与搜索的激活知识库数量

注意：当前版本自动搜索所有用户可访问的激活状态知识库
```

#### 3.1.2 智能问答 API
```
POST /api/knowledge/ask/
参数：
- question: 用户问题

返回：
- answer: AI生成的回答
- sources: 回答来源信息
- confidence: 置信度评分
- kb_sources: 参与回答的知识库信息

注意：自动使用所有用户可访问的激活状态知识库生成回答
```

#### 3.1.3 相关问题 API
```
POST /api/knowledge/related-questions/
参数：
- question: 当前问题

返回：
- questions: 相关问题列表
- categories: 问题分类
```

### 3.2 前端实现

#### 3.2.1 页面路由
- URL: `/search/`
- 支持查询参数: `?q=搜索内容`
- 支持分页参数: `&page=页码`
- 当前版本不支持知识库选择参数

#### 3.2.2 状态管理
- 搜索状态（加载中、成功、失败）
- 搜索结果数据
- 分页信息
- 筛选条件

#### 3.2.3 用户体验
- 响应式设计，支持移动端
- 加载动画和骨架屏
- 错误处理和重试机制
- 无障碍访问支持

### 3.3 RagFlow 集成

#### 3.3.1 API 调用
- 基于现有 RagFlow 知识库配置
- 调用 RagFlow 的搜索和问答 API
- 处理 API 认证和错误

#### 3.3.2 数据处理
- 结果格式标准化
- 内容高亮处理
- 来源信息提取

## 4. 界面设计

### 4.1 页面布局
```
┌─────────────────────────────────────┐
│           顶部导航栏                  │
├─────────────────────────────────────┤
│           搜索框区域                  │
├─────────────────────────────────────┤
│         智能回答区域                  │
├─────────────────────────────────────┤
│                                     │
│         搜索结果列表                  │
│                                     │
├─────────────────────────────────────┤
│           分页导航                   │
├─────────────────────────────────────┤
│         相关问题推荐                  │
└─────────────────────────────────────┘
```

### 4.2 设计规范
- 保持与现有页面风格一致
- 使用 Bootstrap 组件和自定义 CSS
- 主色调与品牌色保持一致
- 字体和间距遵循设计系统

### 4.3 交互设计
- 搜索框获得焦点时高亮
- 搜索结果悬停效果
- 分页按钮状态变化
- 加载状态动画

## 5. 性能要求

### 5.1 响应时间
- 搜索请求响应时间 < 3秒
- 页面加载时间 < 2秒
- 分页切换时间 < 1秒

### 5.2 并发处理
- 支持多用户同时搜索
- API 限流和缓存机制
- 搜索结果缓存优化

### 5.3 数据处理
- 支持大量搜索结果分页
- 高亮处理性能优化
- 内存使用控制

## 6. 安全要求

### 6.1 权限控制
- 用户只能搜索有权限且状态为激活的知识库
- 搜索结果按权限和状态过滤
- API 访问权限验证
- 知识库状态验证（仅激活状态可搜索）

### 6.2 数据安全
- 搜索查询日志记录
- 敏感信息过滤
- XSS 和注入攻击防护

## 7. 测试要求

### 7.1 功能测试
- 搜索功能完整性测试
- 分页功能测试
- 筛选功能测试
- 错误处理测试

### 7.2 性能测试
- 搜索响应时间测试
- 并发用户测试
- 大数据量测试

### 7.3 兼容性测试
- 浏览器兼容性测试
- 移动端适配测试
- 不同屏幕尺寸测试

## 8. 上线计划

### 8.1 开发阶段
1. **Phase 1 (V1.0)**: 基础搜索功能
   - 搜索框、结果展示、分页
   - 仅搜索激活状态的知识库
   - 基础权限控制
2. **Phase 2 (V1.1)**: 智能回答和相关问题推荐
   - 基于激活知识库的智能问答
   - 相关问题推荐功能
3. **Phase 3 (V2.0)**: 高级筛选和优化功能
   - 知识库选择筛选
   - 高级搜索选项
   - 性能优化

### 8.2 测试阶段
- 内部测试
- 用户验收测试
- 性能压力测试

### 8.3 发布计划
- 灰度发布
- 全量发布
- 监控和优化

## 9. 成功指标

### 9.1 用户指标
- 搜索使用率 > 60%
- 搜索成功率 > 90%
- 用户满意度 > 4.0/5.0

### 9.2 技术指标
- 搜索响应时间 < 3秒
- 系统可用性 > 99.5%
- 错误率 < 1%

### 9.3 业务指标
- 知识库使用率提升 30%
- 用户停留时间增加 20%
- 从搜索到对话的转化率 > 15%
