# 平台标题设置功能

## 功能概述

在管理员设置页面新增了自定义平台标题的功能，管理员可以自定义以下几个部分：

1. **平台名称** - 显示在导航栏和页面标题中
2. **首页主标题** - 首页Hero区域的大标题
3. **首页描述** - 首页Hero区域的描述文字

## 功能特点

### 🎨 自定义平台标识
- 平台名称：显示在导航栏左上角，替换默认的"Agent Portal"
- 页面标题：浏览器标签页显示的标题
- 支持中英文及特殊字符

### 📝 首页内容定制
- 主标题：首页Hero区域的大标题，默认为"AI智能体门户"
- 描述文字：主标题下方的描述段落，支持多行文本
- 实时生效，无需重启服务

### 🔒 权限控制
- 仅超级管理员可以访问和修改
- 普通用户和未登录用户无法访问设置页面

## 使用方法

### 1. 访问设置页面
1. 使用超级管理员账号登录
2. 点击右上角用户头像下拉菜单
3. 选择"管理员设置"

### 2. 配置平台标题
1. 在"平台标题设置"区域填写：
   - **平台名称**：最多100个字符
   - **首页主标题**：最多200个字符
   - **首页描述**：最多500个字符
2. 点击"保存设置"按钮

### 3. 查看效果
- 导航栏：立即显示新的平台名称
- 首页：刷新页面查看新的标题和描述
- 浏览器标题：显示为"平台名称 - 首页主标题"

## 技术实现

### 数据库字段
在`SystemSettings`模型中新增了三个字段：
```python
site_name = models.CharField(max_length=100, default='Agent Portal')
site_title = models.CharField(max_length=200, default='AI智能体门户')
site_description = models.TextField(max_length=500, default='...')
```

### Context Processor
创建了`core.context_processors.system_settings`，在所有模板中提供系统设置数据：
```python
def system_settings(request):
    settings = SystemSettings.get_settings()
    return {
        'system_settings': {
            'site_name': settings.site_name,
            'site_title': settings.site_title,
            'site_description': settings.site_description,
            # ...
        }
    }
```

### 模板更新
- `base.html`：导航栏和页面标题使用`{{ system_settings.site_name }}`
- `index.html`：首页标题和描述使用系统设置变量

### API端点
- `GET /api/system-settings/`：获取系统设置
- `PUT /api/system-settings/update/`：更新系统设置

## 默认值

| 字段 | 默认值 |
|------|--------|
| 平台名称 | Agent Portal |
| 首页主标题 | AI智能体门户 |
| 首页描述 | 连接多个AI平台，一站式智能对话体验。支持RagFlow、Dify、OpenAI等主流平台，让AI助手触手可及。 |

## 验证和测试

功能已通过完整测试，包括：
- ✅ 模型字段创建和数据库迁移
- ✅ Context Processor正常工作
- ✅ API端点功能正常
- ✅ 前端表单提交和显示
- ✅ 数据验证和错误处理

## 注意事项

1. **字符限制**：请遵守各字段的最大字符数限制
2. **HTML安全**：输入内容会被自动转义，防止XSS攻击
3. **缓存**：设置更改后立即生效，无需清除缓存
4. **备份**：建议在修改前记录原始设置，以便必要时恢复

## 扩展建议

未来可以考虑添加：
- 平台Logo上传功能
- 主题色彩自定义
- 多语言支持
- 设置历史记录和回滚功能
