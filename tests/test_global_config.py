#!/usr/bin/env python
"""
测试RagFlow知识库全局配置共享功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agent_portal.settings')
django.setup()

from knowledge_bases.models import KnowledgeBase, RagFlowGlobalAuth
from knowledge_bases.ragflow_auth_views import get_ragflow_global_auth
from knowledge_bases.ragflow_auth_service import get_ragflow_auth_header
from django.contrib.auth.models import User


def test_global_config_sharing():
    """测试全局配置共享功能"""
    print("=== 测试RagFlow知识库全局配置共享功能 ===\n")
    
    # 1. 测试全局认证设置
    print("1. 检查全局认证设置...")
    global_auth = get_ragflow_global_auth()
    if global_auth:
        print(f"   ✓ 全局认证设置存在: {global_auth['username']}@{global_auth['url']}")
    else:
        print("   ✗ 全局认证设置不存在")
        print("   请先在管理界面配置RagFlow全局认证设置")
        return False
    
    # 2. 创建测试用户（如果不存在）
    print("\n2. 准备测试用户...")
    test_user, created = User.objects.get_or_create(
        username='test_user',
        defaults={'email': '<EMAIL>'}
    )
    if created:
        print("   ✓ 创建测试用户: test_user")
    else:
        print("   ✓ 使用现有测试用户: test_user")
    
    # 3. 创建使用全局配置的知识库
    print("\n3. 创建使用全局配置的知识库...")
    test_kb_data = {
        'name': '测试全局配置知识库',
        'description': '用于测试全局配置共享的知识库',
        'platform': 'ragflow',
        'platform_kb_id': 'test_kb_global_config',
        'uses_global_config': True,
        'owner': test_user
    }
    
    # 删除可能存在的测试知识库
    KnowledgeBase.objects.filter(
        platform_kb_id='test_kb_global_config',
        owner=test_user
    ).delete()
    
    test_kb = KnowledgeBase.objects.create(**test_kb_data)
    print(f"   ✓ 创建知识库: {test_kb.name} (ID: {test_kb.id})")
    print(f"   ✓ 使用全局配置: {test_kb.uses_global_config}")
    
    # 4. 测试认证头获取
    print("\n4. 测试认证头获取...")
    auth_header = get_ragflow_auth_header(test_kb)
    if auth_header:
        print(f"   ✓ 成功获取认证头: {auth_header[:50]}...")
    else:
        print("   ✗ 无法获取认证头")
        return False
    
    # 5. 创建使用独立配置的知识库进行对比
    print("\n5. 创建使用独立配置的知识库...")
    independent_kb_data = {
        'name': '测试独立配置知识库',
        'description': '用于测试独立配置的知识库',
        'platform': 'ragflow',
        'platform_kb_id': 'test_kb_independent_config',
        'uses_global_config': False,
        'api_endpoint': global_auth['url'],
        'username': global_auth['username'],
        'password': global_auth['password'],
        'owner': test_user
    }
    
    # 删除可能存在的测试知识库
    KnowledgeBase.objects.filter(
        platform_kb_id='test_kb_independent_config',
        owner=test_user
    ).delete()
    
    independent_kb = KnowledgeBase.objects.create(**independent_kb_data)
    print(f"   ✓ 创建知识库: {independent_kb.name} (ID: {independent_kb.id})")
    print(f"   ✓ 使用全局配置: {independent_kb.uses_global_config}")
    
    # 6. 测试独立配置的认证头获取
    print("\n6. 测试独立配置的认证头获取...")
    independent_auth_header = get_ragflow_auth_header(independent_kb)
    if independent_auth_header:
        print(f"   ✓ 成功获取认证头: {independent_auth_header[:50]}...")
    else:
        print("   ✗ 无法获取认证头")
    
    # 7. 对比两种配置方式
    print("\n7. 对比两种配置方式...")
    print(f"   全局配置知识库认证头: {auth_header == independent_auth_header}")
    print(f"   全局配置知识库字段检查:")
    print(f"     - api_endpoint: {test_kb.api_endpoint}")
    print(f"     - username: {test_kb.username}")
    print(f"     - uses_global_config: {test_kb.uses_global_config}")
    
    print(f"   独立配置知识库字段检查:")
    print(f"     - api_endpoint: {independent_kb.api_endpoint}")
    print(f"     - username: {independent_kb.username}")
    print(f"     - uses_global_config: {independent_kb.uses_global_config}")
    
    # 8. 清理测试数据
    print("\n8. 清理测试数据...")
    test_kb.delete()
    independent_kb.delete()
    print("   ✓ 测试数据已清理")
    
    print("\n=== 测试完成 ===")
    return True


def test_serializer_logic():
    """测试序列化器逻辑"""
    print("\n=== 测试序列化器逻辑 ===\n")
    
    from knowledge_bases.serializers import KnowledgeBaseSerializer
    
    # 测试全局配置的序列化器验证
    print("1. 测试全局配置的序列化器验证...")
    
    global_config_data = {
        'name': '测试序列化器全局配置',
        'description': '测试序列化器的全局配置处理',
        'platform': 'ragflow',
        'platform_kb_id': 'test_serializer_global',
        'uses_global_config': True,
        'is_public': False
    }
    
    serializer = KnowledgeBaseSerializer(data=global_config_data)
    if serializer.is_valid():
        print("   ✓ 全局配置数据验证通过")
        validated_data = serializer.validated_data
        print(f"   ✓ 验证后的数据: uses_global_config = {validated_data.get('uses_global_config')}")
    else:
        print("   ✗ 全局配置数据验证失败")
        print(f"   错误: {serializer.errors}")
    
    # 测试独立配置的序列化器验证
    print("\n2. 测试独立配置的序列化器验证...")
    
    independent_config_data = {
        'name': '测试序列化器独立配置',
        'description': '测试序列化器的独立配置处理',
        'platform': 'ragflow',
        'platform_kb_id': 'test_serializer_independent',
        'uses_global_config': False,
        'api_endpoint': 'http://test.ragflow.com:7080',
        'username': 'test_user',
        'password': 'test_password',
        'is_public': False
    }
    
    serializer = KnowledgeBaseSerializer(data=independent_config_data)
    if serializer.is_valid():
        print("   ✓ 独立配置数据验证通过")
        validated_data = serializer.validated_data
        print(f"   ✓ 验证后的数据: uses_global_config = {validated_data.get('uses_global_config')}")
    else:
        print("   ✗ 独立配置数据验证失败")
        print(f"   错误: {serializer.errors}")
    
    print("\n=== 序列化器测试完成 ===")


if __name__ == '__main__':
    try:
        # 测试全局配置共享功能
        success = test_global_config_sharing()
        
        if success:
            # 测试序列化器逻辑
            test_serializer_logic()
            
            print("\n🎉 所有测试通过！全局配置共享功能正常工作。")
        else:
            print("\n❌ 测试失败，请检查配置。")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
