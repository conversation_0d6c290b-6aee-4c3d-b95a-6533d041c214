# RagFlow认证设置持久化修复

## 问题描述

RagFlow知识库管理中的认证设置在程序重启后会丢失，导致用户需要重新配置认证信息。

## 问题原因

原有实现只将RagFlow认证设置保存在Django缓存中，没有持久化到数据库：

1. **只使用缓存存储**：设置仅保存在内存缓存中
2. **缓存易失性**：程序重启时缓存被清空
3. **没有数据库备份**：缺少持久化存储机制

## 解决方案

### 1. 新增数据库模型

创建了 `RagFlowGlobalAuth` 模型来持久化存储全局认证设置：

```python
class RagFlowGlobalAuth(models.Model):
    """RagFlow全局认证设置模型"""
    
    # 认证信息
    url = models.URLField(verbose_name='RagFlow服务地址')
    username = models.CharField(max_length=100, verbose_name='用户名')
    password = models.CharField(max_length=200, verbose_name='密码')
    refresh_interval = models.PositiveIntegerField(default=12, verbose_name='令牌刷新间隔(小时)')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
```

### 2. 修改存储逻辑

更新了 `RagFlowAuthView.post()` 方法，实现双重存储：

- **数据库存储**：持久化保存设置
- **缓存存储**：提高访问速度

### 3. 修改加载逻辑

更新了 `RagFlowAuthView.get()` 和 `get_ragflow_global_auth()` 函数：

- **优先从缓存加载**：快速访问
- **缓存失效时从数据库加载**：确保数据不丢失
- **自动重新缓存**：提高后续访问速度

## 修改的文件

1. **app/knowledge_bases/models.py**
   - 新增 `RagFlowGlobalAuth` 模型
   - 添加单例模式的类方法

2. **app/knowledge_bases/ragflow_auth_views.py**
   - 修改 `get()` 方法支持从数据库加载
   - 修改 `post()` 方法同时保存到数据库和缓存
   - 更新 `get_ragflow_global_auth()` 函数

3. **数据库迁移**
   - 创建了迁移文件 `0003_ragflowglobalauth.py`

4. **管理命令**
   - 新增 `migrate_ragflow_auth` 命令用于数据迁移

## 测试验证

创建了完整的测试脚本验证修复效果：

1. ✅ 数据库存储功能
2. ✅ 缓存清理后的数据恢复
3. ✅ 数据完整性验证
4. ✅ 缓存自动重新填充

## 使用说明

### 对用户的影响

- **无需重新配置**：现有设置会自动迁移到数据库
- **程序重启后设置保持**：不再丢失认证信息
- **性能无影响**：仍然优先使用缓存提高访问速度

### 管理员操作

如果需要手动迁移现有缓存设置到数据库：

```bash
python manage.py migrate_ragflow_auth
```

## 技术细节

### 存储策略

1. **写入时**：同时保存到数据库和缓存
2. **读取时**：优先从缓存读取，缓存失效时从数据库读取并重新缓存
3. **单例模式**：数据库中只保存一份全局设置

### 数据一致性

- 使用事务确保数据库操作的原子性
- 缓存和数据库同步更新
- 自动处理缓存失效情况

### 向后兼容

- 保持原有API接口不变
- 自动从缓存迁移到数据库
- 不影响现有功能

## 总结

此修复彻底解决了RagFlow认证设置在程序重启后丢失的问题，通过数据库持久化存储确保设置的可靠性，同时保持了缓存机制的性能优势。用户无需任何额外操作，设置会自动持久化并在程序重启后恢复。
