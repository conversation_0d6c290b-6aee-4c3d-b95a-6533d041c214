# Agent Portal - AI智能体门户平台

Agent Portal是一个统一的AI智能体门户平台，允许用户在单一界面中接入和使用来自不同平台（Dify、RagFlow、OpenAI等）的AI智能体。

## 核心功能

- **统一门户**: 集成多个AI平台的智能体服务
- **搜索对话**: 类似RagFlow的三栏布局对话界面
- **智能体管理**: 支持多平台智能体的配置和管理
- **用户系统**: 完整的用户注册、登录和权限管理
- **对话历史**: 保存和管理用户的对话记录

## 支持的AI平台

- Dify智能体
- RagFlow知识库
- OpenAI API
- 自定义API接入
- 外部链接

## 目标用户

- 个人用户：需要使用多个AI平台服务
- 企业用户：统一管理各种AI智能体的团队
- 开发者：需要集成多个AI服务的开发人员

## 设计原则

- 简洁直观的用户界面
- 响应式设计，支持桌面和移动端
- 统一的视觉风格和交互模式
- 高性能和可扩展性