# 技术栈和开发环境

## 后端技术栈

- **框架**: Django 5.2.3 + Django REST Framework 3.16.0
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **缓存**: Redis 6.2.0
- **异步任务**: Celery 5.5.3
- **认证**: Django内置认证 + Token认证
- **API**: RESTful API with DRF

## 前端技术栈

- **框架**: 纯HTML + Bootstrap 5.3
- **样式**: Bootstrap CSS + 自定义CSS
- **图标**: Bootstrap Icons
- **交互**: 原生JavaScript + Bootstrap JS
- **构建**: 无需构建工具，直接运行

## AI平台集成

- **HTTP客户端**: requests 2.32.3, httpx 0.28.1
- **OpenAI**: openai 1.58.1
- **加密**: pycryptodome 3.15.0

## 开发工具

- **调试**: django-debug-toolbar 4.4.6
- **测试**: pytest-django 4.9.0
- **代码格式化**: black 24.10.0
- **代码检查**: flake8 7.1.1

## 生产环境

- **WSGI服务器**: gunicorn 23.0.0
- **静态文件**: whitenoise 6.8.2
- **数据库URL**: dj-database-url 2.3.0
- **容器化**: Docker + Docker Compose

## 常用命令

### 开发环境启动
```bash
cd app
python manage.py runserver
```

### 数据库迁移
```bash
python manage.py makemigrations
python manage.py migrate
```

### 创建超级用户
```bash
python manage.py createsuperuser
```

### 收集静态文件
```bash
python manage.py collectstatic
```

### 运行测试
```bash
python manage.py test
pytest
```

### Docker部署
```bash
cd docker
docker-compose up -d
```

### 代码格式化
```bash
black .
flake8 .
```

## 项目配置

- **设置文件**: `app/agent_portal/settings.py`
- **URL配置**: `app/agent_portal/urls.py`
- **WSGI**: `app/agent_portal/wsgi.py`
- **环境变量**: 使用python-decouple管理

## 安全配置

- CSRF验证已禁用（通过自定义中间件）
- CORS允许所有来源
- 自定义安全头中间件
- API密钥加密存储

## 日志配置

- 日志文件: `app/logs/django.log`
- 同时输出到控制台和文件
- 支持应用级别的日志记录