# 项目结构和组织

## 根目录结构

```
agent-portal/
├── app/                    # Django应用主目录
├── docker/                 # Docker配置文件
├── docs/                   # 项目文档
├── scripts/                # 辅助脚本
├── tests/                  # 测试文件
├── third/                  # 第三方依赖
├── PRD.md                  # 产品需求文档
└── CHANGELOG.md            # 变更日志
```

## Django应用结构

```
app/
├── agent_portal/           # Django项目配置
│   ├── settings.py         # 项目设置
│   ├── urls.py            # 主URL配置
│   ├── wsgi.py            # WSGI配置
│   └── asgi.py            # ASGI配置
├── accounts/              # 用户账户应用
├── agents/                # 智能体管理应用
├── conversations/         # 对话管理应用
├── core/                  # 核心功能应用
├── knowledge_bases/       # 知识库应用
├── search/                # 搜索功能应用
├── templates/             # HTML模板
├── static/                # 静态文件
├── logs/                  # 日志文件
├── manage.py              # Django管理脚本
└── requirements.txt       # Python依赖
```

## Django应用职责

### accounts/ - 用户系统
- 用户模型和认证
- 用户注册、登录、权限管理
- 用户配置和设置

### agents/ - 智能体管理
- 智能体模型和配置
- 多平台智能体集成
- 智能体状态监控和统计

### conversations/ - 对话管理
- 对话和消息模型
- 对话历史记录
- 消息的增删改查

### core/ - 核心功能
- 系统设置和配置
- 公共中间件
- LLM服务集成
- 前端页面视图

### knowledge_bases/ - 知识库
- RagFlow集成
- 知识库认证和配置
- 知识库搜索功能

### search/ - 搜索功能
- 搜索模型和索引
- 搜索API和界面
- 搜索结果处理

## 模型设计模式

### 通用字段模式
- 使用UUID作为主键
- created_at/updated_at时间戳
- 软删除支持（部分模型）

### 关联关系模式
- User -> Agent (一对多)
- User -> Conversation (一对多)
- Agent -> Conversation (一对多)
- Conversation -> Message (一对多)

## 文件命名约定

### Python文件
- 模型: `models.py`
- 视图: `views.py`
- 序列化器: `serializers.py`
- URL配置: `urls.py`
- 管理后台: `admin.py`
- 应用配置: `apps.py`
- 测试: `tests.py`

### 模板文件
- 基础模板: `base.html`
- 页面模板: `页面名.html`
- 组件模板: `_组件名.html`

### 静态文件
- CSS: `static/css/`
- JavaScript: `static/js/`
- 图片: `static/img/`
- 字体: `static/fonts/`

## API设计模式

### URL模式
- `/api/auth/` - 认证相关API
- `/api/agents/` - 智能体API
- `/api/conversations/` - 对话API
- `/api/knowledge-bases/` - 知识库API

### 响应格式
```json
{
    "success": true,
    "data": {},
    "message": "操作成功",
    "code": 200
}
```

## 配置管理

### 环境变量
- `DEBUG` - 调试模式
- `SECRET_KEY` - Django密钥
- `ALLOWED_HOSTS` - 允许的主机
- `REDIS_URL` - Redis连接URL

### 配置文件
- 开发环境: 使用默认配置
- 生产环境: 通过环境变量覆盖
- Docker环境: 通过docker-compose.yml配置

## 部署结构

### Docker部署
- `docker/Dockerfile` - 应用镜像
- `docker/docker-compose.yml` - 服务编排
- `docker/.env.example` - 环境变量模板

### 静态文件处理
- 开发: Django直接服务
- 生产: collectstatic + whitenoise
- CDN: 可选的静态文件CDN