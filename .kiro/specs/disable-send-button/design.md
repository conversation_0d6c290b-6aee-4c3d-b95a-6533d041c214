# Design Document

## Overview

This design implements a send button disable mechanism for the Agent Portal chat interface. The solution leverages the existing `isTyping` state variable and enhances the UI to provide clear visual feedback when the agent is responding. The design ensures consistent behavior across both agent chat mode and direct LLM model mode.

## Architecture

### State Management
The solution builds upon the existing `isTyping` boolean variable that tracks when an agent is actively responding. This variable is already set to `true` when a message is sent and reset to `false` when the response completes or encounters an error.

### UI Components
- **Send Button**: The primary button that will be disabled/enabled based on agent response state
- **Input Field**: Keyboard event handling will be modified to respect the typing state
- **Visual Indicators**: CSS classes and transitions for smooth state changes

### Event Flow
1. User sends message → `isTyping = true` → UI updates to disabled state
2. Agent responds (streaming) → UI remains disabled
3. Agent completes/errors → `isTyping = false` → UI returns to enabled state

## Components and Interfaces

### JavaScript Functions

#### `updateSendButtonState()`
```javascript
function updateSendButtonState() {
    const sendButton = document.querySelector('.send-btn');
    const messageInput = document.getElementById('messageInput');
    
    if (isTyping) {
        // Disable send button
        sendButton.disabled = true;
        sendButton.classList.add('disabled');
        sendButton.title = 'Agent is responding...';
        
        // Update button content to show waiting state
        sendButton.innerHTML = '<i class="bi bi-hourglass-split"></i>';
    } else {
        // Enable send button
        sendButton.disabled = false;
        sendButton.classList.remove('disabled');
        sendButton.title = 'Send message (Enter)';
        
        // Restore original button content
        sendButton.innerHTML = '<i class="bi bi-send"></i>';
    }
}
```

#### Enhanced `sendMessage()` Function
The existing `sendMessage()` function will be modified to call `updateSendButtonState()` when setting `isTyping = true`.

#### Enhanced Response Handlers
Both `sendMessageViaAPI()` and `sendMessageToModel()` functions will call `updateSendButtonState()` when setting `isTyping = false`.

### CSS Styles

#### Disabled Button Styles
```css
.send-btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--antdx-text-secondary) !important;
    border-color: var(--antdx-text-secondary) !important;
    transition: all 0.2s ease;
}

.send-btn.disabled:hover {
    background: var(--antdx-text-secondary) !important;
    transform: none;
    box-shadow: none;
}

.send-btn:disabled {
    pointer-events: none;
}
```

#### Transition Effects
```css
.send-btn {
    transition: all 0.2s ease;
}

.send-btn i {
    transition: all 0.2s ease;
}
```

### Event Handling

#### Enhanced Keyboard Event Handler
The existing keyboard event listener will be modified to check `isTyping` state:

```javascript
document.getElementById('messageInput').addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
        if (e.shiftKey) {
            // Shift+Enter: allow line break
            return;
        } else {
            // Single Enter: send message only if not typing
            e.preventDefault();
            if (!isTyping) {
                sendMessage();
            }
        }
    }
});
```

## Data Models

### State Variables
- `isTyping` (boolean): Existing variable that tracks agent response state
- No new data models required

### UI State Properties
- `sendButton.disabled` (boolean): Native HTML disabled property
- `sendButton.classList` (DOMTokenList): CSS classes for visual state
- `sendButton.title` (string): Tooltip text for user feedback

## Error Handling

### Network Errors
All existing error handlers in `sendMessageViaAPI()` and `sendMessageToModel()` will be enhanced to ensure `isTyping` is reset and `updateSendButtonState()` is called.

### SSE Connection Errors
The `eventSource.onerror` handler will be updated to reset the button state:
```javascript
eventSource.onerror = function(event) {
    console.error('SSE连接错误:', event);
    addMessage('assistant', '网络错误，请检查连接后重试。');
    eventSource.close();
    isTyping = false;
    updateSendButtonState(); // Add this line
};
```

### Timeout Handling
If response timeouts are implemented in the future, they will follow the same pattern of resetting `isTyping` and calling `updateSendButtonState()`.

## Testing Strategy

### Unit Testing
- Test `updateSendButtonState()` function with different `isTyping` values
- Verify CSS class application and removal
- Test button disabled/enabled state changes
- Verify tooltip text updates

### Integration Testing
- Test complete message sending flow with button state changes
- Verify keyboard event handling during typing state
- Test error scenarios and state recovery
- Test both agent mode and model mode functionality

### User Interface Testing
- Verify visual feedback is clear and intuitive
- Test responsive behavior on different screen sizes
- Verify smooth CSS transitions
- Test accessibility with screen readers

### Cross-browser Testing
- Test in Chrome, Firefox, Safari, and Edge
- Verify CSS transitions work consistently
- Test keyboard event handling across browsers

## Implementation Notes

### Backward Compatibility
The solution builds upon existing code without breaking changes. The `isTyping` variable is already used throughout the codebase, so this enhancement is purely additive.

### Performance Considerations
- CSS transitions are lightweight and won't impact performance
- `updateSendButtonState()` function is simple DOM manipulation
- No additional network requests or heavy computations

### Mobile Responsiveness
The existing responsive design will be maintained. The disabled button styles will work consistently across desktop and mobile interfaces.

### Accessibility
- Button disabled state is properly communicated to screen readers
- Tooltip text provides clear feedback about system state
- Visual indicators complement the functional disabled state

## Future Enhancements

### Potential Improvements
1. **Progress Indicator**: Add a subtle progress bar or spinner to show response progress
2. **Queue Management**: Allow queuing messages while agent is responding
3. **Typing Indicator**: Show "Agent is typing..." text near the input area
4. **Sound Feedback**: Optional audio cues for state changes

### Extensibility
The design allows for easy extension to support additional UI states or more complex response handling scenarios.